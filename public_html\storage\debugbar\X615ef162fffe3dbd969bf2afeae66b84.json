{"__meta": {"id": "X615ef162fffe3dbd969bf2afeae66b84", "datetime": "2025-06-26 15:59:17", "utime": **********.503442, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.080576, "end": **********.503459, "duration": 0.4228830337524414, "duration_str": "423ms", "measures": [{"label": "Booting", "start": **********.080576, "relative_start": 0, "end": **********.448323, "relative_end": **********.448323, "duration": 0.36774706840515137, "duration_str": "368ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.448333, "relative_start": 0.36775708198547363, "end": **********.503461, "relative_end": 1.9073486328125e-06, "duration": 0.055127859115600586, "duration_str": "55.13ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45660640, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00296, "accumulated_duration_str": "2.96ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.479563, "duration": 0.00196, "duration_str": "1.96ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 66.216}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.490329, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 66.216, "width_percent": 15.878}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.496105, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 82.095, "width_percent": 17.905}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "G4oUC4dvL91DvSdXtTSUa87JqfM4sfNNzjOiK6ns", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-535180166 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-535180166\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-891161443 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-891161443\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1466974639 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G4oUC4dvL91DvSdXtTSUa87JqfM4sfNNzjOiK6ns</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1466974639\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-374099112 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1ap6d1q%7C2%7Cfx3%7C0%7C1998; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clsk=p74v85%7C1750953510329%7C81%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik1LVGxRQ1F1UUd2UmE5cEZPbXZ1dUE9PSIsInZhbHVlIjoiLzF6YS9xVGlVTXl5TVVORllsU2syTUszOTBYZVBVN2NSRFJ6a0dsaXJUWnVOdFFMZzA1U21JbzdwMjg2dkM1VktJYXQvc2R0VGIzaU9QbTllZldwNjZQZFBHcEZjaFFPbWNlTXc1RDljeWFOWWVJTFdENUlxSG92UytKc2x5a2JFS244MExTS1lkbkI5N0tZRTNQMlhHWGpYcHl3V3dzUEJrZEJNNWFGNWtMd0NBY1pIVjA0dXBNNUtjcE1Zdkd3M1VQRmZDOFlKUjJYQkRYTFR0aHRVd3VEeFkyZElVaU5ETXlhRzU0Y1luUmhRb1dBeU9UY3VhWm40ZFFwWWsydnlSdVFOMW51ZWNuelJVblB4QXgySWtRYzlmRml1MWdJeDkvd3ArRy9DUk5zaXBrOFoyTEJ1N2Y5MGdkUU12SCs3NTgvQlNDbE1YbVNSa1lYeGZwYno1M1ducDU1K0dVS05nNjVKS053RlpnZk05RDROL042REpJMTBTc05Pcnlva2h0TFVmdVpJZWZ0bTZibEdaZVk0NE95UGdJdWNld3FpczdLQXpvTm5xUjVDZTlGaDB6TytDbklWeGxkSzZUUU0vbkJwSThqT3lyM09CWU9BbUZTYTc4YkV3clIwMGJNZE45aTlyeUJodzlmSGNnM0hMYWRKQ2p1OW90ZDJuKzMiLCJtYWMiOiI3OTkyOTgzN2RjMTliNmMyM2E2NGQyMWJmNDY4MzhkYzVlN2Y3NTRlOGZkNDNhOGIzM2YzZDg2MjcxZDc1Nzk1IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IklKZnpVUkpJY1R2dTZyNHBocG5WZmc9PSIsInZhbHVlIjoiUmlXdWZBbXZCeUFUY0F6SCsxc3BMYWlMTXRiYTZ4QVkxYXFuS2laQUw2UThsYlBTL0kzUTA5dWtZakJFMFpvM0IybldSbXF5RUxDS0x6aGVmSDJwRlBoWFBHeldEdUZXWXJncnRWVUw2ZTdYTk1jb1FHWEtWOXppdVJwTUppVWhXNldFNCtJeWNnV1VCeExHOHprUTdqNjBLWkp0M2ZteEQ1L3phVFZmL216MEVPYUFJQWRzUzZUQU1ESEZVeWFEUjhBbzRlbjdyRHhXRlZWN2RXaXA1ZU9Ed3FacEhRRWhwQjlOZENVZWRuSnMwWm1DcFNsRTQzK01qRnlvUld0bHM1TWJ1UGRHbnBWVm12bVFuQXI4OC9HaHhTQ2oxNWcxV2swOHdSRjVVMnd1QlVqczJTWUZqdFZwYUlhVnBMUll5RnZVVCtGd1IvcjF4YTdsc1RoMXlBL0I3WGdJRWkzNUhTRnRSWUdZOTZQOSt0WjF6cDlmOHNyOHJJa1ZMQzBXSWp2WFFsMnVBaVFGOW44WGg2eXVHRTlqMy96TGdHWjQ1Zysvdy9BWG9LMHo3SjIvcGpGWW1VQlM0T2VlQmdZK09BZFpTLzdKWUdOT1ZTR1ltN2ZDNEhtVDZaL1JTM0NTUlIwbXBYT0xEV2tNVmZUUG5uUldKUkhPV05ScHphaXYiLCJtYWMiOiJjODJhNDQ0Y2Y1NzQyN2QzNTYyN2U4NjlmOWQ3M2UzODBiYmFiYjAyMGZjYWVhYjI2MzQ5ZjRkYjcyYzhkM2NiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-374099112\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1940836548 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G4oUC4dvL91DvSdXtTSUa87JqfM4sfNNzjOiK6ns</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">16z2m35FIhU8zNuOqkDvqLO3UyIjTT7e4c1Y0Zqf</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1940836548\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-914447635 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 15:59:17 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InFqbzM3bzdYQ3dRNHNsQndIV0tPTUE9PSIsInZhbHVlIjoibkd2dlp6VWV5eU1scGNMb1g4VFkxV2tRVmJPdjVUWGxYbEdQNXZXeUFKQllsOTlmamRwV1pwRHVJZzlIVW1teW54cjlyd0tpOHMweDFKMkl1Ni9TVGppZy9Qc3ArY1BQcm1FcjB6Tkt5U1FHYjl3c2FvcmZEU0VMM1JxV0tUVmdER0hUcDR1c3hhOExqMnR0ckg5c1Mvc0c1aXpVcFY0ZEdTVnNWUkFZUkRGZVg5WTAwa3dxb0xRTFhGUEQ0aVhyR0dVZ2J3ZGd2SUFwUjBNZWRsYW1hUzZoS09BZXlXdEZCaFNmOERlNk1YVFVmTUJSSVVSZGxOSnEzai9oTVpMYmx6WTgycE9XZmt3RDVuWVRaR1VaZ0M5cjI5b1hFdVlyR2hvOHdJc0t1dHYwUTdPeWlIdXBwYjFiejNYN1QrMFJuMjh3blVreHpXUHV2UFVCSm0vSjZRUDg3MWtBbko1VEx1dGRmNko4KytaeXUzRlN3REllckZTWi9aZ1BNMlBndjFaZ29QbEM0b1EyRno2cnZ6cDNUdkxPV3VIaU1HeWRINFNkK3lmMTZRZmh2clljQWh4YW52OUF5aUo5enZzS1Bna1FsVnVwcUZWVUp3dGhROFFqMUNWVzYzU01YYi9aS01uZDR4bUJsR0lBelFxRE52aHJGR0dFRWVXOUdVSkciLCJtYWMiOiI1MGFkYjc1N2Q1YWM4MGE0ODcxODY2Y2M5NjE1YTI1ZmQ1NmYyM2U0MTAxYTMyMzVmNGU1M2JhZDU2NTJlMzI3IiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 17:59:17 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjBudC9BU2YwVUFMTnR0N1lvRGRrWFE9PSIsInZhbHVlIjoiUjdhN3UxS2pZSFZZT1VBb0Y0bHpUN0dJNXlQdEcvYUJiMFBzZ0dNVXkrYWR2TmIwamJrc1RJSVZpd0R1Q2I2YzlhNkRzUmd5dlFjWVRHVGM1b0NNbVFVU0JkNVVVazlZUEJkYXJqaUJWanBDdmhFNFYycUxoRlB5QlNFOXBiS1B0UFJWR3d3b3VqZ0ExRHI0RFR6a0dGdDhsRHBvY1U1dFhTam0xUFNSdVNlM2NtQ2xSZ3FYOTlhOVZxNFFhZWI5RlhSOWFKbk1mQVJGMVVxOCtSbG9wZXJQeGtmbFRhR05tYit5eXJKSDRUWjF1NTVOOVE1WTBxU3dHREplcVQrOUJpT0JxMUtHcnV3cERMT1E5Tm1BRi8zb2VCZHBOajR6a2hZQW0zdUdqb1MzSWxaV09aaURBa2dUQlJ0by9tMVIzNHlKd2hrelFQOG9TK1RMbG5Eci9yTDFuZGRtY1NBNEZGRWxQdFVZbTY1MEtmQ0MwV0FsbmpycGhCZjBLMmlZZWcwSlBBWjJveHJ4NS9yODdtZ0FLNkxuOW9xL1c1dGpmY25VK292SUVBZU4rTzVZdzl4Y0t3YW52STVCYzh2YzM3aUt2OXg4cGJKdVYzQ3pjVzJEenZKbmtvb3JrOERVdU9IdHpmL3UyU0lnNGpTU0VEckhNRy93WitoS2NnNlciLCJtYWMiOiI5YTBkMzFhNjQwYmNhNzU0YjBkZjYyZTg2MTFmN2M2MjYyYmM2NmU3NzA3MjAzN2ViYjk0YjYxNzE0MThiZTBlIiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 17:59:17 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InFqbzM3bzdYQ3dRNHNsQndIV0tPTUE9PSIsInZhbHVlIjoibkd2dlp6VWV5eU1scGNMb1g4VFkxV2tRVmJPdjVUWGxYbEdQNXZXeUFKQllsOTlmamRwV1pwRHVJZzlIVW1teW54cjlyd0tpOHMweDFKMkl1Ni9TVGppZy9Qc3ArY1BQcm1FcjB6Tkt5U1FHYjl3c2FvcmZEU0VMM1JxV0tUVmdER0hUcDR1c3hhOExqMnR0ckg5c1Mvc0c1aXpVcFY0ZEdTVnNWUkFZUkRGZVg5WTAwa3dxb0xRTFhGUEQ0aVhyR0dVZ2J3ZGd2SUFwUjBNZWRsYW1hUzZoS09BZXlXdEZCaFNmOERlNk1YVFVmTUJSSVVSZGxOSnEzai9oTVpMYmx6WTgycE9XZmt3RDVuWVRaR1VaZ0M5cjI5b1hFdVlyR2hvOHdJc0t1dHYwUTdPeWlIdXBwYjFiejNYN1QrMFJuMjh3blVreHpXUHV2UFVCSm0vSjZRUDg3MWtBbko1VEx1dGRmNko4KytaeXUzRlN3REllckZTWi9aZ1BNMlBndjFaZ29QbEM0b1EyRno2cnZ6cDNUdkxPV3VIaU1HeWRINFNkK3lmMTZRZmh2clljQWh4YW52OUF5aUo5enZzS1Bna1FsVnVwcUZWVUp3dGhROFFqMUNWVzYzU01YYi9aS01uZDR4bUJsR0lBelFxRE52aHJGR0dFRWVXOUdVSkciLCJtYWMiOiI1MGFkYjc1N2Q1YWM4MGE0ODcxODY2Y2M5NjE1YTI1ZmQ1NmYyM2U0MTAxYTMyMzVmNGU1M2JhZDU2NTJlMzI3IiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 17:59:17 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjBudC9BU2YwVUFMTnR0N1lvRGRrWFE9PSIsInZhbHVlIjoiUjdhN3UxS2pZSFZZT1VBb0Y0bHpUN0dJNXlQdEcvYUJiMFBzZ0dNVXkrYWR2TmIwamJrc1RJSVZpd0R1Q2I2YzlhNkRzUmd5dlFjWVRHVGM1b0NNbVFVU0JkNVVVazlZUEJkYXJqaUJWanBDdmhFNFYycUxoRlB5QlNFOXBiS1B0UFJWR3d3b3VqZ0ExRHI0RFR6a0dGdDhsRHBvY1U1dFhTam0xUFNSdVNlM2NtQ2xSZ3FYOTlhOVZxNFFhZWI5RlhSOWFKbk1mQVJGMVVxOCtSbG9wZXJQeGtmbFRhR05tYit5eXJKSDRUWjF1NTVOOVE1WTBxU3dHREplcVQrOUJpT0JxMUtHcnV3cERMT1E5Tm1BRi8zb2VCZHBOajR6a2hZQW0zdUdqb1MzSWxaV09aaURBa2dUQlJ0by9tMVIzNHlKd2hrelFQOG9TK1RMbG5Eci9yTDFuZGRtY1NBNEZGRWxQdFVZbTY1MEtmQ0MwV0FsbmpycGhCZjBLMmlZZWcwSlBBWjJveHJ4NS9yODdtZ0FLNkxuOW9xL1c1dGpmY25VK292SUVBZU4rTzVZdzl4Y0t3YW52STVCYzh2YzM3aUt2OXg4cGJKdVYzQ3pjVzJEenZKbmtvb3JrOERVdU9IdHpmL3UyU0lnNGpTU0VEckhNRy93WitoS2NnNlciLCJtYWMiOiI5YTBkMzFhNjQwYmNhNzU0YjBkZjYyZTg2MTFmN2M2MjYyYmM2NmU3NzA3MjAzN2ViYjk0YjYxNzE0MThiZTBlIiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 17:59:17 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-914447635\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1712490367 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G4oUC4dvL91DvSdXtTSUa87JqfM4sfNNzjOiK6ns</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1712490367\", {\"maxDepth\":0})</script>\n"}}