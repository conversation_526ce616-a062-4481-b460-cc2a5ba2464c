{"__meta": {"id": "X32cbded8e7d0f2a6b1e43a664212ec0c", "datetime": "2025-06-26 16:27:58", "utime": **********.199181, "method": "GET", "uri": "/customer/check/delivery?customer_id=6", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750955277.783624, "end": **********.199198, "duration": 0.4155740737915039, "duration_str": "416ms", "measures": [{"label": "Booting", "start": 1750955277.783624, "relative_start": 0, "end": **********.155556, "relative_end": **********.155556, "duration": 0.3719320297241211, "duration_str": "372ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.155564, "relative_start": 0.37194013595581055, "end": **********.199199, "relative_end": 9.5367431640625e-07, "duration": 0.043634891510009766, "duration_str": "43.63ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44489776, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00222, "accumulated_duration_str": "2.22ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 17 limit 1", "type": "query", "params": [], "bindings": ["17"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.187729, "duration": 0.00183, "duration_str": "1.83ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 82.432}, {"sql": "select * from `customers` where `customers`.`id` = '6' limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.1925209, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "kdmkjkqknb", "start_percent": 82.432, "width_percent": 17.568}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PKqZteC3IabV2rdCxsXBMLnTb72fzH4qdR2KdQSn", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "17"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-1934789694 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1934789694\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1781825118 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>6</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1781825118\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-204818829 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-204818829\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1537735657 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PKqZteC3IabV2rdCxsXBMLnTb72fzH4qdR2KdQSn</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1bowxa%7C2%7Cfx3%7C0%7C1999; _clsk=1osc4j2%7C1750953875247%7C2%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InlJU1RHTElaZzBZZFU1QmJUWjdIK1E9PSIsInZhbHVlIjoiMFI5OTdwYkRETUNQNnVFMnFKQm1qRmU3Z0t5N29GZ2ZQUUFQSmg0SWFUYStjaUMyTXdOSWNoZll2Qm4xa1d4dThkdG9XdnV5QnVMQlM5Q0VhQng3NFlYbVN6WENpTGt5RWE4Q3hSWkdMVkNURE5ocE5veWlISTVydU14UkZ3bjVDaURkYzY4ekQ1STlhT0txSGE3eWUzNjhIalJ5NmVpN2VDbG5jemRDbnhVUkdoQkpPTDZXOFY5U2RRSUtkVkV3VkJPQ3NEUXFsQlZlNThZZllNNks5bDEvUkU1TnhrNVlnbWxMcGNHdXhyMng1UEpuRkt1T1k4WjRKeTIzdDVLbHZsaEtjWWdTRXBPYUtWOVErdm80MFJGbkovcXdacVV5WDZGWGRRQWp3SEVNUURHM2xreTVuaXBsa1VQZjFiY0s4a2lwUGdZT29JYmIzbVdJK29SZjVwTHpMSkhJVjRFUkltOVRNOVQyQ29ZMVAyTExLc1A1bnBxbWpOQTEyZVFEU1B2ekd0M2NOM21TVUhqRGRXUjdaanFrTDYyN05tRU02UnJTT0gyZUoyQnBnV0t4aG1vSUpHSVJ1NzhrL0dERzREakNjT0FQRTc4Q1BEcld1YThFR2xxYXNOTElDTGNFSFdMOXhrdVJxZDFFRnJqamQ3UmtYdW9yT2o5UTZJdnYiLCJtYWMiOiI2NTdjNTg5MzNkYmJjYzk4ZDk2OTZhM2FkZDU5OThjZWQyMmVhMmUwYjBlMDcxMzEyMjk2NTJkMWY3MTY1ZWQ0IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImdmQWRUSWkzakpQSVdBZEExcnlYN0E9PSIsInZhbHVlIjoiaFFsTEpKN1E1emhzMGE2b29KazUyZFpmclJ5L09YSFFvaERHNjhjUkdnelF1Y3RsemgwRVBtdVlheEVBSTNrei91VFp2eUxybm9lckhneGxVM09YSTlJNm9DWDFmVi9keXVIWlUweEVjbno3RitEdXFMSytGdGJsaU42Z0k1WGxPNmxiVEZheGhOR244VDJzRjk1Wm5IK2FqTHdsZ2dnN053WkIxSFc2NEVsYm5nT0pJM0I2M0dsd015cjJERERaTXRVQzhMdUlpK1o5d3piQTlNdmU4cWw4OUdEZ1c5cUdlOERud0F6Uk5mOUFWMmtpOXBoMnVsRHJDL2Jaa2NvNnk5QzR2c1RVcnFNR1Z4R2g5TnZTOWhpMXNsc2JremJZcTRIUE5kdlZ1eEdrMDJWUkZwWERTRHRBOUloQW9EbzJSRkJvWjB0M215SjRTRmFrL3JEbW1JWmVPZ0UvNndaVGpoVnBrZ3h2cHMrbEdvVWk1M1FKRGNZUjg2VVVzTUxHZEVVOVlkbitqT0drVytLU1g5a0NhUjVPKzdLL0MxQW1HMkZSOHYvTHRhV2RGOTY4OGlqY3E4WGtsdjVRd0xFZVhOYkJYQTVkZ3BNRDBKMENQUkVyZHg4RUh0em9EK3lJYmdrOVBGTEFtL0U0V2tZT2tZYlRQM2JiTUNKWVU4WHoiLCJtYWMiOiI5MzQ5YTEzMmNiM2ExZTc3MjU2ZjZiYzQwNjViYzQ3YTBmMGM5NzFlMjJiMTk4NTk2YjM3YzE3MDVhNjVlMGUwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1537735657\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-737860987 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PKqZteC3IabV2rdCxsXBMLnTb72fzH4qdR2KdQSn</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GUUMfjgJKgBqP5oaDcRP4Rfu0FM4k6V9zWwhkyoG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-737860987\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2094423039 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 16:27:58 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImtUM1FzTkZDeHN3SE42c29sMXhQVHc9PSIsInZhbHVlIjoiK05pZCt3NWtaa3UzRjVkYlEwcHNmNWlPVHhreHZNdEExRHl5TDFTb09QSU9RVlE5TTF4UXVneU5XeGN3aEpkWHdqNDZwcG5TMWtQYWkvOVc3ZzdlYlpwWEEvN3FDMXpQZ3JlcWpKUDRsQll1M1VnenlsM0kyenNNUXcyWE4zTS80M2cwTUtHRnNMckUvci9NaVhQYzJESkVHVDFjK1lSY0ttK2xIbE1KVmNGQVAwakFKMnd3UzNEbmlOZGdmOWtHSy8wQlNuc1Z2Rks4V0wwWDRqYWduUkpwZ0xsNjVVOXUzWU1qekdiVmdNRzdxWi9aQnNTUnV1SVc1eG1BbDdIbFpMYkFiejF6aXIzTHdtTWJydkhqK3hXcW1yUjNLanEvbGZJS1JGNHUyVVF1Y3dNaUt5RmFSSFV4aU45NmxDVitaZzIzcmtPTzhqMHJobmFFWWs5b2JWTlZ2ZUJidVpyTGhPbVJKTnhyWGxiTTgzVUpERkxIcS9jbWNwb0ZobkNMcWxtVnRFNm1UR0I5eThCQStpNHN4M2JFVUtJMm5wL0NVekpVaE5wdTFxaXo3TTJEeGpTQTg1YTM5Y1B6dUpaNjUvNVZxL1ZFUXYxR2xBOERZYzV2Zm5yUkxGbW43YXA4UXlmOHpSMG14Wkd0V0pUNVQ5a2dKc2VZR2g2MkpoNzgiLCJtYWMiOiIxYTBjYTZlNzIwNzFlN2U5MWNhYzJmN2I4ZjY0NjE4NTJjMTkxMWZlMTU5NWU0NGUzZmJhZjZiZTg0NTc0ZDdhIiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 18:27:58 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImlZcmJkQ0lTbkZMdGVZSU1oRE5HVkE9PSIsInZhbHVlIjoiV20xZzlZN3lHY2JFNTM0TCtiZFhKeDB3VEF4eG0xb2JqYTJ6Sm9RYzNuK2dyK1llUElNNEtZRXpHUk9WQ1RqYjBhZTliZm54UmdMbG83RlJoQ05ENTdOMDI0bDBKM1owK0tyQ09hS1czcS8wNVpZa056VThKV1FzNkgvbEFoRU14L0JNeVIxejJtdi9ZUm8yMjYraVZxQ2dxS1ZxRTFLS0gwQW1EdlJzeXVuQlgxT1hRb3AzNk9uRDFWWmQwUy9idXNsUDNKcUtqNkZ2Tm8xWCtkclI3WXR3cjVWMlhwT2Q3SzFKdkowOEhDdlllbGpwMmQ2eHlySUo3ajYway8rUUUrbS9jVlcvY1AyeE1kZER0bkFlZkhRZGx4MENvVjBIakVVYWRKaGl5TXIrcFowVXhzQ3NzR3pLYy9XTFBzR0dWSWx5WnZYamVka3NaQ29Yc0E1VU9VaVBvK2xqNmRERzZySGluT2RndDFtZDlkanpXM2trTms0RXBheDV2VXoxdUtFT1kva3o5N29NZ3BYZ1MvYlNWZU9tK3NXNzhoSlVZQTBPRklva2gyWXFSZzVUek5RY2FDWEFiWkRocWpuajFSRDhIMmo5MFRleEVad1pKZTNjSjIzZzB0S3R3OHZRK29RdjJDME5WZ0tOcmNTbUs1aHkzM1JiV2xjMlRVaUsiLCJtYWMiOiI1ZDAyMzU0NzliYWFiODkwMTk0ODMxNjI1ZTcwMDY4MGY4MzNmZTI3NjQ4OTdiYTFlNTYzNWIwOGFhZmRkMWRjIiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 18:27:58 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImtUM1FzTkZDeHN3SE42c29sMXhQVHc9PSIsInZhbHVlIjoiK05pZCt3NWtaa3UzRjVkYlEwcHNmNWlPVHhreHZNdEExRHl5TDFTb09QSU9RVlE5TTF4UXVneU5XeGN3aEpkWHdqNDZwcG5TMWtQYWkvOVc3ZzdlYlpwWEEvN3FDMXpQZ3JlcWpKUDRsQll1M1VnenlsM0kyenNNUXcyWE4zTS80M2cwTUtHRnNMckUvci9NaVhQYzJESkVHVDFjK1lSY0ttK2xIbE1KVmNGQVAwakFKMnd3UzNEbmlOZGdmOWtHSy8wQlNuc1Z2Rks4V0wwWDRqYWduUkpwZ0xsNjVVOXUzWU1qekdiVmdNRzdxWi9aQnNTUnV1SVc1eG1BbDdIbFpMYkFiejF6aXIzTHdtTWJydkhqK3hXcW1yUjNLanEvbGZJS1JGNHUyVVF1Y3dNaUt5RmFSSFV4aU45NmxDVitaZzIzcmtPTzhqMHJobmFFWWs5b2JWTlZ2ZUJidVpyTGhPbVJKTnhyWGxiTTgzVUpERkxIcS9jbWNwb0ZobkNMcWxtVnRFNm1UR0I5eThCQStpNHN4M2JFVUtJMm5wL0NVekpVaE5wdTFxaXo3TTJEeGpTQTg1YTM5Y1B6dUpaNjUvNVZxL1ZFUXYxR2xBOERZYzV2Zm5yUkxGbW43YXA4UXlmOHpSMG14Wkd0V0pUNVQ5a2dKc2VZR2g2MkpoNzgiLCJtYWMiOiIxYTBjYTZlNzIwNzFlN2U5MWNhYzJmN2I4ZjY0NjE4NTJjMTkxMWZlMTU5NWU0NGUzZmJhZjZiZTg0NTc0ZDdhIiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 18:27:58 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImlZcmJkQ0lTbkZMdGVZSU1oRE5HVkE9PSIsInZhbHVlIjoiV20xZzlZN3lHY2JFNTM0TCtiZFhKeDB3VEF4eG0xb2JqYTJ6Sm9RYzNuK2dyK1llUElNNEtZRXpHUk9WQ1RqYjBhZTliZm54UmdMbG83RlJoQ05ENTdOMDI0bDBKM1owK0tyQ09hS1czcS8wNVpZa056VThKV1FzNkgvbEFoRU14L0JNeVIxejJtdi9ZUm8yMjYraVZxQ2dxS1ZxRTFLS0gwQW1EdlJzeXVuQlgxT1hRb3AzNk9uRDFWWmQwUy9idXNsUDNKcUtqNkZ2Tm8xWCtkclI3WXR3cjVWMlhwT2Q3SzFKdkowOEhDdlllbGpwMmQ2eHlySUo3ajYway8rUUUrbS9jVlcvY1AyeE1kZER0bkFlZkhRZGx4MENvVjBIakVVYWRKaGl5TXIrcFowVXhzQ3NzR3pLYy9XTFBzR0dWSWx5WnZYamVka3NaQ29Yc0E1VU9VaVBvK2xqNmRERzZySGluT2RndDFtZDlkanpXM2trTms0RXBheDV2VXoxdUtFT1kva3o5N29NZ3BYZ1MvYlNWZU9tK3NXNzhoSlVZQTBPRklva2gyWXFSZzVUek5RY2FDWEFiWkRocWpuajFSRDhIMmo5MFRleEVad1pKZTNjSjIzZzB0S3R3OHZRK29RdjJDME5WZ0tOcmNTbUs1aHkzM1JiV2xjMlRVaUsiLCJtYWMiOiI1ZDAyMzU0NzliYWFiODkwMTk0ODMxNjI1ZTcwMDY4MGY4MzNmZTI3NjQ4OTdiYTFlNTYzNWIwOGFhZmRkMWRjIiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 18:27:58 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2094423039\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PKqZteC3IabV2rdCxsXBMLnTb72fzH4qdR2KdQSn</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>17</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}