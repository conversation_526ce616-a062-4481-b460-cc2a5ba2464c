{"__meta": {"id": "Xafc70b5a8df57e9a4c73494fb35c6c90", "datetime": "2025-06-26 16:00:04", "utime": **********.880505, "method": "GET", "uri": "/users/15/login-with-company", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.467886, "end": **********.880522, "duration": 0.41263604164123535, "duration_str": "413ms", "measures": [{"label": "Booting", "start": **********.467886, "relative_start": 0, "end": **********.791754, "relative_end": **********.791754, "duration": 0.3238680362701416, "duration_str": "324ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.791764, "relative_start": 0.32387804985046387, "end": **********.880525, "relative_end": 3.0994415283203125e-06, "duration": 0.0887610912322998, "duration_str": "88.76ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44380160, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET users/{id}/login-with-company", "middleware": "web, auth", "controller": "App\\Http\\Controllers\\UserController@LoginWithCompany", "namespace": null, "prefix": "", "where": [], "as": "login.with.company", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FHttp%2FControllers%2FUserController.php&line=660\" onclick=\"\">app/Http/Controllers/UserController.php:660-667</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.0019000000000000002, "accumulated_duration_str": "1.9ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.819077, "duration": 0.00161, "duration_str": "1.61ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 84.737}, {"sql": "select * from `users` where `users`.`id` = '15' limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Controllers\\UserController.php", "line": 662}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.823441, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "UserController.php:662", "source": "app/Http/Controllers/UserController.php:662", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FHttp%2FControllers%2FUserController.php&line=662", "ajax": false, "filename": "UserController.php", "line": "662"}, "connection": "kdmkjkqknb", "start_percent": 84.737, "width_percent": 15.263}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "ET1WIQmDwH5DmIalEePkmJkdqregxR405rAMGD3B", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users/15/login-with-company\"\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/users/15/login-with-company", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-74480902 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-74480902\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1842 characters\">_clck=1lqc4d8%7C2%7Cfx3%7C0%7C2003; _clsk=1h4itwx%7C1750953601700%7C4%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImpkTFo3S0JJY1ZLU3FMVmlFK2pBdWc9PSIsInZhbHVlIjoiOXJnWnhUcmVYRkVIZ0F1VWtKYi9uSC9JanBIUjBIcXVOc3ZxR1pFWWZVSWFMazhYaVYwbkpsWlR5NmFNVHBndWs0WU9YWDI5MmlnOXpwQzdmMUhwbE5XQ2R5T1QyOE0zSzZndXFrWTdFaFF5d0dKUkxQOFNYSFgxbkpjdEdjMXVuWXFJOWhpNEdJV1hLUXNYdlF3bTMyZkZ4dkZwNjlCVktFMGozWXMzWjF1MnpOQmZJZmRSMCtQNllwenpYdTNYNktsSEtya3pZV1J6d2ZJMHNvWE1KK096d2FSaVhsR2NMQllpSEhJSFBVOG42czVkM0Q5TWEvNXJCSWxTMjZiMXBuczlZenRUQWpyenNRSFAxL3AwQlVOeHBBQkdKamtpc3FIZGRuRWR4TSsrUjJwUFJTTE9lZWRVMkVQNi91ZDVNSktIb014eWorZU52NmZlV2M3QU91anByVTlVSkN4Mzk0YW4ydmw4TFFiTW9CZ0hZdlJvYWJKQW5jYWNveXFWczZSZVNxRFpBQUpvTEdQdHpxd0Ftckora24yK0YyZUdmOVRuRk9TZ3FYdGhzZHEzdlFnakJ2RHo4L2E2d01ZTVI1dy9uME5lcGtQOUJJQkxXN0FLMS9SRGVFamdlZitSNi8wZHdxZWF4UmRLSkZwWU9qemRLazlxaXBYSXJxT3MiLCJtYWMiOiI4ZWRkNDQwYjBmYjI1MzVjNDJhMTY0Yjg3MjAxNGM5NzY0ZTFmNmJlMTIyNDNkNzVkMDNlYzgxMTM3MDAxMWQ3IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjA1UndvYnhqOWN0Y01nTnlpbS84NlE9PSIsInZhbHVlIjoiRE9tMkEwV1RaeEtndDVTZHppbHVxRWdzWXlWYUlFTWN6RGwvRDhtR3hVblpGR05IYXlpaGp0OUpxNFI1SjlFd3hoZFEvQ24wK0tuZHNxN1hQY1BEZlZKNjdGQzVTZlZjMGY5MDBUemw0TEtXZXpFVVROdW1YQkhxemN2MUxGSE5NV2ZhajV0cU1TSjFoTGFaZWZxYTZrSzFJbXNsQk1HMjVMbXBlMisvQlZVQm0vOVJzS0QrVnZrYm1mTEkrQXdrUWhhZHNNVzNCRUMwbE4wcjRoZmlwaU8xUmpQY29GYmJIbVdnYVhNL3EvaHFYeDFyaG0xSWNNQ0Y5N1UrTVpqNDYvZ2Q5aGk2c0NMU3QyS3M1Sk96d0g5Vk82RXQ5d0orTThkSjRpdU9QUXA1R1FPZkNsQ2gvemZ0YVdTeGJaTUZNNDVYdzREb1paTlZQb1A4V0hSZnFHUzh4MTFjMzB4U3JBUk1ETVo2WUZTN3NxQmJ4MDZQWVU4QytCOU5OZlh3dGVWa2xaR2lNNkZ1Z2tlb3NaNUlRZk9nQWZReXBvS0h0SmVIbzJqQ0M5Ylk4K2IwR08vdGZ5ZjNEZFR0bkU4K1N0Q0RWbXoyS3VaWmJLbEJTM3BUbEEvZXVoTElpQTdocEhtYUNHS1VVNExpeDZRSW5OQ2MvMUE4Zlp0eU5idisiLCJtYWMiOiI4YTVmODBjMTY5M2MxYTk5OWUyYjFkZDJiNjhiZWFiZDQyYTM1MGQyNmM4MWIyMTRkYjEyMzZhMjQyYzkyODY3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-89813771 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ET1WIQmDwH5DmIalEePkmJkdqregxR405rAMGD3B</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OKKbyGk1l1oGFNUe7gxioQDH0z4ATppFVbVTtLek</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-89813771\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-574529495 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 16:00:04 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im5wazdyeVVDN2U4bWMxYTVwV2ZWaUE9PSIsInZhbHVlIjoiTEZUcUhVRC9GWC9sNFJ4NTUxR04rb21meFYrY01wTXo0dDA5WE40aCt6UmRaUC95N3ZnWkdJajBNazlLVG5kanFaZ3Q1S3QyL2tybXB0bDI2TlN4WjJNSjVhWmEyaGp0YjRaK0sxVWlFdW1MWWNYSk9xa3B1T2dOaWEvamRCUVU2Z3YzYWllU2dPZnZXZklKMU55WFpxM3hySi9WLzBSY1JSd0VBdHhOb0NiRk4wVzFmVEtVNDF6RjJKZmhqUlNKVFRkVTlEbmg3MHluS1d2bkFOaVcvVDlNcDhsR000ZVBCY1NiYnpPUkV4ckl1T2lTS1UyTWxSRnRoMHg4L2psMnp2NWNtVjRraGRBR0RBRWlGRE54RTNMK1VpeEtSc2NUdlFLNzltRFZoY1ZTdUo1aURUUWRvMDkxOWlwKzQ5Z1ZFWloxTUFqTXZETGZTc3UvcXdCdWpWMzgyWVZTLzBGczJ5MXdqL1ZTYlp0aUhPTlQxSXlUSHRQT25LcU5WQ2JOQWphckV1NVcxSGxVM3AwM3BRT1FiUnU3TXV4YmxKa3pYUC9CbkpGTVhXVjNtb3JUbTJQUTFTaW94UC9TNFJ6cnlyY1htY2NLSWhLVEs2MmlmS21oR29kNlpvRW5YUzJhZjR2aEpzeDgyT29zTmE3ZjRkN290TmFtRGVwanNCNHMiLCJtYWMiOiI0YWM2MzI2ZDUxNTcwNTEzZjEzN2U1ZTMwMjRiMDUzY2NmMjIwOTZlMjJmMTllMDMxOWVmMWY1MzIzMmNmMTc3IiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 18:00:04 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IklwcG5OZlhuN1E1NmJwRXE4dXJkekE9PSIsInZhbHVlIjoicmViSllna3BuWUVsa2J2WXd6KzJidFhyS2JFNHhxMVp1MDUwZ3N3WlphMkNSR2JLSHp1d1liMFVMVjJiZDZUZnFtOW5hZ1dmbGF0bTdyUkJFLytNQUU2ZnhkRmlYOWdYYWU3QkFSOTZFV2xtbjBhWlp5cnIxaGFXNlZwSVlTOFBZbE55dXpiNXV3V0ZzakJvelM4bUJ6MGJLZVhuY2VibWhLK2trVkRsWlJRY1daN1JXVlVWUU9KSmh1TVRJNjIxbTRvR285eHR2ZEdISFBkRldUTE51eTdSdnlVM1ZWWWVkRTVvQ0FvNzdNVmRGK0x1eW96cDJabnRFV0VBYXJYeXdNWUNGQ25tMGF1ZVEzcEN2K1RLdkdicnVlMFVLcGt6VTNDZ3hWYVd2SFRiVThRZ0VGKzRkNHQ2ZWpmWHc5WU16R1ZXQ1llMjhnZ3o3KzBuc0RHUkNIc0p6VkVjZTh1bjN3OHRUNHlFcGNpeGpIYzJBOVI2TktkT1J0WWRVQk5ZamFJb0laVEtERXl0UlFZV0dpRk1VNXRyR2gzZmNyTmVzMkRCMDVPaGtYUDRVWEdiS29KbjRFY3BzMzR2b013RUpuSG1pWXVUYTZLdCtPY3BxOXQyaXR6UjlmUmpsY2hqZzFNZm9QVEZFT0N0WUpOaVRhUzJkc3VqRjV0RkN3K04iLCJtYWMiOiJlNzg0OTE1ZGNmNWViMzY4ZjliYjU1MjJlNWM3MzhjMDUxOTY0ZThmZTY0ZjQ3NTU4ZDYxMWU1NTExZjQzZGU3IiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 18:00:04 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im5wazdyeVVDN2U4bWMxYTVwV2ZWaUE9PSIsInZhbHVlIjoiTEZUcUhVRC9GWC9sNFJ4NTUxR04rb21meFYrY01wTXo0dDA5WE40aCt6UmRaUC95N3ZnWkdJajBNazlLVG5kanFaZ3Q1S3QyL2tybXB0bDI2TlN4WjJNSjVhWmEyaGp0YjRaK0sxVWlFdW1MWWNYSk9xa3B1T2dOaWEvamRCUVU2Z3YzYWllU2dPZnZXZklKMU55WFpxM3hySi9WLzBSY1JSd0VBdHhOb0NiRk4wVzFmVEtVNDF6RjJKZmhqUlNKVFRkVTlEbmg3MHluS1d2bkFOaVcvVDlNcDhsR000ZVBCY1NiYnpPUkV4ckl1T2lTS1UyTWxSRnRoMHg4L2psMnp2NWNtVjRraGRBR0RBRWlGRE54RTNMK1VpeEtSc2NUdlFLNzltRFZoY1ZTdUo1aURUUWRvMDkxOWlwKzQ5Z1ZFWloxTUFqTXZETGZTc3UvcXdCdWpWMzgyWVZTLzBGczJ5MXdqL1ZTYlp0aUhPTlQxSXlUSHRQT25LcU5WQ2JOQWphckV1NVcxSGxVM3AwM3BRT1FiUnU3TXV4YmxKa3pYUC9CbkpGTVhXVjNtb3JUbTJQUTFTaW94UC9TNFJ6cnlyY1htY2NLSWhLVEs2MmlmS21oR29kNlpvRW5YUzJhZjR2aEpzeDgyT29zTmE3ZjRkN290TmFtRGVwanNCNHMiLCJtYWMiOiI0YWM2MzI2ZDUxNTcwNTEzZjEzN2U1ZTMwMjRiMDUzY2NmMjIwOTZlMjJmMTllMDMxOWVmMWY1MzIzMmNmMTc3IiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 18:00:04 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IklwcG5OZlhuN1E1NmJwRXE4dXJkekE9PSIsInZhbHVlIjoicmViSllna3BuWUVsa2J2WXd6KzJidFhyS2JFNHhxMVp1MDUwZ3N3WlphMkNSR2JLSHp1d1liMFVMVjJiZDZUZnFtOW5hZ1dmbGF0bTdyUkJFLytNQUU2ZnhkRmlYOWdYYWU3QkFSOTZFV2xtbjBhWlp5cnIxaGFXNlZwSVlTOFBZbE55dXpiNXV3V0ZzakJvelM4bUJ6MGJLZVhuY2VibWhLK2trVkRsWlJRY1daN1JXVlVWUU9KSmh1TVRJNjIxbTRvR285eHR2ZEdISFBkRldUTE51eTdSdnlVM1ZWWWVkRTVvQ0FvNzdNVmRGK0x1eW96cDJabnRFV0VBYXJYeXdNWUNGQ25tMGF1ZVEzcEN2K1RLdkdicnVlMFVLcGt6VTNDZ3hWYVd2SFRiVThRZ0VGKzRkNHQ2ZWpmWHc5WU16R1ZXQ1llMjhnZ3o3KzBuc0RHUkNIc0p6VkVjZTh1bjN3OHRUNHlFcGNpeGpIYzJBOVI2TktkT1J0WWRVQk5ZamFJb0laVEtERXl0UlFZV0dpRk1VNXRyR2gzZmNyTmVzMkRCMDVPaGtYUDRVWEdiS29KbjRFY3BzMzR2b013RUpuSG1pWXVUYTZLdCtPY3BxOXQyaXR6UjlmUmpsY2hqZzFNZm9QVEZFT0N0WUpOaVRhUzJkc3VqRjV0RkN3K04iLCJtYWMiOiJlNzg0OTE1ZGNmNWViMzY4ZjliYjU1MjJlNWM3MzhjMDUxOTY0ZThmZTY0ZjQ3NTU4ZDYxMWU1NTExZjQzZGU3IiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 18:00:04 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-574529495\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1366703076 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ET1WIQmDwH5DmIalEePkmJkdqregxR405rAMGD3B</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"44 characters\">http://localhost/users/15/login-with-company</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1366703076\", {\"maxDepth\":0})</script>\n"}}