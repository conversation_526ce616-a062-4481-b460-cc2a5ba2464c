{"__meta": {"id": "X98d0a06e65c220be948609750959212a", "datetime": "2025-06-26 15:59:48", "utime": **********.265385, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.869113, "end": **********.265405, "duration": 0.*****************, "duration_str": "396ms", "measures": [{"label": "Booting", "start": **********.869113, "relative_start": 0, "end": **********.193925, "relative_end": **********.193925, "duration": 0.****************, "duration_str": "325ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.193932, "relative_start": 0.*****************, "end": **********.265407, "relative_end": 2.1457672119140625e-06, "duration": 0.*****************, "duration_str": "71.48ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.016950000000000003, "accumulated_duration_str": "16.95ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.2203138, "duration": 0.015880000000000002, "duration_str": "15.88ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 93.687}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.244807, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 93.687, "width_percent": 1.77}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.2575788, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "kdmkjkqknb", "start_percent": 95.457, "width_percent": 4.543}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "ET1WIQmDwH5DmIalEePkmJkdqregxR405rAMGD3B", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/dashboard\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1842 characters\">_clck=1lqc4d8%7C2%7Cfx3%7C0%7C2003; _clsk=1h4itwx%7C**********305%7C3%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlR1QWZxVkcvcTBSMVp0NTlXckRWYVE9PSIsInZhbHVlIjoiV1oyODE3L0ozNS9Tdno1cWtReEFsWEQxQ1pLY1g2T0p3alpaKy9BNGxlWFMwOU9aeHA0MTBmY3Bmd1N4QjlhcWhDRVVNRStQb1BsNzRIUWh3bW1ib0gxTzJCbXo1RnJab2NOSGFzTEVZTG1FemJYRVVGamVaUU52Q1c2NHBGc3RmNHRRZFBHYVVBaWRCTnpOL3lCUGRFRHo4N2lNb1E4WGZFMk5ZTFlUMElyRXVlWkNqK3BxNTJ2VnpLVkdtSy9nWEtod21IK1JaZzFKQmpzQTd2eXlkaVZUREpkclZ4aDBtRHZMVFhtZ2dnMW0vUzdjeFhCc1lQWXk0MGZKQnZxS2xHVVFFQ0JuazFTTnFwL3A5V0lrenZaL1JtdXAvUkpaaU5UdG80clQwMC9JNGphRjZ3V1hQZTRKcUM5UmFCMTh6NUdGVDdzaUdxWWp4KzFNNE1XVTI3NTAvZUtyNnBQU3JmZHhTRFcwUzlHcWYxV3RiVXpRNTV4N3YwZVd2WnAzcVdOTkpmM2E0MmM2SXlJQ2lPZENpS2t1UXlqOXhOdkdWM25vbXhGTGZkUTVRYyt4TzhVWTlBR2pqL25TeUxvU3BIRU1sZCszMjhNeWZuOWc0TmNrTEhkcEsrcVNHL0xuZVN4WTVUTDVXSGtROUZJZXJBOGc0WU9sOG5aR3hVT1AiLCJtYWMiOiIwZDUwOWIxOTNhMDA0MTM1MmNlYTJhYTBiNzU3YzI1NWU4MTE5MDk2MDJlZmRlYWZjMmIzMzZkNTlhMDFiOGUzIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlFyQUd4bUlvN3VIWlV1MEVMbVAxZUE9PSIsInZhbHVlIjoiRnFQRGxCRjRGUEdiQWRVVFRnZDA0eE8wa095b1NrS0VuZVJCbENpenRTSEdjSUFyZnptb3R5Q3RIVmVyUkpwVWNvQmtrNXlvWEVCenUwT1grWUx2RW1RQXc2TVhvRDNkWWVBRDA0WllzK2JaV3NYVTBSNms4c251dFBDYXNZVkVUR3czdHBwVnBJVVhYUE04cFdiekdFMlkrbS9wZXY5dDJuamlxeHNuUEYzalQ2SFNpbGNncUdWWSt0MnlKWEhIV0VhbjJpOEZmNStQaTdIbTkyZVFVMlhHL1VnT0JiMmdtTXJRandJMlh6WFJSS3NNNmVvK0JLSmhzQUJPM3pPaU13M21JK05HcUVrbTBndWk5TjY1VEJEU3lmemlXdFVGRGtvV0ovU1BaMUcxZE9wSWNJSFVSbDdDOERwRjl1Y3lITWh0VGZ2OERMQ1h1ZmxaMWxRbDVYODVNSERQRDVJUXpPZXNMK0h0RWRaTjRkUUpmckNIbHdueVlrUWczV05wVmlISjc2SnFYSllsejZVS2d1dmdiRE1Uc0EwOXpsTHJiTDRsd0xQbE1WRlVzaUw0T1E0VEc4akk0ZzFXU0wwRHpqRnZsaXpIeE5EcDZXVWFSNnhGdFpGTjh4azE3K2FtUjVUUEl5S2p2Z0lZK3BJb0RNVjZOdUtsZHdXbUN6ODgiLCJtYWMiOiIyNDIyYjVlZmY0OWNlNzMzZjhjMmRmYzEyMzU5NTVkYTdmNTM1NmViYjJjNzM2ZDZhODY2N2ZjZmNjZmM4N2IxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-279304301 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ET1WIQmDwH5DmIalEePkmJkdqregxR405rAMGD3B</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OKKbyGk1l1oGFNUe7gxioQDH0z4ATppFVbVTtLek</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-279304301\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1096206088 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 15:59:48 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImlYOGlZMjEvbnp3Z1NtNWErTGNTZ3c9PSIsInZhbHVlIjoiVFVrM2xXamljdW94YyttWk9xeUk1ZDM3MVFQR3NhSUJCSDM5WllKZjNFVUJ4NElZandHNGdHM2I0QUJ3ZEtDL1RtMFdUTC9HVXhQZmtzTUJrbUlORmErVEdxVzdyMmgzU1lwbzBBZ1dzNjFZUVI4bXdIMzRzVzBVREdzZTlhZ0dYeTZTaXlpN05UdkRZMC91K1dSRkpCaDNMbWRwcUpWdm9nZzA3QUVrYjR2bWxzb1RIRkpYMUlIeVhEcTdrTitsbDV3Vktybmg1dzd0cC83OVFGenNtUloxaFZJcWFzMnhXQ3RnUnhINHBZVldkYmpxU3g2ZGtGQTRORHN5UmZQVHNTeFZGUmY3ZnZXbytzVWl4YmdTSVlEMjR2cENCMUd2V09zajk2WEFDbjljbkVIaGJLNzdieWRkczRiYXRoWk0rMlNZeGp4eUF6Z0kwV3dvYkVFTlBZbXIycDRtcmwxaWErTUN0ZWp0c0lySXFyUzFNN3pPYXdDc1laNTM2WWcwSU5NY3RwcHV5UDNEYlRVMDd6M3hnUEpHWUh6TWs3a2J6elhaYksvcUd6RzFoVmMrVDAySENTcWxJcEVPVXNsNFNSamVuZkJoK3R6T1hZZHYxR2pORjNuWTZrbkFsbkVLTEVIbk5pQUZPaHV0WWtWSmNvV3NWQnJMbVlhOW5zT2UiLCJtYWMiOiI4YzAxNDU5ZWU1M2MxMzljM2ViMDZiYTAxNDRkODA2OTg0YWM3YjhkMDJhN2Q2ZDdhNzU4ODFmMjRmODg0ZmE5IiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 17:59:48 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ikl4bjVQSXJENFZRdGM5bG5wendydkE9PSIsInZhbHVlIjoiN2dhN0JkOFoxdkY5TlFjTzdUY3hqeWlWWG9UcTlRSFp1a3V3OVZtRW1YRFZYR3JzMmoxVjFqZHdJbUI5blRocFl1cXVFU1paLzBaQTFNQ201UDdMY1NRNVZJMFVRYVc0cENYZDY2RHBRQitzREJWRUZyNWRlYS93SWI1TTJRYmtqazVlZHVwNER6clN3OEpJKzdCM1lDRU5WVXhjV1E4R1pUdTg2azI0ZGErTzd0SE5CenJ2SUc1SmsvbUhtdTR5OWFyU28zdUluOFFlNzR0Q3pLaVQrU0ZJeGZXTVlEQVlqY1JJY2gxMDVic0twZWN5WXpYNmpndkduNzNPbjI5Q1Zpd2hKQXc2dUFoenBTbkxic1h0eG5Ub2lUZEU5ZkgxWUNFMVpkUkFmNHcxY29oREZxenF0NXpWOXpTd3NGS2E4MnhMNjd1KzMwK21RbDZLZTllRC9MRkM5UGJFK1JvelJpWjFxOUV5N0dpTE94YXIvcHRQMlljZ2Q4WnA0V1k2cnFsbjNoU05jR2kvV25UZFZDQXQrK0hGWUFMMFc2U2VPZDFlVFJ0VHBIekpqRk1FTGF5OVBqd1pyVkdKbTRobWd3ZU9TMzVvLzFKTE93QnQ0dTZJbG5ySDNleE1UY01yVzFtYU1ZaXBvQjJ5VGJxQjZnb2MzL2JSOXZXRVkwR24iLCJtYWMiOiI4ODI1N2NkMjc3YjdiZmY2N2YzZDljNGIxZTQ1MmExZjM3MWFmMDVjMTM0NDUwNDA3NzI1MGEzYjhiMjc4ZTZiIiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 17:59:48 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImlYOGlZMjEvbnp3Z1NtNWErTGNTZ3c9PSIsInZhbHVlIjoiVFVrM2xXamljdW94YyttWk9xeUk1ZDM3MVFQR3NhSUJCSDM5WllKZjNFVUJ4NElZandHNGdHM2I0QUJ3ZEtDL1RtMFdUTC9HVXhQZmtzTUJrbUlORmErVEdxVzdyMmgzU1lwbzBBZ1dzNjFZUVI4bXdIMzRzVzBVREdzZTlhZ0dYeTZTaXlpN05UdkRZMC91K1dSRkpCaDNMbWRwcUpWdm9nZzA3QUVrYjR2bWxzb1RIRkpYMUlIeVhEcTdrTitsbDV3Vktybmg1dzd0cC83OVFGenNtUloxaFZJcWFzMnhXQ3RnUnhINHBZVldkYmpxU3g2ZGtGQTRORHN5UmZQVHNTeFZGUmY3ZnZXbytzVWl4YmdTSVlEMjR2cENCMUd2V09zajk2WEFDbjljbkVIaGJLNzdieWRkczRiYXRoWk0rMlNZeGp4eUF6Z0kwV3dvYkVFTlBZbXIycDRtcmwxaWErTUN0ZWp0c0lySXFyUzFNN3pPYXdDc1laNTM2WWcwSU5NY3RwcHV5UDNEYlRVMDd6M3hnUEpHWUh6TWs3a2J6elhaYksvcUd6RzFoVmMrVDAySENTcWxJcEVPVXNsNFNSamVuZkJoK3R6T1hZZHYxR2pORjNuWTZrbkFsbkVLTEVIbk5pQUZPaHV0WWtWSmNvV3NWQnJMbVlhOW5zT2UiLCJtYWMiOiI4YzAxNDU5ZWU1M2MxMzljM2ViMDZiYTAxNDRkODA2OTg0YWM3YjhkMDJhN2Q2ZDdhNzU4ODFmMjRmODg0ZmE5IiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 17:59:48 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ikl4bjVQSXJENFZRdGM5bG5wendydkE9PSIsInZhbHVlIjoiN2dhN0JkOFoxdkY5TlFjTzdUY3hqeWlWWG9UcTlRSFp1a3V3OVZtRW1YRFZYR3JzMmoxVjFqZHdJbUI5blRocFl1cXVFU1paLzBaQTFNQ201UDdMY1NRNVZJMFVRYVc0cENYZDY2RHBRQitzREJWRUZyNWRlYS93SWI1TTJRYmtqazVlZHVwNER6clN3OEpJKzdCM1lDRU5WVXhjV1E4R1pUdTg2azI0ZGErTzd0SE5CenJ2SUc1SmsvbUhtdTR5OWFyU28zdUluOFFlNzR0Q3pLaVQrU0ZJeGZXTVlEQVlqY1JJY2gxMDVic0twZWN5WXpYNmpndkduNzNPbjI5Q1Zpd2hKQXc2dUFoenBTbkxic1h0eG5Ub2lUZEU5ZkgxWUNFMVpkUkFmNHcxY29oREZxenF0NXpWOXpTd3NGS2E4MnhMNjd1KzMwK21RbDZLZTllRC9MRkM5UGJFK1JvelJpWjFxOUV5N0dpTE94YXIvcHRQMlljZ2Q4WnA0V1k2cnFsbjNoU05jR2kvV25UZFZDQXQrK0hGWUFMMFc2U2VPZDFlVFJ0VHBIekpqRk1FTGF5OVBqd1pyVkdKbTRobWd3ZU9TMzVvLzFKTE93QnQ0dTZJbG5ySDNleE1UY01yVzFtYU1ZaXBvQjJ5VGJxQjZnb2MzL2JSOXZXRVkwR24iLCJtYWMiOiI4ODI1N2NkMjc3YjdiZmY2N2YzZDljNGIxZTQ1MmExZjM3MWFmMDVjMTM0NDUwNDA3NzI1MGEzYjhiMjc4ZTZiIiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 17:59:48 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1096206088\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ET1WIQmDwH5DmIalEePkmJkdqregxR405rAMGD3B</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}