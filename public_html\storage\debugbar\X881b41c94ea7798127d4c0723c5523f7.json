{"__meta": {"id": "X881b41c94ea7798127d4c0723c5523f7", "datetime": "2025-06-26 15:37:29", "utime": **********.305222, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750952248.875352, "end": **********.305235, "duration": 0.4298830032348633, "duration_str": "430ms", "measures": [{"label": "Booting", "start": 1750952248.875352, "relative_start": 0, "end": **********.239996, "relative_end": **********.239996, "duration": 0.36464405059814453, "duration_str": "365ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.240006, "relative_start": 0.3646540641784668, "end": **********.305237, "relative_end": 2.1457672119140625e-06, "duration": 0.0652310848236084, "duration_str": "65.23ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45662576, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.018109999999999998, "accumulated_duration_str": "18.11ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.26646, "duration": 0.01719, "duration_str": "17.19ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 94.92}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.291905, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 94.92, "width_percent": 2.982}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.2981539, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.902, "width_percent": 2.098}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "G4oUC4dvL91DvSdXtTSUa87JqfM4sfNNzjOiK6ns", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/bill/eyJpdiI6InNzWlhKbTYwWndnMXhFSnlrbWhnK0E9PSIsInZhbHVlIjoiaWhTMHpYT3kzU0JqQTdHK01oV1JvUT09IiwibWFjIjoiODg3MTAzOGJiNDk1MzcyODU5MWZmZTNkMTdhYTAzMDJkNmI1NjlmODFkYzk1MzA3NTljODFhMzc1MTkyMzg5YiIsInRhZyI6IiJ9/edit\"\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-68479377 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-68479377\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-278235856 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-278235856\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1336494355 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G4oUC4dvL91DvSdXtTSUa87JqfM4sfNNzjOiK6ns</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1336494355\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-320160828 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"227 characters\">http://localhost/bill/eyJpdiI6InNzWlhKbTYwWndnMXhFSnlrbWhnK0E9PSIsInZhbHVlIjoiaWhTMHpYT3kzU0JqQTdHK01oV1JvUT09IiwibWFjIjoiODg3MTAzOGJiNDk1MzcyODU5MWZmZTNkMTdhYTAzMDJkNmI1NjlmODFkYzk1MzA3NTljODFhMzc1MTkyMzg5YiIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1ap6d1q%7C2%7Cfx3%7C0%7C1998; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clsk=p74v85%7C1750952227381%7C76%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjlkRmRYemZHKzVFRzVlQjlsWWJncVE9PSIsInZhbHVlIjoiTVVlUWpkMEVRTWgwSFViWXhxeXF3SVJxQU1pUm9CZWZkOWJqcnBzdFVBUTVwV3BBeS9RWXYveWtQWlVFQVo3UWZXUXRmRS9pK1JWUXhpOHRhY2tmbHFBT3NVVFNWdU9YbzZnWXhLQjBZSmdyQk8vUURhRTY4QWNldVY4djhJQ2dRTnNnaktkd3RrUXhvOUM2b2d1NjlBQU9HM3M4QTZ6VnNHZkF6SThyWjF6a1EvTjJXd2JWd1hQY3I3bVd6WkQ2bGtLV3UzWkFiczJ1eDJkZ0Z1RXJhNVBySDN4VnlxSGFqOU5VYUJhSnBGbGFPdWFqUVk3TUtROFNEUTdVOHJNWm4vQUpzeVEwYnNQOTFxRzBoQWkzUTR4NGhvVEMrKzB4MTZwMkxYVHQ2Y0E2cVRJTXdVOXN6REQvT00reEo5ZzB4d1d3OVBVYVRBWi9Na0JRREtFRW90RWxKNWJYV3VpR1lyK1BXM1Bnd2JNLzdrSS9qdWprRjZ1WC9OK1V2UmJZSXgxN05XalZWM0JjYUNmeFk4TnNIMHJkaWd2Ri8zc2RwMDhiYVNpZWsrMFBWaHE2dG84THgzRVJMZDJqMFdkMjZSNkxrWVhXZDJPWXovaWhsUmZZMGZ0YWZXSzZab0kwSFYyQlFxaEpiN3lFOXVIQlBSZXdWRzVyR1F1dFZvNngiLCJtYWMiOiI5NzE0MjE3MTRkYmE2MDMzYzViNThlOWQ2YWU4ZGMyYzBmZmU5NzQ0N2M5ZGE5NGQ4YjljMmVmZDllNmUzMzIzIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InhiN3czYjJGbDc0ck9ibnpsOE4yWGc9PSIsInZhbHVlIjoiV3o4SldpNHFvSFZkYXdNZjlSN2NnbTF1eUV2dXdRbldnWVBFY3FVb2RnbCthVkJUd2NCTlViTFVjS25zNlJoNDcvd1ZNT2xvU1JQVms1VzBwcjFkUDJ6dE4zRmNxUVlFT3hUeDdXSDUvVXIzdHZXcTNJUVZscTk1NjkyUUl4bk1CLzNUOS9iVFJMQitYbUdHS1lmakN0Y3k5MzBFcGUvUkFzUGlJQzRONHMvVVZxa2RHVm1tR3JQeFcrQndIM0NHMk1jamxXZE9VWVc4RDFhMTF4eTlkR1lTa1ExMFBteHRGRlV4bkVPMmFGSTRtUUZ0SWlHQUtkbjhzc05URVZocWdmVEdhcVdrQ0p5a24xcjN6Mk1DbWJQMXYrSFRDSFRnR3Zid2pvM09tVGkrQS8rQ1VWbkVOMnBXQ1lhQ0pqbzRudUJlcEJEdzR4RlNnQklrMmxpeWhmWjlmZTBRbEVhZy83RmtCT2dQMHNFclU0RnZVSWFyWDE3bHJvNW5RTVJ1Ty9vd2RGemo1cTk3Y3lDcUZ1YXJMSmppNlhnV2ZXMm9lcExSN3cwd0JqQTFCMWFWb3NmZ3o4ak9zNzBDZ3AwbWpvTm94eUxRL0xEbkJtTXREMW9hTVF1cG9ibGQ3VUhHcXk1VExYL0IzNVFyeGdRQlpzdEE3TWhuNHU0aEpMNysiLCJtYWMiOiI2Y2MwNGI3YjdhZTZkYzU2MTc5OWQxOGE4M2IwOTdiMGQ2NzFiYmNkMTI1NDdhMWUwYzRlZjI3ZGQwYjUyZTcyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-320160828\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-502418810 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G4oUC4dvL91DvSdXtTSUa87JqfM4sfNNzjOiK6ns</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">16z2m35FIhU8zNuOqkDvqLO3UyIjTT7e4c1Y0Zqf</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-502418810\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1675334807 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 15:37:29 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InlOOWU2Z0ljcnIxbEdsR2JKVDF0TGc9PSIsInZhbHVlIjoiL0VqTFl0SXhYNFN5eWdSdldrV1hmUm9ZRGlXVU95OEJCRVpIb1YrZ1kxeVA4VGZ3cXp4aEhNRWVSZmowSnRDblMrdjBvOHVEcW9HcTE0UlFxNHU5RzJwYTZybzNrejlNb09wTnRGaGl6aEgwcHNUVS8vdDBRckhiZnk2RTFiUDZKZDVNdlM2M0lEWVVSTEoyZ2xvVFM4c1c0MXlST2ZBMnJGS2tydEFuUkhjZnNndlBNVFZlMXdDaG1SM3FhdEZDeW41SXNWNWhGdXJ5N0thZ1RxK3lhMzdJUHUvZXpDNFRXQ3FUN0ZLTzFhR1ZlMzV1Z2dWMnlqcFA3N0xVSDZ5QVRnbWdGaldZeGt4cUxrWFdQUmhqR1ByWTlJYVZNWkthQlhQNHhPUG0zTW9PcVpON3RHL2QvVTZTQjZjUUJHeHBYV0NONzZwekRpbkJhbTVPdXhqV0wva3J6TVlsZDg2NDJCT2psVitPbExTa1A4ZUx3ZDZjSWZZNlNURDJIdVA2UngzM2dwMUs1SU1FN0JMYjdwSHp6cnovOTZ2blk2YUlFc3Y5Mlc3Vk4xZ3VxN0RacTZwSGJmSjE4emo4MnhsdmF4Mm1MYkluem5XTG5UMWJwMEQ1eURtVGdrOE0wM0dYREd0QVplcjNxeElFQVhha3lFaExqbE4vdS9qTDJ2eUYiLCJtYWMiOiJmMmM3NWQ3YzkwNWFkMGZhZGQ1MjJlYWExNmFiMTI2ZTYyNzg2NDRjMzAwZGQ2MDg2NGMwMDA2MDM0ZjYwMTI0IiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 17:37:29 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImYrb1J5WXFUb09vVDZtYmRMd1NnckE9PSIsInZhbHVlIjoiQzZnZW1rS3I0SndqTnNRS1c1VXNmUHRSZ21KTStsYVF0RmlvVFdyTUZUZVBtb05HaEVaSXJ4bm5EOWRSQ3ZmRng3czF2d1oyT3gxa3ZMNVFrWEcxanVvUGF2M1hzTWo5MURETXNTTlBCTVlPLzFiN3FKYVE1QmNyU1MyNTR5emtud1ZERmdhNVFKbFFRbnliSnlBNWI4NlRIUmpkeC9LNmxkNEQ3YUk1MCtFaFp4ZVE0TlRPc3ZVNnJPMk55RWpXUW04eVpVOUVTa0thZG5zSHpSTXZCeG02aDA5alJSODdUOHZtYm1UNW1EMXdWdHJyZzNqOU9VcnZCQWJWN3J0Z0hGWWQzb3hzRXppVlRCNExOdGQwUHlldERpZy9KdUQxaitIUnl6b1N5T2RMSjFJWEVtemdMYW5SS1dHWlNGdVMvNHMwenVOeUZnN2pEWFFyZldvWDV2SHhTNyt2dFZyKzI2Wjc4b1AxQVFDWnVwdEpFRHZxaW1vTFY4cVRzdUpXWjhTaE5RMkMwU3RjZ2xiekZib1hCTDBCRWRWZ3FHNFNiS1E3cHdJblBmU1JLYytQV2d4Ri8yV0RPeGtXYzk2WnN1WVJ4bjgzK1hJWFRFcjh4UThvY0FyajkrT0x4QTRqTVFxRHJtT0t3TEhGSm83a05oVkFwczh6UFIrazRRbjQiLCJtYWMiOiI0ODIyZjIwMWQzYzQ5NGRjOGE4MGM0NWRkNjdjZDgyYzMyN2M2ZGNmODI2ODBhNTQyYzQyNjVjMTI2YWUyMjUxIiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 17:37:29 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InlOOWU2Z0ljcnIxbEdsR2JKVDF0TGc9PSIsInZhbHVlIjoiL0VqTFl0SXhYNFN5eWdSdldrV1hmUm9ZRGlXVU95OEJCRVpIb1YrZ1kxeVA4VGZ3cXp4aEhNRWVSZmowSnRDblMrdjBvOHVEcW9HcTE0UlFxNHU5RzJwYTZybzNrejlNb09wTnRGaGl6aEgwcHNUVS8vdDBRckhiZnk2RTFiUDZKZDVNdlM2M0lEWVVSTEoyZ2xvVFM4c1c0MXlST2ZBMnJGS2tydEFuUkhjZnNndlBNVFZlMXdDaG1SM3FhdEZDeW41SXNWNWhGdXJ5N0thZ1RxK3lhMzdJUHUvZXpDNFRXQ3FUN0ZLTzFhR1ZlMzV1Z2dWMnlqcFA3N0xVSDZ5QVRnbWdGaldZeGt4cUxrWFdQUmhqR1ByWTlJYVZNWkthQlhQNHhPUG0zTW9PcVpON3RHL2QvVTZTQjZjUUJHeHBYV0NONzZwekRpbkJhbTVPdXhqV0wva3J6TVlsZDg2NDJCT2psVitPbExTa1A4ZUx3ZDZjSWZZNlNURDJIdVA2UngzM2dwMUs1SU1FN0JMYjdwSHp6cnovOTZ2blk2YUlFc3Y5Mlc3Vk4xZ3VxN0RacTZwSGJmSjE4emo4MnhsdmF4Mm1MYkluem5XTG5UMWJwMEQ1eURtVGdrOE0wM0dYREd0QVplcjNxeElFQVhha3lFaExqbE4vdS9qTDJ2eUYiLCJtYWMiOiJmMmM3NWQ3YzkwNWFkMGZhZGQ1MjJlYWExNmFiMTI2ZTYyNzg2NDRjMzAwZGQ2MDg2NGMwMDA2MDM0ZjYwMTI0IiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 17:37:29 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImYrb1J5WXFUb09vVDZtYmRMd1NnckE9PSIsInZhbHVlIjoiQzZnZW1rS3I0SndqTnNRS1c1VXNmUHRSZ21KTStsYVF0RmlvVFdyTUZUZVBtb05HaEVaSXJ4bm5EOWRSQ3ZmRng3czF2d1oyT3gxa3ZMNVFrWEcxanVvUGF2M1hzTWo5MURETXNTTlBCTVlPLzFiN3FKYVE1QmNyU1MyNTR5emtud1ZERmdhNVFKbFFRbnliSnlBNWI4NlRIUmpkeC9LNmxkNEQ3YUk1MCtFaFp4ZVE0TlRPc3ZVNnJPMk55RWpXUW04eVpVOUVTa0thZG5zSHpSTXZCeG02aDA5alJSODdUOHZtYm1UNW1EMXdWdHJyZzNqOU9VcnZCQWJWN3J0Z0hGWWQzb3hzRXppVlRCNExOdGQwUHlldERpZy9KdUQxaitIUnl6b1N5T2RMSjFJWEVtemdMYW5SS1dHWlNGdVMvNHMwenVOeUZnN2pEWFFyZldvWDV2SHhTNyt2dFZyKzI2Wjc4b1AxQVFDWnVwdEpFRHZxaW1vTFY4cVRzdUpXWjhTaE5RMkMwU3RjZ2xiekZib1hCTDBCRWRWZ3FHNFNiS1E3cHdJblBmU1JLYytQV2d4Ri8yV0RPeGtXYzk2WnN1WVJ4bjgzK1hJWFRFcjh4UThvY0FyajkrT0x4QTRqTVFxRHJtT0t3TEhGSm83a05oVkFwczh6UFIrazRRbjQiLCJtYWMiOiI0ODIyZjIwMWQzYzQ5NGRjOGE4MGM0NWRkNjdjZDgyYzMyN2M2ZGNmODI2ODBhNTQyYzQyNjVjMTI2YWUyMjUxIiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 17:37:29 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1675334807\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1379790455 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G4oUC4dvL91DvSdXtTSUa87JqfM4sfNNzjOiK6ns</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"227 characters\">http://localhost/bill/eyJpdiI6InNzWlhKbTYwWndnMXhFSnlrbWhnK0E9PSIsInZhbHVlIjoiaWhTMHpYT3kzU0JqQTdHK01oV1JvUT09IiwibWFjIjoiODg3MTAzOGJiNDk1MzcyODU5MWZmZTNkMTdhYTAzMDJkNmI1NjlmODFkYzk1MzA3NTljODFhMzc1MTkyMzg5YiIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1379790455\", {\"maxDepth\":0})</script>\n"}}