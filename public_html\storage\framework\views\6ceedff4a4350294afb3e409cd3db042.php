<?php
    $totalPrice = $sales['total'];
?>
<div class="p-4">
    <h2><?php echo e(__('Select Payment Type')); ?></h2>
    <form id="paymentForm" action="#" method="post" class="mt-3">
        <?php echo csrf_field(); ?>
        <input type="hidden" name="payment_type" id="hiddenPaymentType">

        <div class="mb-3">
            <label class="form-label"><?php echo e(__('Payment Type')); ?>:</label> <span class="text-danger">*</span>
            <div class="btn-group" role="group">
                <input type="radio" class="btn-check" name="select_payment" id="cash" value="cash" required>
                <label class="btn btn-outline-primary" for="cash">💵 <?php echo e(__('Cash')); ?></label>

                <input type="radio" class="btn-check" name="select_payment" id="network" value="network">
                <label class="btn btn-outline-success" for="network">💳 <?php echo e(__('Network')); ?></label>

                <input type="radio" class="btn-check" name="select_payment" id="split" value="split">
                <label class="btn btn-outline-warning" for="split">💰💳 <?php echo e(__('Split Payment')); ?></label>
            </div>
            <div id="payment-error" class="text-danger mt-2 d-none"><?php echo e(__('Please select a payment type')); ?></div>
        </div>

        <div id="cashFields" class="d-none">
            <label class="form-label"><?php echo e(__('Amount Received')); ?>:</label>
            <input type="number" step="0.01" class="form-control" id="amountReceived"
                placeholder="Enter amount received">
            <label class="form-label mt-2"><?php echo e(__('Change')); ?>:</label>
            <input type="text" class="form-control" id="change" readonly>
        </div>

        <div id="networkFields" class="d-none">
            <label class="form-label"><?php echo e(__('Transaction Number')); ?>:</label>
            <input type="text" class="form-control" name="transaction_number" id="transactionNumber"
                placeholder="Enter transaction ID">
        </div>

        <div id="splitFields" class="d-none">
            <div class="row">
                <div class="col-md-6">
                    <label class="form-label"><?php echo e(__('Cash Amount')); ?>:</label>
                    <input type="number" step="0.01" class="form-control" id="splitCashAmount" name="split_cash_amount"
                        placeholder="Enter cash amount">
                </div>
                <div class="col-md-6">
                    <label class="form-label"><?php echo e(__('Network Amount')); ?>:</label>
                    <input type="number" step="0.01" class="form-control" id="splitNetworkAmount" name="split_network_amount"
                        placeholder="Enter network amount">
                </div>
            </div>
            <div class="mt-2">
                <label class="form-label"><?php echo e(__('Transaction Number')); ?>:</label>
                <input type="text" class="form-control" name="split_transaction_number" id="splitTransactionNumber"
                    placeholder="Enter transaction ID for network payment">
            </div>
            <div id="splitAmountError" class="text-danger mt-2 d-none"><?php echo e(__('Total split amount must equal the total price')); ?></div>
        </div>

        <label class="form-label mt-3"><?php echo e(__('Total Price')); ?>:</label>
        <input type="number" class="form-control" name="total_price" id="totalPrice" value="<?php echo e(number_format($totalPrice, 2, '.', '')); ?>"
            readonly required step="0.01">

        <div class="mt-3">
            <button type="submit" class="btn btn-primary" data-fmodel="true" data-url="<?php echo e(route('pos.pos-payment-type')); ?>"><?php echo e(__('Submit')); ?></button>
        </div>
    </form>

    <script>
        $(document).ready(function() {
            let totalPrice = parseFloat($('#totalPrice').val()).toFixed(2) || 0;

            $('input[name="select_payment"]').change(function() {
                let selectedType = $(this).val();
                $('#hiddenPaymentType').val(selectedType);

                // إخفاء جميع الحقول أولاً
                $('#cashFields, #networkFields, #splitFields').addClass('d-none');
                $('#transactionNumber, #amountReceived, #splitCashAmount, #splitNetworkAmount, #splitTransactionNumber').prop('disabled', true);

                // إظهار الحقول المناسبة بناءً على نوع الدفع المحدد
                if (selectedType === 'cash') {
                    $('#cashFields').removeClass('d-none');
                    $('#amountReceived').prop('disabled', false);
                } else if (selectedType === 'network') {
                    $('#networkFields').removeClass('d-none');
                    $('#transactionNumber').prop('disabled', false);
                } else if (selectedType === 'split') {
                    $('#splitFields').removeClass('d-none');
                    $('#splitCashAmount, #splitNetworkAmount, #splitTransactionNumber').prop('disabled', false);

                    // تحديث مجموع المبالغ عند تغيير قيم الدفع المجزأ
                    updateSplitTotal();
                }
            });

            // دالة لتحديث مجموع المبالغ في الدفع المجزأ
            function updateSplitTotal() {
                let totalPrice = parseFloat($('#totalPrice').val()) || 0;
                let cashAmount = parseFloat($('#splitCashAmount').val()) || 0;
                let networkAmount = parseFloat($('#splitNetworkAmount').val()) || 0;
                let totalSplit = cashAmount + networkAmount;

                // التحقق من أن المجموع يساوي السعر الإجمالي
                if (Math.abs(totalSplit - totalPrice) < 0.01) {
                    $('#splitAmountError').addClass('d-none');
                } else {
                    $('#splitAmountError').removeClass('d-none');
                }
            }

            // تحديث المجموع عند تغيير قيم الدفع المجزأ
            $('#splitCashAmount, #splitNetworkAmount').on('input', function() {
                updateSplitTotal();
            });

            $('#amountReceived').on('input', function() {
                let received = parseFloat($(this).val()) || 0;
                let change = Math.max(0, received - totalPrice);
                $('#change').val(change.toFixed(2));
            });

            $('#paymentForm').submit(function(e) {
                let selectedType = $('#hiddenPaymentType').val();
                if (!selectedType) {
                    e.preventDefault();
                    $('#payment-error').removeClass('d-none');
                    return false;
                } else {
                    $('#payment-error').addClass('d-none');
                }
            });

            $('button[data-fmodel="true"]').click(function(e) {
                e.preventDefault();

                // التحقق من اختيار طريقة الدفع
                let selectedType = $('#hiddenPaymentType').val();
                if (!selectedType) {
                    $('#payment-error').removeClass('d-none');
                    // عرض رسالة خطأ للمستخدم
                    show_toastr('Error', '<?php echo e(__("Please select a payment type")); ?>', 'error');
                    return false;
                } else {
                    $('#payment-error').addClass('d-none');
                }

                // التحقق من إدخال رقم المعاملة إذا كانت الدفع بالشبكة
                if (selectedType === 'network') {
                    let transactionNumber = $('#transactionNumber').val();
                    if (!transactionNumber) {
                        show_toastr('Error', '<?php echo e(__("Please enter transaction number")); ?>', 'error');
                        return false;
                    }
                }

                // التحقق من صحة بيانات الدفع المجزأ
                if (selectedType === 'split') {
                    let totalPrice = parseFloat($('#totalPrice').val()) || 0;
                    let cashAmount = parseFloat($('#splitCashAmount').val()) || 0;
                    let networkAmount = parseFloat($('#splitNetworkAmount').val()) || 0;
                    let transactionNumber = $('#splitTransactionNumber').val();
                    let totalSplit = cashAmount + networkAmount;

                    // التحقق من إدخال المبالغ
                    if (cashAmount <= 0 || isNaN(cashAmount)) {
                        show_toastr('Error', '<?php echo e(__("Please enter a valid cash amount")); ?>', 'error');
                        return false;
                    }

                    if (networkAmount <= 0 || isNaN(networkAmount)) {
                        show_toastr('Error', '<?php echo e(__("Please enter a valid network amount")); ?>', 'error');
                        return false;
                    }

                    // التحقق من إدخال رقم المعاملة للدفع بالشبكة
                    if (!transactionNumber) {
                        show_toastr('Error', '<?php echo e(__("Please enter transaction number for network payment")); ?>', 'error');
                        return false;
                    }

                    // التحقق من أن مجموع المبالغ يساوي السعر الإجمالي
                    if (Math.abs(totalSplit - totalPrice) > 0.01) {
                        $('#splitAmountError').removeClass('d-none');
                        show_toastr('Error', '<?php echo e(__("Total split amount must equal the total price")); ?>', 'error');
                        return false;
                    } else {
                        $('#splitAmountError').addClass('d-none');
                    }
                }

                console.log("URL:", $(this).data("url"));
                var data = {};
                var title1 = $(this).data("title");

                var title2 = $(this).data("bs-original-title");
                var title3 = $(this).data("original-title");
                var title = (title1 != undefined) ? title1 : title2;
                var title = (title != undefined) ? title : title3;

                $('#commonModal .modal-dialog').removeClass(
                'modal-sm modal-md modal-lg modal-xl modal-xxl');
                var size = ($(this).data('size') == '') ? 'md' : $(this).data('size');

                var url = $(this).data('url');
                $("#commonModal .modal-title").html(title);
                $("#commonModal .modal-dialog").addClass('modal-' + size);

                if ($('#vc_name_hidden').length > 0) {
                    data['vc_name'] = $('#vc_name_hidden').val();
                }
                if ($('#warehouse_name_hidden').length > 0) {
                    data['warehouse_name'] = $('#warehouse_name_hidden').val();
                }
                if ($('#discount_hidden').length > 0) {
                    data['discount'] = $('#discount_hidden').val();
                }
                if ($('#quotation_id').length > 0) {
                    data['quotation_id'] = $('#quotation_id').val();
                }
                if($('#hiddenPaymentType').length > 0) {
                    data['payment_type'] = $('#hiddenPaymentType').val();
                }
                if(totalPrice) {
                    data['total_price'] = totalPrice;
                }

                // إضافة بيانات حسب نوع الدفع
                if(selectedType === 'network' && $('#transactionNumber').length > 0) {
                    data['transaction_number'] = $('#transactionNumber').val();
                } else if(selectedType === 'split') {
                    data['split_cash_amount'] = $('#splitCashAmount').val();
                    data['split_network_amount'] = $('#splitNetworkAmount').val();
                    data['split_transaction_number'] = $('#splitTransactionNumber').val();
                }

                $.ajax({
                    url: url,
                    type:"POST",
                    data: data,
                    beforeSend: function() {
                        // إظهار مؤشر التحميل
                        $('button[data-fmodel="true"]').prop('disabled', true).html('<span class="spinner-border spinner-border-sm" role="status"></span> <?php echo e(__("Processing Payment...")); ?>');
                    },
                    success: function(response) {
                        // إذا كانت الاستجابة تحتوي على HTML (شاشة النجاح)
                        if (typeof response === 'string' && response.includes('payment_success')) {
                            // عرض شاشة النجاح في المودال
                            $('#commonModal .modal-body').html(response);
                            $('#commonModal .modal-title').text('<?php echo e(__("Payment Completed Successfully")); ?>');
                            $('#commonModal .modal-dialog').removeClass('modal-sm modal-md modal-lg').addClass('modal-xl');
                            $("#commonModal").modal('show');
                        } else {
                            // معالجة الاستجابات الأخرى
                            $('#commonModal .modal-body').html(response);
                            $("#commonModal").modal('show');
                        }

                        taskCheckbox();
                        common_bind("#commonModal");
                        validation();
                        commonLoader();
                    },
                    error: function(xhr) {
                        var errorData = xhr.responseJSON;
                        show_toastr('Error', errorData.error || '<?php echo e(__("An error occurred during payment processing")); ?>', 'error');

                        // إعادة تفعيل الزر
                        $('button[data-fmodel="true"]').prop('disabled', false).html('<?php echo e(__("Submit")); ?>');
                    }
                });

            });

        });
    </script>
</div>
<?php /**PATH C:\laragon\www\ERPcopy\public_html\resources\views/pos/bill_type.blade.php ENDPATH**/ ?>