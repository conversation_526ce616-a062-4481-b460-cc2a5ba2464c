{"__meta": {"id": "Xc1925dbf36f3e943de9f10b47d915519", "datetime": "2025-06-26 16:05:40", "utime": **********.280079, "method": "GET", "uri": "/add-to-cart/2295/pos", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750953939.807126, "end": **********.280093, "duration": 0.47296690940856934, "duration_str": "473ms", "measures": [{"label": "Booting", "start": 1750953939.807126, "relative_start": 0, "end": **********.162097, "relative_end": **********.162097, "duration": 0.35497093200683594, "duration_str": "355ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.162113, "relative_start": 0.35498690605163574, "end": **********.280095, "relative_end": 2.1457672119140625e-06, "duration": 0.11798214912414551, "duration_str": "118ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 49201544, "peak_usage_str": "47MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET add-to-cart/{id}/{session}", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@addToCart", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1320\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1320-1544</a>"}, "queries": {"nb_statements": 7, "nb_failed_statements": 0, "accumulated_duration": 0.023940000000000003, "accumulated_duration_str": "23.94ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 17 limit 1", "type": "query", "params": [], "bindings": ["17"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.2098591, "duration": 0.018949999999999998, "duration_str": "18.95ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 79.156}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.239847, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 79.156, "width_percent": 2.339}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 17 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["17", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.255118, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 81.495, "width_percent": 3.258}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (17) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.257242, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 84.754, "width_percent": 1.671}, {"sql": "select * from `product_services` where `product_services`.`id` = '2295' limit 1", "type": "query", "params": [], "bindings": ["2295"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1324}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.26266, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1324", "source": "app/Http/Controllers/ProductServiceController.php:1324", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1324", "ajax": false, "filename": "ProductServiceController.php", "line": "1324"}, "connection": "kdmkjkqknb", "start_percent": 86.424, "width_percent": 2.047}, {"sql": "select sum(`quantity`) as aggregate from `warehouse_products` where `product_id` = 2295 and exists (select * from `warehouses` where `warehouse_products`.`warehouse_id` = `warehouses`.`id` and `created_by` = 15)", "type": "query", "params": [], "bindings": ["2295", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/ProductService.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\ProductService.php", "line": 155}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1328}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.267027, "duration": 0.00246, "duration_str": "2.46ms", "memory": 0, "memory_str": null, "filename": "ProductService.php:155", "source": "app/Models/ProductService.php:155", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FProductService.php&line=155", "ajax": false, "filename": "ProductService.php", "line": "155"}, "connection": "kdmkjkqknb", "start_percent": 88.471, "width_percent": 10.276}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 4748}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 4682}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1397}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.270772, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4748", "source": "app/Models/Utility.php:4748", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUtility.php&line=4748", "ajax": false, "filename": "Utility.php", "line": "4748"}, "connection": "kdmkjkqknb", "start_percent": 98.747, "width_percent": 1.253}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 17,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2063044906 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>17</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2063044906\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.261603, "xdebug_link": null}]}, "session": {"_token": "PKqZteC3IabV2rdCxsXBMLnTb72fzH4qdR2KdQSn", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "17", "pos": "array:3 [\n  2295 => array:9 [\n    \"name\" => \"منتوس علكة ابيض نعناع 54جم\"\n    \"quantity\" => 2\n    \"price\" => \"15.00\"\n    \"id\" => \"2295\"\n    \"tax\" => 0\n    \"subtotal\" => 30.0\n    \"originalquantity\" => 5\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  2296 => array:8 [\n    \"name\" => \"بطيخ إضافي 60 ثانية\"\n    \"quantity\" => 1\n    \"price\" => \"18.00\"\n    \"tax\" => 0\n    \"subtotal\" => 18.0\n    \"id\" => \"2296\"\n    \"originalquantity\" => 2\n    \"product_tax\" => \"-\"\n  ]\n  2297 => array:8 [\n    \"name\" => \"ساديا - دجاج مجمّد 800جم\"\n    \"quantity\" => 1\n    \"price\" => \"16.00\"\n    \"tax\" => 0\n    \"subtotal\" => 16.0\n    \"id\" => \"2297\"\n    \"originalquantity\" => 6\n    \"product_tax\" => \"-\"\n  ]\n]"}, "request": {"path_info": "/add-to-cart/2295/pos", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1691518221 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1691518221\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1473506212 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PKqZteC3IabV2rdCxsXBMLnTb72fzH4qdR2KdQSn</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1bowxa%7C2%7Cfx3%7C0%7C1999; _clsk=1osc4j2%7C1750953875247%7C2%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkFzYVNlVGJ3RkpsRU4wWXlScmsvSkE9PSIsInZhbHVlIjoiK2Rjazl4cHZPdU1TQUE5eTh3cVhZYzhRREI1NUJHMW5vR283VXNPTTE2anVFNlJ6Q0VhRWxHZWtGdkFiUm9SQlZiVG5kSjZqb1FGQ1BiRHBqMzVnTlRjZ3ZDUTJPNXlRSlpzcWtlVEYyVVdpRDlzV0I4Uy9KYlJuMFhTN2piTTVOdko1cWtZdUdZN3U3Tk9Ld0JYL3BiSks1UStHQnVId1FuNlJpZjh4NVhub2VQRjg3dG45VTM0dmVNMHkrTERUbW1ETlorWUZwMFZvZlNOMVpOWkF3ZDd5R0dCTkxId3lpckNJOUtHYXErVjJ0MGh0MmFtSDhpSFpVeDRzSHhhQlRKZGVtRHhwVWxvdkRzNEkyTVp0WkZVaUxDMXUvcUI3NnRObEdaWEF3K1VLNVU3RVh1bHFobG5NbHJVMlllVDI5S29oOGdkbnRXZWxOMEVKOVA3aUlmU2ovVlRuTDRmMlFoNDlvYU5yTlBHOXJza1h6eG54QjJrM0t0aXh4dFNwUk1nN0kveWRpVGZld3NZSDZ1cEdjV0hhSjVwY0h3bjlORm91L0c0QnVhTFJwVTJSQ2pMQXpJNUtNUUQvTUk2cXhuQ3FkSG9XOWpHSCsrSGdvYjBrcWZMQnR2dUxwZXpOVFhUeXNtdkI2MkNrUnppWStNdDZIeUEzUE5yZSt6QngiLCJtYWMiOiJlOWVjYjlhYmNjMzA5YjAzYjQxOGQ0OGIwMDYyZTIzZjdjZGVmOTY5YWZmYzE0ODc1ZmM5ZmM0ZTIzZDVkYWY4IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkZIM1hUVzQwK0pJUTlqNkFDQmpESEE9PSIsInZhbHVlIjoiNmRJZHNua25JNURHTW1VYlgxeHF1MS9NWjhpOGlTVWtZNjFDODAzWktmUi9kMXpDK3NLbHEyNU5aaUhhM1FyNkNFMHM1RHhvem5OeC9MSE00dStqMmtkUHY5S2IzNHl3UEN3WVRxTXdKaE4zR2FxbUpJUVVPWHl6RXpvSE9XcE5wangvQmRhZ3BTWmswVGg0MEJDcms1RkE5NkwyaVpJSW1uU0pGSU1ZOGU4SzQwaEdEU3dHTFloTmNoY2tON2dQTWMyRzhOMnNiQXJ1ZlpINGJGWDB2YjAxOFdZRzBGL0ozNUFwSU5XeXlxLzRYNUdiaGZlOVRJWkFFVlIzbFo4WDY5b3pDd0xkcFF6SjhtWEE4bjYwbForaEt5aElxK1ZveTNqcjI3eWlERVRyaVEwS2s3bDVwTGluQ1Z0bTRkZVVrei9LdG92VnF0dDZLQzNYR2NZZEwzWGgyUjc4M2lwTSt5bng0MXVJeFEzU0FyNFI4OFlaQ2lIRWJ5Y0djNFh0SzI4R1RkenF6SlpXekJ2VVpPcUJxaThZRURKNmtxSFJYQUxHenVvQjdzNG1Hc1p1RnlZSk9ZWWMxTElIanp6aG5ZbkJBOTVoNWM1TFl1bTF3SGFIWVFVaHQ1ekFydHlUbEtpWVkzbW90dEMwUXRERTJMTU1qaDhSWFY4Q3hqVXkiLCJtYWMiOiJmMTU4Y2UwZjJmNjIyODY1YWY3NWU0YmJhNjM5Yzc2ZTgyN2M1NDk0NGM3Y2Y5NDA5N2RiOGU4ZGQxZWRmYjIzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1473506212\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1454614839 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PKqZteC3IabV2rdCxsXBMLnTb72fzH4qdR2KdQSn</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GUUMfjgJKgBqP5oaDcRP4Rfu0FM4k6V9zWwhkyoG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1454614839\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2006694724 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 16:05:40 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik83QVBwcVIyaFFJRWFmeFA0b1BKRlE9PSIsInZhbHVlIjoiQlQ4MEJscDUwSkkvWjBNZG5leEJzRC9XVml1WldIdjhyUzBlUXI0ZmFSZFRwWTJudk1nYm00bEpJY202Q1Myb0toWlJDdkh3SGJiVlRaMzY3bWh4U3BzY095TStOK0ZkVklMTTlaWFZpNWgzdHhMUWRDekFPU1JGTk0wNHQxblRQKzh1Z1VPV2l1bTFSWGNSalBPSTUwNFJsa0hsTTFzOUxsU2VqVm9LNVFBaEljekdRcTFGK0ZNQWFQMVJ5aEdXNTFpV2IyZVZCcVJNOEZ0alVlbFlSTkdjZksxc3ZuSDBLZGVEWXhjV0JhZVRpNDVDMVRoTzJtYXU1alFKaDdrWFpTR2lFVk5SNEQ5ekg3ZzluZ2NqTGFmR0pVRXVpMFNnSVZUZ3dTOU1yR2FwTFE4eTFYUm12aTZkM0JYTDZrcCtRU3U5SkVtSldPdDYvV0ZpV3RSNzFLWHlpZzlaRDlmM3V6T3BPWXNabGZjUnVjNTcrNHV0eE8xbWJ1OEtwWTZqUEkyWWxKZ0lsRmx5eEpubHhlbDRFb2VIamNtWGVhTzBjbm9PRERBd0M4S2d3S1NGUDdRK2JGY2E3a3ROeW9wSGFWbFRHM3FJbFRoQUNzaFJWNG1hSmdNZ3dTZDd1bWhyUmxZMHU1a0FkaW0xK21oQUltSEJjUjNLcTJHUGYwYnMiLCJtYWMiOiIxNDJiYTI5NjkwY2Q2MjdjZjdlZDA2ODAzYmJlZjRmZjQwMTE3OGEyMWI3YjU2MGRhYjU4MzVlYjYyYTU1NzI4IiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 18:05:40 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Im9WRTFlQ1psM3JQQUREYUFVMzlxUHc9PSIsInZhbHVlIjoiS3V2YUNBaDlLRkJZcWJzUlc2QTlCVGNHMlp6bTNhRlc4TWMreDlYdk9NSW0wYmQzSVZYYUVHQjBTTElGS1J5M3JCbFhsTUg0TzBqL1dxRm5HZEdnQzBUdEFqQXdTVm80ZVhpdjFRNWpvTVZ1SFpRanBrY0lFRmx0SlhaSTJGTWd2TlJQQ1FpeFlXeW12RXNmbzFaeHJIdmx5c3c1YkxCdjdQdUJweTVYSGROeWhMQXI0VlN5MWxsVGxxTDdhWDVSL3g3cHdiLzB0VHBHMWZkSjJ2SkFiNEdTMEhQT21tdXRTeVEwTnFpSUZEdkl4OWxodjdhSGkzRUxXcHd2V0tLUzFIS1ltMUsyUEVIZHJaYi9KbEhTdnpmNk4yZE5VNHJ1b0Z5TkNnUWZxZE1tcFRSOUJqbnVhWHRwN21TWW1yNnFEWGlVbEsrYVg4amdiMy81a3JPYis0ODcwTEVzOWtnNE15NWZyMFdmR0F2Y0VxSkRiMkVmTmlEL042S1RVV3pRcUx2ck9nQVUxWDBIK0RXbUpieHEwTDdkZGdPK296RXdZTkx0emc1TDVackFkWVhmRExuNTJLOVh4NjFhVkhVaE9QeUlrRHpMK3crMWdpSCt3Nllrd3I4ZXlIWHdBRk0zdU9uS1F1bVk5ZWI3NkE4RmxMOU1UM3NCem5HSjh5bmIiLCJtYWMiOiIxMTIwMzI5NmVjZWRiNWViM2NmZThmOWY0NjViODg5MjNkOTNkZDZhODhlYTY2MTg2OWVmYzVmNjRmZDMzYTAyIiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 18:05:40 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik83QVBwcVIyaFFJRWFmeFA0b1BKRlE9PSIsInZhbHVlIjoiQlQ4MEJscDUwSkkvWjBNZG5leEJzRC9XVml1WldIdjhyUzBlUXI0ZmFSZFRwWTJudk1nYm00bEpJY202Q1Myb0toWlJDdkh3SGJiVlRaMzY3bWh4U3BzY095TStOK0ZkVklMTTlaWFZpNWgzdHhMUWRDekFPU1JGTk0wNHQxblRQKzh1Z1VPV2l1bTFSWGNSalBPSTUwNFJsa0hsTTFzOUxsU2VqVm9LNVFBaEljekdRcTFGK0ZNQWFQMVJ5aEdXNTFpV2IyZVZCcVJNOEZ0alVlbFlSTkdjZksxc3ZuSDBLZGVEWXhjV0JhZVRpNDVDMVRoTzJtYXU1alFKaDdrWFpTR2lFVk5SNEQ5ekg3ZzluZ2NqTGFmR0pVRXVpMFNnSVZUZ3dTOU1yR2FwTFE4eTFYUm12aTZkM0JYTDZrcCtRU3U5SkVtSldPdDYvV0ZpV3RSNzFLWHlpZzlaRDlmM3V6T3BPWXNabGZjUnVjNTcrNHV0eE8xbWJ1OEtwWTZqUEkyWWxKZ0lsRmx5eEpubHhlbDRFb2VIamNtWGVhTzBjbm9PRERBd0M4S2d3S1NGUDdRK2JGY2E3a3ROeW9wSGFWbFRHM3FJbFRoQUNzaFJWNG1hSmdNZ3dTZDd1bWhyUmxZMHU1a0FkaW0xK21oQUltSEJjUjNLcTJHUGYwYnMiLCJtYWMiOiIxNDJiYTI5NjkwY2Q2MjdjZjdlZDA2ODAzYmJlZjRmZjQwMTE3OGEyMWI3YjU2MGRhYjU4MzVlYjYyYTU1NzI4IiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 18:05:40 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Im9WRTFlQ1psM3JQQUREYUFVMzlxUHc9PSIsInZhbHVlIjoiS3V2YUNBaDlLRkJZcWJzUlc2QTlCVGNHMlp6bTNhRlc4TWMreDlYdk9NSW0wYmQzSVZYYUVHQjBTTElGS1J5M3JCbFhsTUg0TzBqL1dxRm5HZEdnQzBUdEFqQXdTVm80ZVhpdjFRNWpvTVZ1SFpRanBrY0lFRmx0SlhaSTJGTWd2TlJQQ1FpeFlXeW12RXNmbzFaeHJIdmx5c3c1YkxCdjdQdUJweTVYSGROeWhMQXI0VlN5MWxsVGxxTDdhWDVSL3g3cHdiLzB0VHBHMWZkSjJ2SkFiNEdTMEhQT21tdXRTeVEwTnFpSUZEdkl4OWxodjdhSGkzRUxXcHd2V0tLUzFIS1ltMUsyUEVIZHJaYi9KbEhTdnpmNk4yZE5VNHJ1b0Z5TkNnUWZxZE1tcFRSOUJqbnVhWHRwN21TWW1yNnFEWGlVbEsrYVg4amdiMy81a3JPYis0ODcwTEVzOWtnNE15NWZyMFdmR0F2Y0VxSkRiMkVmTmlEL042S1RVV3pRcUx2ck9nQVUxWDBIK0RXbUpieHEwTDdkZGdPK296RXdZTkx0emc1TDVackFkWVhmRExuNTJLOVh4NjFhVkhVaE9QeUlrRHpMK3crMWdpSCt3Nllrd3I4ZXlIWHdBRk0zdU9uS1F1bVk5ZWI3NkE4RmxMOU1UM3NCem5HSjh5bmIiLCJtYWMiOiIxMTIwMzI5NmVjZWRiNWViM2NmZThmOWY0NjViODg5MjNkOTNkZDZhODhlYTY2MTg2OWVmYzVmNjRmZDMzYTAyIiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 18:05:40 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2006694724\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1597750422 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PKqZteC3IabV2rdCxsXBMLnTb72fzH4qdR2KdQSn</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>17</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2295</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"26 characters\">&#1605;&#1606;&#1578;&#1608;&#1587; &#1593;&#1604;&#1603;&#1577; &#1575;&#1576;&#1610;&#1590; &#1606;&#1593;&#1606;&#1575;&#1593; 54&#1580;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">15.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2295</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>30.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>5</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>2296</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"19 characters\">&#1576;&#1591;&#1610;&#1582; &#1573;&#1590;&#1575;&#1601;&#1610; 60 &#1579;&#1575;&#1606;&#1610;&#1577;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">18.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>18.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2296</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n    <span class=sf-dump-key>2297</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1587;&#1575;&#1583;&#1610;&#1575; - &#1583;&#1580;&#1575;&#1580; &#1605;&#1580;&#1605;&#1617;&#1583; 800&#1580;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">16.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>16.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2297</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>6</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1597750422\", {\"maxDepth\":0})</script>\n"}}