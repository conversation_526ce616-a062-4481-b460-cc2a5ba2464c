{"__meta": {"id": "Xc6ec9cf47cac1d14c8263fddb9757940", "datetime": "2025-06-26 16:00:01", "utime": **********.337181, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750953600.898409, "end": **********.337195, "duration": 0.43878602981567383, "duration_str": "439ms", "measures": [{"label": "Booting", "start": 1750953600.898409, "relative_start": 0, "end": **********.269871, "relative_end": **********.269871, "duration": 0.3714621067047119, "duration_str": "371ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.269881, "relative_start": 0.3714721202850342, "end": **********.337197, "relative_end": 2.1457672119140625e-06, "duration": 0.06731605529785156, "duration_str": "67.32ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45685968, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.00349, "accumulated_duration_str": "3.49ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.296876, "duration": 0.0020800000000000003, "duration_str": "2.08ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 59.599}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.311955, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 59.599, "width_percent": 14.613}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.324054, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "kdmkjkqknb", "start_percent": 74.212, "width_percent": 14.04}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.3296318, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 88.252, "width_percent": 11.748}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "ET1WIQmDwH5DmIalEePkmJkdqregxR405rAMGD3B", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-295782964 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-295782964\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-657068695 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-657068695\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-468304892 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ET1WIQmDwH5DmIalEePkmJkdqregxR405rAMGD3B</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-468304892\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1474406646 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1842 characters\">_clck=1lqc4d8%7C2%7Cfx3%7C0%7C2003; _clsk=1h4itwx%7C1750953587305%7C3%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ii83TzJOTzVXVk9wblQ5emZYNmJRVkE9PSIsInZhbHVlIjoidVR3STY2RXdKaWIzUC82YUlRWFRROHg2UHgvSHFvMzBtcUpNaFFxUGVBSzRUVkQ1SjdXK283UUNSSk5kd3d6MzdnQVF1MHE1bUZNY09qSm11QStwZ2h5aFdIMDV4T1gzbFdBMjdRSDRmVWRHRmxzaEJTbTQ1d0tsZzdjOXZPeEhsL05aYVFidnIvMmRDcUJPOGtHNmw5V0w5WGJOQmx5VzBBbzdTNUxacDNXZDVZYnUzb2pFY2dSWkE3RDVNYXhSVlRXV3hPNldsSUtpRmJtYnNJMElTVitCRUxNMHVVbFZIQWhhU1ZvZ29qOVlpbGZyRnlQallsMWpQQWVyWFFLSUYvQ1pLWUdEWmRHT0RZYUhHUDA5bGVLaElvR3RuaGlnbjAreHI4aGNiRzdYeXVJTEJFSXFPWUdsS2tPZzc0SmFkUXpiRGtkTmFXU0lJTTNzd3ArOUVKa1Yyb2lzM05KWWlwL0k3TG9Sd1hWYXdqbU0wNHJBREg5VTY4MmZhM3hrdTRMa3IrOC9Oamt5RWx1Qzd1TzNXMHVYTTN6dXFpZDB1eGpPV2NOOVRDZXZaZFFWdXRYWndDVm5wck5MN2NFaHVZbThkUUVKbFBvaVFwOEZNelVVVlczeXlPblpZemwvVnJQRHQ0UWpwMjVFd1BDNjdZOCtRdzVyZjFBM3NYblMiLCJtYWMiOiIwZTZiMjdlMWY2NjUzNjNkYzMxODg1ZTkwZTU2ZWIyYjgwMjVkY2VmNzRjMGU1YWQ4MGEyYjZhMWM3Zjg5MjUwIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImNNczEvVm83VWdPSERHRm4wZ0ljR3c9PSIsInZhbHVlIjoiN011WmFNU2E2Z3ZZS2hUOUhaRkY4TGZqWVRnMVZaSTM0L3lzV2IxUXgvTFN2RXRtQ1o4My9KS0doVStCazZvMCs5Smk0ODNVeFNXZHF2UzNRcVo3YlZ4VkNOSUhRRU1iUWMwcFlkcUZYK2VrQ0lIYnhJSWMxaldOaU5BSEZqQm9NOS9vdHJCSmlYNnBVd2NsY3Z6Sm9PVTl4WTU3aHk4c0x6VFFKR1hneVJhbmJ5alVmL0dhUXNJWWRjdEhNT2FYNGxFTG1jUHRTOTRhL2hCZzFmUmFoeTJZOHJ3OXl3cjFmYm5SZnRKbVlpVW1aV3FSbDdsYmx1VWNVOWRaVlZ2bUcyVUpkSU9OcUZSc1lxaG1IUHlzRjJRTTA2MnJmVUpjQ0xwVXV6TmJxd2pJc1QrUzdIVDhWZHZxM3pvbjdaTVJ5ek9JRXdlbzBVbW1pemJEWmt4TlM1WnZMZy9NdEp4WWczdm02dk5QdTFMaHNtbEh0SWhUNHhjYWpxVi9PclNXOWJSMlFZN3paYjh5dURTUFBDSFBJdThOczdWVUNVZy9oZk9GMlBnT3NVRG5KM3Z6NW50aXNiTGZSSHBnVkthYVMwWW1Takkxb2piZEp2MnRWenUrUHRWa1hpMVBHUzk0blBGbG42TlY2NThNK3dHODVIRzkyem11WnlNU2xDV1MiLCJtYWMiOiJkZWFjZGVlMzMyMTNiN2E1M2Y2N2JmNTg1MjgzZjA5MzY5MTM5MTY2M2I0YjcxYmY3Y2ZkNDk1YTFjNDY2ZmE4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1474406646\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ET1WIQmDwH5DmIalEePkmJkdqregxR405rAMGD3B</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OKKbyGk1l1oGFNUe7gxioQDH0z4ATppFVbVTtLek</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 16:00:01 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkV2YmF6V0hZKys0YzB1UCtVcS82MUE9PSIsInZhbHVlIjoiRSswUDhvYXA2NFdLU28ycXY2c3Nqa2xPYk5KUE9ERGF3TWthL2RyTmhWTkpHUUZWTWdEMi9UYTBPdE5vUXphWnlQUUkzK0dGdFhFaGJZNGczVjhJK2R2bTJYOTlFcjVhelVGTEo3NTJWYnR0aksrMVdlWitEVmNIMkZIYnZVazFZTFNRcHhJdFh0TGZoa1ppenk4cW1iSUxZUUYzVzkwV1JQRi9uOVc3Ty9STUs0S3NXaXVRdkhiZ21mN1Z4M3VsK0srV1BSNG01bjlmTE1mYndKVmErN0Y5emN4cS9KQzlnd1NsWFBXZ3J2ck5ORkc5QTFLK1MwV0RoSi84UmpGeXRZb001WVJZSmllckhFYmg1azliNXNaUndFTDU4VzV4Q3YxZjA4R040YzUyeTgxWjZKbFN6VXl5TXFwWjQ1TnZ0WkZ3VU1GYnZVNHdYSjFQOW9Sb2d2dDhSbExEb0Q1TkpZeEZtOUVoclhNNlYrVnVUZThabzRMdzRQcitITmxZWVpkT0I2UnBwalVMM095cXlLU3NlU2FrTXUrbTJCaDJjdWs3TlpvalRXNWtDMGhSM1FxMkF1K0lKQVVKSkNhc3lMcStEdlBvaWdUUHRhTnYvUXNDenI2KzUzL2pTc1ZFdGpnVmdVanNpWUxNQ2NTRmgwdEdPVFZRQUs0WWFSZ2MiLCJtYWMiOiIyODcyMzZlYmU2NTFjNDUyNjRmZjE5YThjNTU2YzEzM2I1OTU1NjhiMWZlYjQ1Y2EwZTMyMjQxMzliMDU3MjlhIiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 18:00:01 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjRqUFc5RERvbysvVFYxMmVyYXpNM0E9PSIsInZhbHVlIjoicTZCRnkzd1dHV1k4cHYvcXVJejFkNURQd3grZ2RwQWRuVTIwMlpXa01jeVNzbUtSTFlHYUJxNmVVMXpLdVlyWTlZSUdBVEhiTDJmdW9iR0M0UlU0cXN0aWNEUzR4NFVCYzRHU212MnN1ZnZYbEpoOHV2RGlMcG52cGNDZVg3ajBFSkcyWmNha2JQZGJnK3lCSEdnbFNzMGhNYWdKV3VMeUdRaDB2NEx3ZFRsNGFpYTQ4N2ZHdExoRkhnVkkrWjdqNzF2cFo4Q2U1eFZjNEhwZGhnUnJMclBwamlUOFRXVU9QL0toSUw3amJXRkRDcVEvWEVvODVNMWFtbUZhdG0zMm0rU0JPaGtVSFRJRU9Ld3lkdVRsU0JIZ3NyWXl3YWNuQUtJalNvdGN3dkFibFR1cTcxVFRBZy9NUjNDN1FkM1VmQjNLOGxKWEhLdmQ4bTBJalhPUnUxa0p0cHUxN1pRSXBJeFpqVzlGNWY4V0hFTE5KcGR4b0Nobm9KcElTQWdZT1lQMTZRSlRiaUxiY2o1bnNBRkwrVDZIRFBIQ2tXSWl1UVBUMUxtUVpxeUM1NGRBbk5Rc0dwN0Mwak56dlhUcHZ2UzNOaW8vNjFLQ09LS3A1d0tXK0sveWU2b25KZGl3eVhld1l3Tks1THlHNTZFLytqRkE2TXZTTkFsQUdEdWwiLCJtYWMiOiIzZjNjZjliMTI0NzM3MjNhYTllYjM2N2MwNmRiMGUzZjNmYTRhNTQ3ZTE5ODM5MDUyNjliZTA1M2NhMmMxZjExIiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 18:00:01 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkV2YmF6V0hZKys0YzB1UCtVcS82MUE9PSIsInZhbHVlIjoiRSswUDhvYXA2NFdLU28ycXY2c3Nqa2xPYk5KUE9ERGF3TWthL2RyTmhWTkpHUUZWTWdEMi9UYTBPdE5vUXphWnlQUUkzK0dGdFhFaGJZNGczVjhJK2R2bTJYOTlFcjVhelVGTEo3NTJWYnR0aksrMVdlWitEVmNIMkZIYnZVazFZTFNRcHhJdFh0TGZoa1ppenk4cW1iSUxZUUYzVzkwV1JQRi9uOVc3Ty9STUs0S3NXaXVRdkhiZ21mN1Z4M3VsK0srV1BSNG01bjlmTE1mYndKVmErN0Y5emN4cS9KQzlnd1NsWFBXZ3J2ck5ORkc5QTFLK1MwV0RoSi84UmpGeXRZb001WVJZSmllckhFYmg1azliNXNaUndFTDU4VzV4Q3YxZjA4R040YzUyeTgxWjZKbFN6VXl5TXFwWjQ1TnZ0WkZ3VU1GYnZVNHdYSjFQOW9Sb2d2dDhSbExEb0Q1TkpZeEZtOUVoclhNNlYrVnVUZThabzRMdzRQcitITmxZWVpkT0I2UnBwalVMM095cXlLU3NlU2FrTXUrbTJCaDJjdWs3TlpvalRXNWtDMGhSM1FxMkF1K0lKQVVKSkNhc3lMcStEdlBvaWdUUHRhTnYvUXNDenI2KzUzL2pTc1ZFdGpnVmdVanNpWUxNQ2NTRmgwdEdPVFZRQUs0WWFSZ2MiLCJtYWMiOiIyODcyMzZlYmU2NTFjNDUyNjRmZjE5YThjNTU2YzEzM2I1OTU1NjhiMWZlYjQ1Y2EwZTMyMjQxMzliMDU3MjlhIiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 18:00:01 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjRqUFc5RERvbysvVFYxMmVyYXpNM0E9PSIsInZhbHVlIjoicTZCRnkzd1dHV1k4cHYvcXVJejFkNURQd3grZ2RwQWRuVTIwMlpXa01jeVNzbUtSTFlHYUJxNmVVMXpLdVlyWTlZSUdBVEhiTDJmdW9iR0M0UlU0cXN0aWNEUzR4NFVCYzRHU212MnN1ZnZYbEpoOHV2RGlMcG52cGNDZVg3ajBFSkcyWmNha2JQZGJnK3lCSEdnbFNzMGhNYWdKV3VMeUdRaDB2NEx3ZFRsNGFpYTQ4N2ZHdExoRkhnVkkrWjdqNzF2cFo4Q2U1eFZjNEhwZGhnUnJMclBwamlUOFRXVU9QL0toSUw3amJXRkRDcVEvWEVvODVNMWFtbUZhdG0zMm0rU0JPaGtVSFRJRU9Ld3lkdVRsU0JIZ3NyWXl3YWNuQUtJalNvdGN3dkFibFR1cTcxVFRBZy9NUjNDN1FkM1VmQjNLOGxKWEhLdmQ4bTBJalhPUnUxa0p0cHUxN1pRSXBJeFpqVzlGNWY4V0hFTE5KcGR4b0Nobm9KcElTQWdZT1lQMTZRSlRiaUxiY2o1bnNBRkwrVDZIRFBIQ2tXSWl1UVBUMUxtUVpxeUM1NGRBbk5Rc0dwN0Mwak56dlhUcHZ2UzNOaW8vNjFLQ09LS3A1d0tXK0sveWU2b25KZGl3eVhld1l3Tks1THlHNTZFLytqRkE2TXZTTkFsQUdEdWwiLCJtYWMiOiIzZjNjZjliMTI0NzM3MjNhYTllYjM2N2MwNmRiMGUzZjNmYTRhNTQ3ZTE5ODM5MDUyNjliZTA1M2NhMmMxZjExIiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 18:00:01 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-67285263 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ET1WIQmDwH5DmIalEePkmJkdqregxR405rAMGD3B</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-67285263\", {\"maxDepth\":0})</script>\n"}}