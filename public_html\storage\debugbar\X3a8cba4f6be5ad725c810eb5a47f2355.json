{"__meta": {"id": "X3a8cba4f6be5ad725c810eb5a47f2355", "datetime": "2025-06-26 16:27:57", "utime": **********.783417, "method": "GET", "uri": "/customer/check/warehouse?customer_id=6&warehouse_id=9", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.3864, "end": **********.78345, "duration": 0.3970499038696289, "duration_str": "397ms", "measures": [{"label": "Booting", "start": **********.3864, "relative_start": 0, "end": **********.718879, "relative_end": **********.718879, "duration": 0.33247900009155273, "duration_str": "332ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.718887, "relative_start": 0.3324871063232422, "end": **********.783454, "relative_end": 4.0531158447265625e-06, "duration": 0.06456685066223145, "duration_str": "64.57ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45776488, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/warehouse", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\CustomerController@checkWarehouse", "namespace": null, "prefix": "", "where": [], "as": "customer.check.warehouse", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=383\" onclick=\"\">app/Http/Controllers/CustomerController.php:383-397</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.017839999999999998, "accumulated_duration_str": "17.84ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 17 limit 1", "type": "query", "params": [], "bindings": ["17"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.7465842, "duration": 0.01715, "duration_str": "17.15ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 96.132}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.773549, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 96.132, "width_percent": 1.85}, {"sql": "select * from `customers` where `customers`.`id` = '6' limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Controllers\\CustomerController.php", "line": 388}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.776302, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:388", "source": "app/Http/Controllers/CustomerController.php:388", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=388", "ajax": false, "filename": "CustomerController.php", "line": "388"}, "connection": "kdmkjkqknb", "start_percent": 97.982, "width_percent": 2.018}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PKqZteC3IabV2rdCxsXBMLnTb72fzH4qdR2KdQSn", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "17"}, "request": {"path_info": "/customer/check/warehouse", "status_code": "<pre class=sf-dump id=sf-dump-1740466322 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1740466322\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1597792930 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>6</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>9</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1597792930\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1681334180 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1681334180\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-86224726 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PKqZteC3IabV2rdCxsXBMLnTb72fzH4qdR2KdQSn</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1bowxa%7C2%7Cfx3%7C0%7C1999; _clsk=1osc4j2%7C1750953875247%7C2%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InRzZTZGUlpoWGVTM25HOWM0QUNFVkE9PSIsInZhbHVlIjoiTWlTVFhRejdFaWJNWEhaUE9hb1Z6eGhWUWZRbXhHMlI5VEZCc0FUNUR3ZU1TV0s3NThBd3lZcG91N1dnZTNPTGU1REh1dGFDNVV0QUJ5UGFOem5Wb0VHbVFGWlBXWU9LQURWR21CNDFMRUQ5ak1xSmFLL2plbUNaQk9RSlpLYVR1U1ljUXM5MzJ5dUJXdmJvQUZPanVNamtyb0FNQ01WZFpMN3VnSFVsN0ppeE0vRWRhcEdiUEI4U0cvK2tHMGdLUHhQWndTaXQ2UnF2MGdacFF0YnRsU0VWZ1NHSXdWOXR1RWttcXZLTmN6VEhtVGVHeUcvN0xtTnRmUmdoWUxXM0ZCaHRHN3NVVHhja3M2N0EzaVkwcXZxWnBYdlAvNTJ2TUI4ejRpYWxkMUI1VmtpQkQ2MEtwVk1TVm5RNVdWRzI1TTdCaHhoeU5rVkRaRER1ZkRLbFZCOU1BSllMeUozUElUeVNEb1ZsSXBVRGRnQWxhNFZWWDBZNXB6OWRndlZ5TnFlc1g5dndidEJ4N2pCbE1SaEQwRVhQb2twaDVYQTBzUEpxcDNZVTZyZjlWcitXU0VITE9ZMUE0RHlpVmFHTjNQNThPSjFvaVY2cmNUU1FpVmJtdDhkbHFHaFhnYnVsT1pJd2hRaXY2U29haWovL04vbmg3amVUL1ZEWVpZWWwiLCJtYWMiOiIyMTUxM2FkZTk4MTJiZDIwMTAzMzI2YTg0YzFiMmQ5N2FkMThmNmRkMGVjNWVkM2Q1MWI0NjFjZTY4NjZhODNiIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IktqaWlvSWYxL1hmeVk3SktmbGl5VEE9PSIsInZhbHVlIjoiK2QyZ1RVK2E4bUE0K0wrREtHYkpQVkl1a2dzNHpEZ2FFb1pYTTlkTlczOTc1QVVxYlMzbzVCS05tNExWUmdiQnVPU1kvdFZxY2VEVjd2SGJCYnd4WnQ4dGxvUnB0TUJLRnNKRUl6K0tiZmZlT2ZoclV4SWIyMFhsVlZnMWV3dHFwaEd1NnNYL0xVNEN1KzVDRm1xVS9maE9GY2NMYXFEWXJyaHlLMERaOEVQSExNSEo2SjhzZnJXWnNHSTVkc1M1K0duY2JMcEpyRW1KbXdGRWYva2pMMkZocGRiQlVoUTlFOHNCNWNRZS9zbkt1VjV6SFBvQkdQZVZyVGJibUlsYUQzb1lobnhaN05OQnJoSzd6WHJrMHZRQm1qUHUyRzNSYmhMbGhWY3RGSWxlK3JqclVkeTdPYzBJSW52T1hIKytUTGRxaXRHQVdzbCtneU5iMC9QcjFjVkxlak9lZUJnT2tCVllkY3JMVEEvSTF2a0J3emJJUVRrS2JJbmY4aTJiU3pYYmFSZzEvSkhSQ3A2d0E0ZUx3Ri9lc0JZK3Rway9YRzgrUUw2ZDlSSnltcm82V2l6TmRjejUzUlg2Qi82T2d6ejZvRlpSbmhXMzgwV3dZLzdXcmgvaHNEWk1xZEdZc1phaGdleC9UeHNDUDlmTFlvbzNSeTI0OFZzZ3pZMjMiLCJtYWMiOiI4YTYxNTkwMTk2ZjZhNzkwNTI3MGEwZTE2YTkwNDU0MmNiMzNjNzZiMTM5N2ZjMjdiYzA5NDE2NjM4NjYzMGM2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-86224726\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PKqZteC3IabV2rdCxsXBMLnTb72fzH4qdR2KdQSn</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GUUMfjgJKgBqP5oaDcRP4Rfu0FM4k6V9zWwhkyoG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-811386314 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 16:27:57 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InlJU1RHTElaZzBZZFU1QmJUWjdIK1E9PSIsInZhbHVlIjoiMFI5OTdwYkRETUNQNnVFMnFKQm1qRmU3Z0t5N29GZ2ZQUUFQSmg0SWFUYStjaUMyTXdOSWNoZll2Qm4xa1d4dThkdG9XdnV5QnVMQlM5Q0VhQng3NFlYbVN6WENpTGt5RWE4Q3hSWkdMVkNURE5ocE5veWlISTVydU14UkZ3bjVDaURkYzY4ekQ1STlhT0txSGE3eWUzNjhIalJ5NmVpN2VDbG5jemRDbnhVUkdoQkpPTDZXOFY5U2RRSUtkVkV3VkJPQ3NEUXFsQlZlNThZZllNNks5bDEvUkU1TnhrNVlnbWxMcGNHdXhyMng1UEpuRkt1T1k4WjRKeTIzdDVLbHZsaEtjWWdTRXBPYUtWOVErdm80MFJGbkovcXdacVV5WDZGWGRRQWp3SEVNUURHM2xreTVuaXBsa1VQZjFiY0s4a2lwUGdZT29JYmIzbVdJK29SZjVwTHpMSkhJVjRFUkltOVRNOVQyQ29ZMVAyTExLc1A1bnBxbWpOQTEyZVFEU1B2ekd0M2NOM21TVUhqRGRXUjdaanFrTDYyN05tRU02UnJTT0gyZUoyQnBnV0t4aG1vSUpHSVJ1NzhrL0dERzREakNjT0FQRTc4Q1BEcld1YThFR2xxYXNOTElDTGNFSFdMOXhrdVJxZDFFRnJqamQ3UmtYdW9yT2o5UTZJdnYiLCJtYWMiOiI2NTdjNTg5MzNkYmJjYzk4ZDk2OTZhM2FkZDU5OThjZWQyMmVhMmUwYjBlMDcxMzEyMjk2NTJkMWY3MTY1ZWQ0IiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 18:27:57 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImdmQWRUSWkzakpQSVdBZEExcnlYN0E9PSIsInZhbHVlIjoiaFFsTEpKN1E1emhzMGE2b29KazUyZFpmclJ5L09YSFFvaERHNjhjUkdnelF1Y3RsemgwRVBtdVlheEVBSTNrei91VFp2eUxybm9lckhneGxVM09YSTlJNm9DWDFmVi9keXVIWlUweEVjbno3RitEdXFMSytGdGJsaU42Z0k1WGxPNmxiVEZheGhOR244VDJzRjk1Wm5IK2FqTHdsZ2dnN053WkIxSFc2NEVsYm5nT0pJM0I2M0dsd015cjJERERaTXRVQzhMdUlpK1o5d3piQTlNdmU4cWw4OUdEZ1c5cUdlOERud0F6Uk5mOUFWMmtpOXBoMnVsRHJDL2Jaa2NvNnk5QzR2c1RVcnFNR1Z4R2g5TnZTOWhpMXNsc2JremJZcTRIUE5kdlZ1eEdrMDJWUkZwWERTRHRBOUloQW9EbzJSRkJvWjB0M215SjRTRmFrL3JEbW1JWmVPZ0UvNndaVGpoVnBrZ3h2cHMrbEdvVWk1M1FKRGNZUjg2VVVzTUxHZEVVOVlkbitqT0drVytLU1g5a0NhUjVPKzdLL0MxQW1HMkZSOHYvTHRhV2RGOTY4OGlqY3E4WGtsdjVRd0xFZVhOYkJYQTVkZ3BNRDBKMENQUkVyZHg4RUh0em9EK3lJYmdrOVBGTEFtL0U0V2tZT2tZYlRQM2JiTUNKWVU4WHoiLCJtYWMiOiI5MzQ5YTEzMmNiM2ExZTc3MjU2ZjZiYzQwNjViYzQ3YTBmMGM5NzFlMjJiMTk4NTk2YjM3YzE3MDVhNjVlMGUwIiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 18:27:57 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InlJU1RHTElaZzBZZFU1QmJUWjdIK1E9PSIsInZhbHVlIjoiMFI5OTdwYkRETUNQNnVFMnFKQm1qRmU3Z0t5N29GZ2ZQUUFQSmg0SWFUYStjaUMyTXdOSWNoZll2Qm4xa1d4dThkdG9XdnV5QnVMQlM5Q0VhQng3NFlYbVN6WENpTGt5RWE4Q3hSWkdMVkNURE5ocE5veWlISTVydU14UkZ3bjVDaURkYzY4ekQ1STlhT0txSGE3eWUzNjhIalJ5NmVpN2VDbG5jemRDbnhVUkdoQkpPTDZXOFY5U2RRSUtkVkV3VkJPQ3NEUXFsQlZlNThZZllNNks5bDEvUkU1TnhrNVlnbWxMcGNHdXhyMng1UEpuRkt1T1k4WjRKeTIzdDVLbHZsaEtjWWdTRXBPYUtWOVErdm80MFJGbkovcXdacVV5WDZGWGRRQWp3SEVNUURHM2xreTVuaXBsa1VQZjFiY0s4a2lwUGdZT29JYmIzbVdJK29SZjVwTHpMSkhJVjRFUkltOVRNOVQyQ29ZMVAyTExLc1A1bnBxbWpOQTEyZVFEU1B2ekd0M2NOM21TVUhqRGRXUjdaanFrTDYyN05tRU02UnJTT0gyZUoyQnBnV0t4aG1vSUpHSVJ1NzhrL0dERzREakNjT0FQRTc4Q1BEcld1YThFR2xxYXNOTElDTGNFSFdMOXhrdVJxZDFFRnJqamQ3UmtYdW9yT2o5UTZJdnYiLCJtYWMiOiI2NTdjNTg5MzNkYmJjYzk4ZDk2OTZhM2FkZDU5OThjZWQyMmVhMmUwYjBlMDcxMzEyMjk2NTJkMWY3MTY1ZWQ0IiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 18:27:57 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImdmQWRUSWkzakpQSVdBZEExcnlYN0E9PSIsInZhbHVlIjoiaFFsTEpKN1E1emhzMGE2b29KazUyZFpmclJ5L09YSFFvaERHNjhjUkdnelF1Y3RsemgwRVBtdVlheEVBSTNrei91VFp2eUxybm9lckhneGxVM09YSTlJNm9DWDFmVi9keXVIWlUweEVjbno3RitEdXFMSytGdGJsaU42Z0k1WGxPNmxiVEZheGhOR244VDJzRjk1Wm5IK2FqTHdsZ2dnN053WkIxSFc2NEVsYm5nT0pJM0I2M0dsd015cjJERERaTXRVQzhMdUlpK1o5d3piQTlNdmU4cWw4OUdEZ1c5cUdlOERud0F6Uk5mOUFWMmtpOXBoMnVsRHJDL2Jaa2NvNnk5QzR2c1RVcnFNR1Z4R2g5TnZTOWhpMXNsc2JremJZcTRIUE5kdlZ1eEdrMDJWUkZwWERTRHRBOUloQW9EbzJSRkJvWjB0M215SjRTRmFrL3JEbW1JWmVPZ0UvNndaVGpoVnBrZ3h2cHMrbEdvVWk1M1FKRGNZUjg2VVVzTUxHZEVVOVlkbitqT0drVytLU1g5a0NhUjVPKzdLL0MxQW1HMkZSOHYvTHRhV2RGOTY4OGlqY3E4WGtsdjVRd0xFZVhOYkJYQTVkZ3BNRDBKMENQUkVyZHg4RUh0em9EK3lJYmdrOVBGTEFtL0U0V2tZT2tZYlRQM2JiTUNKWVU4WHoiLCJtYWMiOiI5MzQ5YTEzMmNiM2ExZTc3MjU2ZjZiYzQwNjViYzQ3YTBmMGM5NzFlMjJiMTk4NTk2YjM3YzE3MDVhNjVlMGUwIiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 18:27:57 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-811386314\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-344243170 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PKqZteC3IabV2rdCxsXBMLnTb72fzH4qdR2KdQSn</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>17</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-344243170\", {\"maxDepth\":0})</script>\n"}}