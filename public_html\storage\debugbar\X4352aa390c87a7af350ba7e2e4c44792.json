{"__meta": {"id": "X4352aa390c87a7af350ba7e2e4c44792", "datetime": "2025-06-26 16:27:35", "utime": **********.704215, "method": "GET", "uri": "/customer/check/warehouse?customer_id=6&warehouse_id=9", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.260369, "end": **********.704229, "duration": 0.4438600540161133, "duration_str": "444ms", "measures": [{"label": "Booting", "start": **********.260369, "relative_start": 0, "end": **********.630906, "relative_end": **********.630906, "duration": 0.37053704261779785, "duration_str": "371ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.630916, "relative_start": 0.3705470561981201, "end": **********.704231, "relative_end": 1.9073486328125e-06, "duration": 0.07331490516662598, "duration_str": "73.31ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45794976, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/warehouse", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\CustomerController@checkWarehouse", "namespace": null, "prefix": "", "where": [], "as": "customer.check.warehouse", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=383\" onclick=\"\">app/Http/Controllers/CustomerController.php:383-397</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00357, "accumulated_duration_str": "3.57ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 17 limit 1", "type": "query", "params": [], "bindings": ["17"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.681804, "duration": 0.0026, "duration_str": "2.6ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 72.829}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.693912, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 72.829, "width_percent": 14.286}, {"sql": "select * from `customers` where `customers`.`id` = '6' limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Controllers\\CustomerController.php", "line": 388}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.6968172, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:388", "source": "app/Http/Controllers/CustomerController.php:388", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=388", "ajax": false, "filename": "CustomerController.php", "line": "388"}, "connection": "kdmkjkqknb", "start_percent": 87.115, "width_percent": 12.885}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PKqZteC3IabV2rdCxsXBMLnTb72fzH4qdR2KdQSn", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "17", "pos": "array:4 [\n  2295 => array:9 [\n    \"name\" => \"منتوس علكة ابيض نعناع 54جم\"\n    \"quantity\" => 2\n    \"price\" => \"15.00\"\n    \"id\" => \"2295\"\n    \"tax\" => 0\n    \"subtotal\" => 30.0\n    \"originalquantity\" => 5\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  2296 => array:8 [\n    \"name\" => \"بطيخ إضافي 60 ثانية\"\n    \"quantity\" => 2\n    \"price\" => \"18.00\"\n    \"tax\" => 0\n    \"subtotal\" => 36.0\n    \"id\" => \"2296\"\n    \"originalquantity\" => 2\n    \"product_tax\" => \"-\"\n  ]\n  2297 => array:8 [\n    \"name\" => \"ساديا - دجاج مجمّد 800جم\"\n    \"quantity\" => 2\n    \"price\" => \"16.00\"\n    \"tax\" => 0\n    \"subtotal\" => 32.0\n    \"id\" => \"2297\"\n    \"originalquantity\" => 6\n    \"product_tax\" => \"-\"\n  ]\n  2301 => array:8 [\n    \"name\" => \"بريكستا ويفر المقرمش والمغطى بشوكولاتة الحليب 24 حبة\"\n    \"quantity\" => 1\n    \"price\" => \"13.00\"\n    \"tax\" => 0\n    \"subtotal\" => 13.0\n    \"id\" => \"2301\"\n    \"originalquantity\" => 0\n    \"product_tax\" => \"-\"\n  ]\n]"}, "request": {"path_info": "/customer/check/warehouse", "status_code": "<pre class=sf-dump id=sf-dump-2126811846 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2126811846\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-2057186110 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>6</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>9</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2057186110\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1176051445 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PKqZteC3IabV2rdCxsXBMLnTb72fzH4qdR2KdQSn</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1bowxa%7C2%7Cfx3%7C0%7C1999; _clsk=1osc4j2%7C1750953875247%7C2%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkgvcXBZNkdYd1FjUjNQNjZWSHgvVkE9PSIsInZhbHVlIjoiZVhtamczRkZsS3BqSDRFYjZVVmdOcm10UjA5dkN5SzNRTS8wSGc5bUo3Z1I3Wm5vejdYOC96RHlvTzlHSWV3OVBFZlh0aXVnR0xKdVI3RWRGMEdoSEQwclU3ZCtDNisrK01LakpzMkN1bHd6QmlyZGhSeDdVN0tjYkVPa1dzNytQL1VvNnoyNmlLOEFIWW1OM0RUZXd0Y28rYkdTU3F2cmdiOTQ3RmJURytQaWFmZktnTDhiY204S3JVMjJUUTlQWkh5bm9OeDllWGRrS0pCTDlBblRKSUZmT3U4OUNVU2JQdjFNVGp2SzgrQjczNmQwdnZlSFF0WFRyT1QwVUluTUo1bW1XemFXd3FMc2NFUzA3NWlkSHBuY1U3VXptZFMzRlo3OXZJRTg2MHBvR0QxM1BZSysyWG5HM3FwU2Qxc2hkUFFpQ0VKM0xsa0Q2ZE5pMGwvK0tUVFEvUDZoM01XMlE1akthZDNkWDRoYTVESENlblFRTk9ScE1BNmdlVWdsbDBhZS9xc0ZUc0M3US84aU81WHRNZHhDWEovOU5GNEhLYVlqdjFzb005RDduaTVUbW1WQ3IrdHJSa1RNM2ZhQ29xVW9ydExZaUJBZWlHRFpUTHFFa1FHbjN3MmZEeHpqWmhrcExOOCt5MnEvdnFNaFNKTnFOaEx3VmpiZlhSM08iLCJtYWMiOiI5Yjg2ZDA1N2FjZDg0YmEwOWMxMzczYWNlODUyYzk2ODgxZDU1NWE4MWY3MzcwNWQ3YzVkYzUzNGEyNDljNzE4IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ink0TGtWMjhzMnZMNko2RWg4cUZYU0E9PSIsInZhbHVlIjoiNEZjMHgwMFRySzRrb2lPY1N6Z3c0a3hSTDJYUThqME5EbGJQVWNMd25NNFQ5WnNnc3Bna0lKTS81VGFUelFUdzRiNGxKSHV5RTFGYVJ0dDc4RDRQWHlGKzlWNmhPVVVScEV5Vm9UUzU2aGlERkRrdnhPMVY0dll3NDE3TzVtei84SjE5Z2QyZGEvSFBaS3lyNlZiKzl5ZzQva0RXemY1OEkvMzJUaXFJV0hNYzQ0aVJEUm5pNUZFeCtzRklkamNyS0ZkU2wvZytwVUJHNitPaHpvMUFXOTg5bW1xcFlMVDZFb3RuQnVhWjNENm5HN2E0NVVWTE1QQ1FST3ptYm1HOGVwMXUyV3JMVFJRUVJEYStMOXQwR0Z3WGYyemI2T1ZObmdsZVB3YjVxcWQ1dDNBTUtkZnZtenFSWmtMc3VtMlhGbC9ncDgyb0pJYjdBQ0tkZnZGblJLem95TnE2cWRienN4N0REd1g3SjJFd1ZiVUNBYllrRGNyTXNlZ0haMU1xS3lSbXNnR1pod0IwczFUS0Jad3RadndtbjVnbUMxMHhrcUhtamZPTWJmdFViSHlLbHNkRUFoQnNrVzFkNzNuMHU1L2JHVkFGN0pNL0NtMEVOYTVlK2padWJWdld3dkk3c3Rmb0N3anBJb1orVE5qVnFXTURheVplOHBVcHhJTGYiLCJtYWMiOiJhOWIxYjRjMTUwNmRiZGNhOTQyNDA1ZWFmNDI4NmRkNzA2ZGZkMjMzNmE4MTRkMjc1MzA2MTE1MDkwYThhN2VkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1176051445\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-581902784 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PKqZteC3IabV2rdCxsXBMLnTb72fzH4qdR2KdQSn</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GUUMfjgJKgBqP5oaDcRP4Rfu0FM4k6V9zWwhkyoG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-581902784\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-618961361 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 16:27:35 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IllaYWJkczNGQUlwaGhGcjIzWUdwd1E9PSIsInZhbHVlIjoibVc4K0RmS1JlaXVXTEs4bi9LL253YUJvQzRTQWp3WFBNbmFWSXBGZ3kzQUJTeS92b3VjNDFaV0RTMTBzOEJJQkZQeC8yNUtHYjFtZytSa2E4emVMa3hlUGZxbUxIeXFma2dEakRLOUF6SWdzOFJIeG0yZUhsbmxCTDNnTEJ3bjFNQk1tSzJXUEVzenFJVFNqaTV0a3pnY0sxM2JjRGtUM3h1dDVrYU9YU2hBK2lWTkhvSEI2eUZWOXd2SzVxcWlQYXdxSENBaHRUYU9LbGU1bzNTQVFkWk5GRnNpY3BaazE3N0hzM0thaXk3WndSb2tWdU9DYUNabTVGV3EwRDZ6aGpKSGoyc1RLNjZ6V0pTczBRT09oZzhxUUJ0TVZpOEtyNGQ3ZkgvTW1tQ25IMktPdXFyQVgzajlnOVZzTmJBRmc5WjZkYVNieEZIeXRseFcwYjZMcURIOGV0ZTRoclh4VitjL0x5OVZCQ2tBOUNTMmlrWEZRUXRPSFlKYXV5b2lXT3R2SzlkNVpxRmNzN0oybTRlNUkzLzlHbFR2ZVBUMS9TTkR4aUF2NjlCN0VOVE8rUlg4enNRaklnNnFzbzlwK25DT2MwOC85Wm45bFpvWVYydUtUYzd5dVp3a0IyUjJ4SUp5bU40cy9UTmsvcXVTeUhETDY4a1JwVHRscCtPb3YiLCJtYWMiOiIxZWJhMTYzNzFmYTFlM2JmZTZhYjFiOTllNWU2YWIyMmE0MzU2YjQyYzAwOGM2NGUyNmMzYmU2N2I5ZGQzN2E5IiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 18:27:35 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImFZNVVXL0xGOXdnWjFsQlJ2R3ZJNGc9PSIsInZhbHVlIjoidldyY2FUN1VzTEFVOVZMMXZGMkE5dWRyY3RvKzc0U28rZFpTWkxSR24vS1A0cktaSUg0Tm1ncFp1c0l5Y3lJRVA4TVVtWWJTWlYvaUlKUzdtdUd6MFpaV3RYZWFmTjMzVGdxUEFHSDlsb0U5LyttQzJ1Zk9zeHBSdDA0dS9PQ1NWQmlCckppcmJKaGVhKzNPMW1OVGllQS9Yc0IrbzhZV1U1cDBrdnQ5bW1JR09HbHJSRDdHY2tWMGxMeEpTclpuanZweWE4akp6Z1lIM3NYeUVWbW5sdmJ2MXdhOWVrTVFKWlVESEFwT2lrQVlxb1EraWxKZ1VCNm5UVWwyQTB5cS9vMi9xcUVSNWhhTWV1Mjh4Q0h1azZtc1N0VnFXRytzckk1SFhnU21uYStuM1JnTFBobld1RlJRY2V4b2ZmaXR6RlRHbVlMckZjS1NFVXJXdy9YdDk0a3IyU3U5RGpMMndFUjVEaW1oOXlpRXBkZElzZGdXSDlDN0FJZDRxeUhEc3dmQnpvQ3kxZy9VdklLVjZjOVdKWFBNZmdGb0xlSWlTSEhEQXdZMzVZSXpKcEVNMnlXSERJSk9pdVRrZEVjSFBqb1ZSVVBadmVYWkt1cmxxdURtOWZ3ZTdieGNQaGRRKy9PNFFQZEVsbGpxZHZ5U0tENnd6Z1dCaEVlMEgrem4iLCJtYWMiOiI2M2JiMjFhNDNkZDRlYmU2Y2ZhNDgzYzc3MDM0YWExNDA0ZDQxN2ZjNGQwMWI0M2EyOTE0MTM3YjFhOWVhODM3IiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 18:27:35 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IllaYWJkczNGQUlwaGhGcjIzWUdwd1E9PSIsInZhbHVlIjoibVc4K0RmS1JlaXVXTEs4bi9LL253YUJvQzRTQWp3WFBNbmFWSXBGZ3kzQUJTeS92b3VjNDFaV0RTMTBzOEJJQkZQeC8yNUtHYjFtZytSa2E4emVMa3hlUGZxbUxIeXFma2dEakRLOUF6SWdzOFJIeG0yZUhsbmxCTDNnTEJ3bjFNQk1tSzJXUEVzenFJVFNqaTV0a3pnY0sxM2JjRGtUM3h1dDVrYU9YU2hBK2lWTkhvSEI2eUZWOXd2SzVxcWlQYXdxSENBaHRUYU9LbGU1bzNTQVFkWk5GRnNpY3BaazE3N0hzM0thaXk3WndSb2tWdU9DYUNabTVGV3EwRDZ6aGpKSGoyc1RLNjZ6V0pTczBRT09oZzhxUUJ0TVZpOEtyNGQ3ZkgvTW1tQ25IMktPdXFyQVgzajlnOVZzTmJBRmc5WjZkYVNieEZIeXRseFcwYjZMcURIOGV0ZTRoclh4VitjL0x5OVZCQ2tBOUNTMmlrWEZRUXRPSFlKYXV5b2lXT3R2SzlkNVpxRmNzN0oybTRlNUkzLzlHbFR2ZVBUMS9TTkR4aUF2NjlCN0VOVE8rUlg4enNRaklnNnFzbzlwK25DT2MwOC85Wm45bFpvWVYydUtUYzd5dVp3a0IyUjJ4SUp5bU40cy9UTmsvcXVTeUhETDY4a1JwVHRscCtPb3YiLCJtYWMiOiIxZWJhMTYzNzFmYTFlM2JmZTZhYjFiOTllNWU2YWIyMmE0MzU2YjQyYzAwOGM2NGUyNmMzYmU2N2I5ZGQzN2E5IiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 18:27:35 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImFZNVVXL0xGOXdnWjFsQlJ2R3ZJNGc9PSIsInZhbHVlIjoidldyY2FUN1VzTEFVOVZMMXZGMkE5dWRyY3RvKzc0U28rZFpTWkxSR24vS1A0cktaSUg0Tm1ncFp1c0l5Y3lJRVA4TVVtWWJTWlYvaUlKUzdtdUd6MFpaV3RYZWFmTjMzVGdxUEFHSDlsb0U5LyttQzJ1Zk9zeHBSdDA0dS9PQ1NWQmlCckppcmJKaGVhKzNPMW1OVGllQS9Yc0IrbzhZV1U1cDBrdnQ5bW1JR09HbHJSRDdHY2tWMGxMeEpTclpuanZweWE4akp6Z1lIM3NYeUVWbW5sdmJ2MXdhOWVrTVFKWlVESEFwT2lrQVlxb1EraWxKZ1VCNm5UVWwyQTB5cS9vMi9xcUVSNWhhTWV1Mjh4Q0h1azZtc1N0VnFXRytzckk1SFhnU21uYStuM1JnTFBobld1RlJRY2V4b2ZmaXR6RlRHbVlMckZjS1NFVXJXdy9YdDk0a3IyU3U5RGpMMndFUjVEaW1oOXlpRXBkZElzZGdXSDlDN0FJZDRxeUhEc3dmQnpvQ3kxZy9VdklLVjZjOVdKWFBNZmdGb0xlSWlTSEhEQXdZMzVZSXpKcEVNMnlXSERJSk9pdVRrZEVjSFBqb1ZSVVBadmVYWkt1cmxxdURtOWZ3ZTdieGNQaGRRKy9PNFFQZEVsbGpxZHZ5U0tENnd6Z1dCaEVlMEgrem4iLCJtYWMiOiI2M2JiMjFhNDNkZDRlYmU2Y2ZhNDgzYzc3MDM0YWExNDA0ZDQxN2ZjNGQwMWI0M2EyOTE0MTM3YjFhOWVhODM3IiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 18:27:35 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-618961361\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1006407025 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PKqZteC3IabV2rdCxsXBMLnTb72fzH4qdR2KdQSn</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>17</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2295</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"26 characters\">&#1605;&#1606;&#1578;&#1608;&#1587; &#1593;&#1604;&#1603;&#1577; &#1575;&#1576;&#1610;&#1590; &#1606;&#1593;&#1606;&#1575;&#1593; 54&#1580;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">15.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2295</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>30.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>5</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>2296</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"19 characters\">&#1576;&#1591;&#1610;&#1582; &#1573;&#1590;&#1575;&#1601;&#1610; 60 &#1579;&#1575;&#1606;&#1610;&#1577;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">18.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>36.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2296</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n    <span class=sf-dump-key>2297</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1587;&#1575;&#1583;&#1610;&#1575; - &#1583;&#1580;&#1575;&#1580; &#1605;&#1580;&#1605;&#1617;&#1583; 800&#1580;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">16.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>32.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2297</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>6</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n    <span class=sf-dump-key>2301</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"52 characters\">&#1576;&#1585;&#1610;&#1603;&#1587;&#1578;&#1575; &#1608;&#1610;&#1601;&#1585; &#1575;&#1604;&#1605;&#1602;&#1585;&#1605;&#1588; &#1608;&#1575;&#1604;&#1605;&#1594;&#1591;&#1609; &#1576;&#1588;&#1608;&#1603;&#1608;&#1604;&#1575;&#1578;&#1577; &#1575;&#1604;&#1581;&#1604;&#1610;&#1576; 24 &#1581;&#1576;&#1577;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">13.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>13.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2301</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1006407025\", {\"maxDepth\":0})</script>\n"}}