{"__meta": {"id": "Xc795f519ffecca894171506ba7f6622a", "datetime": "2025-06-26 16:04:13", "utime": **********.575458, "method": "GET", "uri": "/login", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.075437, "end": **********.575478, "duration": 0.5000410079956055, "duration_str": "500ms", "measures": [{"label": "Booting", "start": **********.075437, "relative_start": 0, "end": **********.490797, "relative_end": **********.490797, "duration": 0.4153599739074707, "duration_str": "415ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.490807, "relative_start": 0.41536998748779297, "end": **********.57548, "relative_end": 1.9073486328125e-06, "duration": 0.08467292785644531, "duration_str": "84.67ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46559208, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 4, "templates": [{"name": "1x auth.login", "param_count": null, "params": [], "start": **********.541289, "type": "blade", "hash": "bladeC:\\laragon\\www\\ERPcopy\\public_html\\resources\\views/auth/login.blade.phpauth.login", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fresources%2Fviews%2Fauth%2Flogin.blade.php&line=1", "ajax": false, "filename": "login.blade.php", "line": "?"}, "render_count": 1, "name_original": "auth.login"}, {"name": "1x layouts.auth", "param_count": null, "params": [], "start": **********.546769, "type": "blade", "hash": "bladeC:\\laragon\\www\\ERPcopy\\public_html\\resources\\views/layouts/auth.blade.phplayouts.auth", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fresources%2Fviews%2Flayouts%2Fauth.blade.php&line=1", "ajax": false, "filename": "auth.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.auth"}, {"name": "1x landingpage::layouts.buttons", "param_count": null, "params": [], "start": **********.564005, "type": "blade", "hash": "bladeC:\\laragon\\www\\ERPcopy\\public_html\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.phplandingpage::layouts.buttons", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2FModules%2FLandingPage%2FResources%2Fviews%2Flayouts%2Fbuttons.blade.php&line=1", "ajax": false, "filename": "buttons.blade.php", "line": "?"}, "render_count": 1, "name_original": "landingpage::layouts.buttons"}, {"name": "1x layouts.cookie_consent", "param_count": null, "params": [], "start": **********.566404, "type": "blade", "hash": "bladeC:\\laragon\\www\\ERPcopy\\public_html\\resources\\views/layouts/cookie_consent.blade.phplayouts.cookie_consent", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fresources%2Fviews%2Flayouts%2Fcookie_consent.blade.php&line=1", "ajax": false, "filename": "cookie_consent.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.cookie_consent"}]}, "route": {"uri": "GET login/{lang?}", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@showLoginForm", "namespace": null, "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=344\" onclick=\"\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:344-359</a>"}, "queries": {"nb_statements": 9, "nb_failed_statements": 0, "accumulated_duration": 0.00693, "accumulated_duration_str": "6.93ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 555}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 348}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.5228262, "duration": 0.00202, "duration_str": "2.02ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 29.149}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'kdmkjkqknb' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 537}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.527067, "duration": 0.00266, "duration_str": "2.66ms", "memory": 0, "memory_str": null, "filename": "Utility.php:537", "source": "app/Models/Utility.php:537", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUtility.php&line=537", "ajax": false, "filename": "Utility.php", "line": "537"}, "connection": "kdmkjkqknb", "start_percent": 29.149, "width_percent": 38.384}, {"sql": "select `full_name`, `code` from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 543}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.5315971, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "Utility.php:543", "source": "app/Models/Utility.php:543", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUtility.php&line=543", "ajax": false, "filename": "Utility.php", "line": "543"}, "connection": "kdmkjkqknb", "start_percent": 67.532, "width_percent": 2.309}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 4748}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 4682}, {"index": 15, "namespace": "view", "name": "auth.login", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\resources\\views/auth/login.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.54182, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4748", "source": "app/Models/Utility.php:4748", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUtility.php&line=4748", "ajax": false, "filename": "Utility.php", "line": "4748"}, "connection": "kdmkjkqknb", "start_percent": 69.841, "width_percent": 6.782}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 4748}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 4682}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\resources\\views/layouts/auth.blade.php", "line": 10}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.547425, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4748", "source": "app/Models/Utility.php:4748", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUtility.php&line=4748", "ajax": false, "filename": "Utility.php", "line": "4748"}, "connection": "kdmkjkqknb", "start_percent": 76.623, "width_percent": 4.473}, {"sql": "select * from `users` where `type` = 'super admin' limit 1", "type": "query", "params": [], "bindings": ["super admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 4113}, {"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 4155}, {"index": 18, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.556895, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4113", "source": "app/Models/Utility.php:4113", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUtility.php&line=4113", "ajax": false, "filename": "Utility.php", "line": "4113"}, "connection": "kdmkjkqknb", "start_percent": 81.097, "width_percent": 6.782}, {"sql": "select `value`, `name` from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 4114}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 4155}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.55966, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4114", "source": "app/Models/Utility.php:4114", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUtility.php&line=4114", "ajax": false, "filename": "Utility.php", "line": "4114"}, "connection": "kdmkjkqknb", "start_percent": 87.879, "width_percent": 4.329}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 4748}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 4682}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\resources\\views/layouts/auth.blade.php", "line": 39}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.561215, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4748", "source": "app/Models/Utility.php:4748", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUtility.php&line=4748", "ajax": false, "filename": "Utility.php", "line": "4748"}, "connection": "kdmkjkqknb", "start_percent": 92.208, "width_percent": 3.752}, {"sql": "select * from `landing_page_settings`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "Modules/LandingPage/Entities/LandingPageSetting.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\Modules\\LandingPage\\Entities\\LandingPageSetting.php", "line": 27}, {"index": 19, "namespace": "view", "name": "landingpage::layouts.buttons", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.5648441, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "LandingPageSetting.php:27", "source": "Modules/LandingPage/Entities/LandingPageSetting.php:27", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2FModules%2FLandingPage%2FEntities%2FLandingPageSetting.php&line=27", "ajax": false, "filename": "LandingPageSetting.php", "line": "27"}, "connection": "kdmkjkqknb", "start_percent": 95.96, "width_percent": 4.04}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "7AbWtqXnzbsjxSEgglw36A1EIaaJJF4wKz852p9e", "_previous": "array:1 [\n  \"url\" => \"http://localhost/login\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-1463230773 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1463230773\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-128269967 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-128269967\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-323800841 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-323800841\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-337726185 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1881 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1bowxa%7C2%7Cfx0%7C0%7C1999; XSRF-TOKEN=eyJpdiI6IkcyM1pyT1p5YjNRbkhXQVRrMUUyL1E9PSIsInZhbHVlIjoiVklYejZBOHMwSktwZjQzV3E5c1FiSkxkODV1ZDFDTkNOSXdTTEFjS05GenplcXcwTkpMTHZvN3ZycG1OWnZUQ0djdU5CeDgzRS9ISnJ0Mk9YYS9kWVFIWFBVWHdhQ1JVYmU0bzNYdytUd1RydlU4ajRXbGZIYk1NbzRIamRKQTVUdGpQWjlTUkEyMFo4Y2FTbnpHRWJvWFhTdzF6blRReUp4cndZbXQzVDZHNm15d203aEhJVUNLR3lKcXY3ZGpidEFyKzdLU2NUeDVaRG1EOUhnQ2FPeUlGOXFvOFBJeWVFNEVlcDN0SFMwM1lkQmk2UlhvRXRJT2FJQm13S0Z5eXRjRlQxaXBXLzdCNmNISTZuY3lzT3NyNURxRUd6blB6c0ZpME1KL3UxeWM1Tmsyd1lwTmhVeGF5Y2NFaVhwM2txVm44NXR4eXp5TTJEaVJnMGZsbVE2YmRUZTFoalFFa3dOUnFwbDIrZ2RrTDJZeG5mWlo2N3lZaDY3bWZzd29mRE5TeUpuZXhSaG9MTzlxZXdzK3poaHl4UTN1bk5vbmh3M0FoeGZ5cERXRGhaZ09sTkU1Uk16a3BGMmJpUTgvWnNBQVZaWVFhRHRnMmtDRXU5ckFCQ0N0VEhlNktlc005ajVpcENSelhQczNIb1RJeFFnY0lkMTFhSXd2MU42YVgiLCJtYWMiOiIzNWNhZmNkMTAwMmY4MWM0ZjQyZTRlZmIwY2M2NjNiMDYxYWQxMzRiMmViZmY3MjBmMWVjNGU0ZTAwOGM1NDIyIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IitoK0k0cEo0VTBzeThhT1BSN2Rtb0E9PSIsInZhbHVlIjoiUXNFUkt2QUQ4MnN3cXVISVRBY2lkNDFJcExaMHVDQ2NNOXFDMmd5VmMwN2F1Y1JGVEFmWVcvdzRidE52VHVlYmtOMHhpekhSVWtVL093dVJsVnZhRmpTNURCN2VDdEVPTDR2N2ZPSVNQWFZOTEV4NmM4WG1kTHFDS0pHMDVab3hGbU95bVN0dDdVcHpZeEZYZ0ZRRW5abGFSNjFjSWZxekdwcURrSkVhaEVKYUZvd1hQMDVGUUhXcHVHSmlCVU9XaEw2aVFidk9LcGlGZXdRVnpEQVlzMUpPcmFSL1BlbnFLWnNEblFwUGZrYlhDYWY4T3RWN1Ivb2M4YlNRTm5STm1SS2dFNkNPdDRBZnpJU01tbURuM0Jia1U0QnJlNXk5MGdtc3FoMXdvSU9XUXBxdnUvaXRYNHNLZjJPMWlkSG9iY282bEdpZVM2VzVwWXNiTXVCYmJYbTZ0K1BCK2xERVdUT1pZbHU4bCtNdTFYMjFxdUx5ZDF3aGFkYThraHc0MFdJT0l5OUxEL0JHekRQSkxQenlXSjQrRC8rd3o3eDVSSVQySjdkTnY3Kys3NjNJVHN2NlQ4Rkh0dWZvc0QrcE8zOXhWK0Rrb0dJcUZGWmFQSk8vMUlpQ3MxZThFaFF1YXpsTHVrOXRQcEpxVU45N2tBRXhGamVYTStpeExHNSsiLCJtYWMiOiI0NjlhODFlNGVmYTgyNzMyNjhlM2M2NTBiNDBmMTAyMGJiYWI2MDJiMmQ1OGI1ODE4ZjNkMDE2ODQ2NmYwZmVhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-337726185\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1078568410 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7AbWtqXnzbsjxSEgglw36A1EIaaJJF4wKz852p9e</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0rKIsasETh45ci3YxbzYpwHq1nxkSLXGWDCmWFCd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1078568410\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-30865417 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 16:04:13 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImFNeHRHS1hGQ0o0U2xxVmVzK1RUU2c9PSIsInZhbHVlIjoidWdoSnl3cERENnhwcGIwbFlOaTM3VGZCNGMrakUvbzNuMDJGUHNsR0ZBc0tKNC9Eb0NlMnlXc25IK0w0aFIzTUZYRTFta3hWVXBsejhiaXhvUDNLeXFFa2JKWll6Ujh3QnF0RDRQOHVvR1FGb3B1VGsyTXhPc2pFdnNXTWxWUVFxRnEwMURGVzNkZFRRa0xyN0NPcmdPWW1iYWJ1dGt4OEI4ZnQxWTE1UVFzY0FtQzZxcS84NkxyT290a1c5Q2thbVIyZC96U3ZlTWJ5ZGRON1BueStnRi9KdG1DMW1FdG1MRmVhRTBDdWlMK0RMS3oyTlQybFp3N0JvdWUzellhWUtuRGlQektDblc0cHF1NVNtbDB0cVR5ZHlOV0o4WnpQeUJnVHQrUW5naFdNTjlmOWJEblBRL3pNVVgwTVcrUHRBY2Zid1VVbjZNUEI2ck5UOUlzSVpvL29wc3BGMENPZThFTFBkcTQ2RzFhUDdjRTgrSDNZVGZ1UEhya1pKeG9FOGtwQUxsQjhSalZOV29QMXl0V1Z3VWtuYy81dUxsemdRRVFobVl2cnFuL1crYi9QYS9KOEtLQk9UKzdyUzJyUjI1U2JSdFhlQlgvQ1ZoV0tCWmE5R0wwVnIrd2dyZWRRRnZlSUROTVp0YzJFbUtCZEZPZUg1QVBOQVVNN0wzRHoiLCJtYWMiOiIxZjdmYjJjYmYyZDQ3YTA3MGZlN2MzZjlhNzAzNGY3NWEwODI5MjI4OWVjNzAzMmYxMzUxYTQ5M2EzODc0OThiIiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 18:04:13 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlZ0d015N04zc3d0Q2lncjRjK0ZkTHc9PSIsInZhbHVlIjoiVlVMQ0k4UjNjdlcyOU9jUDlTWE9jaTJTWWVsTEY4cmJJTHE3b0paeVo3UE5wRS9Ec3BGMGFZNUxaV25aeUdYK1dieDM1KzV5YmxMMzIxRXNmMzNTb25ObzdKT09jWHVYL2JaMzFKaENLUGpRSkNSUkk4Y0xPUkRVb2lNL0huWUVZT3VKcDhQOWxlTkhPcG4wMWtjZVRiSkJ2bCs1MFpGTkhxUWt3ODNvK0JKdzJuYUIyS3NHaURSU2hhZ0E4R2ZzKzQraVVjSi9sM3lKbHJzdWZEVHJ6VG5hRVh6TUNzVWdacE4vWHdrUDk5ek1wdzRnM1NUS3RrUGFvNCtZSW1SQmt3c1Uvb281dGo4VksveE5uSzJJZjRxQXhQM3lXKyt2VkZIZ0YzVFV2MExMSnVmNHF6UFlxb0ZYVHRwREpzQzFFSCtVclJIT3dacUJDeXZLVXpQc05QMGxPdW9yQjI3cTNPNFo2SnN5NHlWMzRnOVlFR0Ryb3lEc081T2FTV3BvUnpVZlp6Z2Y2MTdvNFg2bDJzZnFVc0JVTEo5dFl4bkhPNXhBK0tqdlRta25xc3pIQXdPUy80UnEwNTZTQ2NRNTh2MTVYZG1HZkVuRFZpQlJKM3AyTmkxaUpXUlZBNU9GTDdXdzlqMDlodmJyZ1poTStkdTBIM2NncXRXWlBJYlYiLCJtYWMiOiI1OGViNTk0NDgzYzY5MjYxYTExMmFkNDJhYjRkMWVmNzAyODJhMGUzOTMxZWEwNjE1OTdlOTE2ZTZkNTMyOTNmIiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 18:04:13 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImFNeHRHS1hGQ0o0U2xxVmVzK1RUU2c9PSIsInZhbHVlIjoidWdoSnl3cERENnhwcGIwbFlOaTM3VGZCNGMrakUvbzNuMDJGUHNsR0ZBc0tKNC9Eb0NlMnlXc25IK0w0aFIzTUZYRTFta3hWVXBsejhiaXhvUDNLeXFFa2JKWll6Ujh3QnF0RDRQOHVvR1FGb3B1VGsyTXhPc2pFdnNXTWxWUVFxRnEwMURGVzNkZFRRa0xyN0NPcmdPWW1iYWJ1dGt4OEI4ZnQxWTE1UVFzY0FtQzZxcS84NkxyT290a1c5Q2thbVIyZC96U3ZlTWJ5ZGRON1BueStnRi9KdG1DMW1FdG1MRmVhRTBDdWlMK0RMS3oyTlQybFp3N0JvdWUzellhWUtuRGlQektDblc0cHF1NVNtbDB0cVR5ZHlOV0o4WnpQeUJnVHQrUW5naFdNTjlmOWJEblBRL3pNVVgwTVcrUHRBY2Zid1VVbjZNUEI2ck5UOUlzSVpvL29wc3BGMENPZThFTFBkcTQ2RzFhUDdjRTgrSDNZVGZ1UEhya1pKeG9FOGtwQUxsQjhSalZOV29QMXl0V1Z3VWtuYy81dUxsemdRRVFobVl2cnFuL1crYi9QYS9KOEtLQk9UKzdyUzJyUjI1U2JSdFhlQlgvQ1ZoV0tCWmE5R0wwVnIrd2dyZWRRRnZlSUROTVp0YzJFbUtCZEZPZUg1QVBOQVVNN0wzRHoiLCJtYWMiOiIxZjdmYjJjYmYyZDQ3YTA3MGZlN2MzZjlhNzAzNGY3NWEwODI5MjI4OWVjNzAzMmYxMzUxYTQ5M2EzODc0OThiIiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 18:04:13 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlZ0d015N04zc3d0Q2lncjRjK0ZkTHc9PSIsInZhbHVlIjoiVlVMQ0k4UjNjdlcyOU9jUDlTWE9jaTJTWWVsTEY4cmJJTHE3b0paeVo3UE5wRS9Ec3BGMGFZNUxaV25aeUdYK1dieDM1KzV5YmxMMzIxRXNmMzNTb25ObzdKT09jWHVYL2JaMzFKaENLUGpRSkNSUkk4Y0xPUkRVb2lNL0huWUVZT3VKcDhQOWxlTkhPcG4wMWtjZVRiSkJ2bCs1MFpGTkhxUWt3ODNvK0JKdzJuYUIyS3NHaURSU2hhZ0E4R2ZzKzQraVVjSi9sM3lKbHJzdWZEVHJ6VG5hRVh6TUNzVWdacE4vWHdrUDk5ek1wdzRnM1NUS3RrUGFvNCtZSW1SQmt3c1Uvb281dGo4VksveE5uSzJJZjRxQXhQM3lXKyt2VkZIZ0YzVFV2MExMSnVmNHF6UFlxb0ZYVHRwREpzQzFFSCtVclJIT3dacUJDeXZLVXpQc05QMGxPdW9yQjI3cTNPNFo2SnN5NHlWMzRnOVlFR0Ryb3lEc081T2FTV3BvUnpVZlp6Z2Y2MTdvNFg2bDJzZnFVc0JVTEo5dFl4bkhPNXhBK0tqdlRta25xc3pIQXdPUy80UnEwNTZTQ2NRNTh2MTVYZG1HZkVuRFZpQlJKM3AyTmkxaUpXUlZBNU9GTDdXdzlqMDlodmJyZ1poTStkdTBIM2NncXRXWlBJYlYiLCJtYWMiOiI1OGViNTk0NDgzYzY5MjYxYTExMmFkNDJhYjRkMWVmNzAyODJhMGUzOTMxZWEwNjE1OTdlOTE2ZTZkNTMyOTNmIiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 18:04:13 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-30865417\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1369790907 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7AbWtqXnzbsjxSEgglw36A1EIaaJJF4wKz852p9e</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1369790907\", {\"maxDepth\":0})</script>\n"}}