{"__meta": {"id": "X2f7cde88283257c7d39d5fc96d97873b", "datetime": "2025-06-26 16:03:24", "utime": **********.979159, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.519007, "end": **********.979172, "duration": 0.46016502380371094, "duration_str": "460ms", "measures": [{"label": "Booting", "start": **********.519007, "relative_start": 0, "end": **********.878796, "relative_end": **********.878796, "duration": 0.3597891330718994, "duration_str": "360ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.878806, "relative_start": 0.3597991466522217, "end": **********.979174, "relative_end": 1.9073486328125e-06, "duration": 0.10036778450012207, "duration_str": "100ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48717208, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.03089, "accumulated_duration_str": "30.89ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 17 limit 1", "type": "query", "params": [], "bindings": ["17"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.912525, "duration": 0.02588, "duration_str": "25.88ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 83.781}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.946908, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 83.781, "width_percent": 1.295}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 17 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["17", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.960397, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 85.076, "width_percent": 1.91}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (17) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.9622679, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 86.986, "width_percent": 1.263}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.9667552, "duration": 0.0024, "duration_str": "2.4ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "kdmkjkqknb", "start_percent": 88.249, "width_percent": 7.77}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.971231, "duration": 0.00123, "duration_str": "1.23ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "kdmkjkqknb", "start_percent": 96.018, "width_percent": 3.982}]}, "models": {"data": {"App\\Models\\ProductServiceCategory": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 28, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 17, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-568333867 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>17</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-568333867\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.965768, "xdebug_link": null}]}, "session": {"_token": "jEiKRXRaeQw7kpqtZmXNWu3bpmEnaxKAnNI8mVhc", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "17"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-808093913 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-808093913\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1171497899 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1171497899\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1307389618 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1307389618\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1152417450 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">jEiKRXRaeQw7kpqtZmXNWu3bpmEnaxKAnNI8mVhc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1843 characters\">_clck=1lqc4d8%7C2%7Cfx3%7C0%7C2003; _clsk=1h4itwx%7C1750953725629%7C12%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlJQdyt5c09uMzROcUJSQlZyVEZuWGc9PSIsInZhbHVlIjoibkt1VFY4ZGdpMUIyMEtVR1J3WVhLbmRaSkRCOTN1VVkwVThZRU5zb3V3VmJlT3VFQWJQQlVJaW0yczdrUHc1S2EyN2VDYzNLS0J2ZVdFRUE5elBLNHJ1cldGZVFJYi9xZ1BpSFgzQ1p3VzBTTDdiTm5CTHBFblI2N3gwaGRNaldKTFhwdEd0N2hNTGJNODZheE5KRG9TNTdJTngrUGZ1MGE0ME5qTDRGY2xGbUFBK0NqeStJdWtQSDNTckxJK3FmSVZ4UURGWkZRZ2RveE43dG91SmkyaEphemJ4bVMyZ0sxWXQxM3lucEQ4T0pOYWhad05kL0srb0VONGFjSERZb2x0RlIvSEF0NXZtTUQ1Y0NzU1NKZnFPaUxpR2lNNEZOMFMrT2VUWnZ3NE9CN3ErUkNCMmowak1WZlNCOEo1aWNGbktDRHpjWnFOVVNuZkI2R0dCMTQzL1dhaHpjaHQ5aGVvZHozRTcxNXpYaXgxWHI3RnpVWUdHNGtTTEdjSVN6QU1YZ3IvSHl0VmFycGcyNFg1UUhrUkpvb1g2WldFZG9XNmR2RFRiNnV0RnozMzcranhDSHY3Q2hDQXVKVDh5Vyt0VnJoM1lGbmtJZGRBVUFpVWhYL01VVG8rM25PeE5RR2VqMFdVM2xpRDI4WXAySzQvdThlaEJvcEFET2RJYUwiLCJtYWMiOiJhMWE3OTdjMDdlYTM1MmQwN2VlY2Q1MDRlZjczMTc5NTU5ZGRlYjAwYjg1ZjE1ZTYwMDMzOTFhMmIxMzFkODc4IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkI5VjdpOWNkb1RCeGZ3VFpBMlJrMEE9PSIsInZhbHVlIjoiZVFXNUZhUkg1RXBlcUdHL1YybXMwRHhBcnRGQVJSOFhjQWQydWFxQzdyNTVRWWdkWFdvQUJSeEo3VG9JemxKNllueG9aRGRYZDZJZmtod28wc1pJbVk2aG1TVG05dXM5TFpFUDBWT3kybnNtLzJVSFN3ektWbWIzTlNQbWxYcGtGNW5RUXFLMUFtSkQ3Ylh6YTh0TWExTUdrU0t4enFKZzJLbktWZ3ZTSHdmSDZpUTkrU3hVUmIveUR2RzA1TVAxVzA0NkFBTm1yTGErQUkrdlRCU2xZcDZSTzFRYkZPWjF3NFBjRk9NY1VRdmpMOEt0T0hnRHgxRVVwaVh4a0Y4WFdpelM4SjRpdUZ1ZEduTVI5UG5ZZERYRmc1WlNGUUxIQVIrOUsxSE1YdWJLYm1BWCtQZUtLRnJTMURJVlkxck04NmJYemM1OGdrU3RITkhiN0FWa1Jvb3QvRUFGRGZ4YmVoY09mWG0vSmtseE14b1VzWmVaWStaaGZ4R0NjUk1WMmVMdS9Ud2RHOGZHL0VGMFIwS3hXZG5YTjNUcXgrQ0cvTDQ4Mm5CY2dPVk9DTW5WRXlhcXJ3WTlzZWlBRDhPVXQvaWdlYVF4Y1Nlb0pEeHdXT2UyWlY3amVOU2xaN3NVWUZqQVRsQllueVdFdG91dDRWRkduVjlTVGNFOW5ISnIiLCJtYWMiOiI4OWE2ODMwNmVmYzY4MWE2ZmYyNTI1ZTdkYjI4Njk5YmViNWNlMjA0MzA2MDdmOGZlNGQ3MzM0Y2U3MmRkNWFjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1152417450\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1608771881 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">jEiKRXRaeQw7kpqtZmXNWu3bpmEnaxKAnNI8mVhc</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PSbLK6QAWuUzu3JpIF2TDtYQ1L7TOqCcejFUVX3V</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1608771881\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-265916072 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 16:03:24 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InpjWVlhNVBwc09QK0FkTXpXUHZRMWc9PSIsInZhbHVlIjoiRHBsOVFVdEJWYUNSaHo5Ylg0ZUtCajhFY2ZTUjMzazN4ck1HK2JLbnhKaGFXL1VWaVdRNjF5RDQzOEphT1B3U1d0TGxYUzRSK2hmdzRndDVZWXBtUkRMQU9WRS9rWkFRNVNHdkVBNWNjcDR2cDVBT1Z5SGJoMldwUjB5cGxFeU5LTnVBYVFST1UrQ2hLai84VjJoVTVmaDBiUlROK3dmTHlJb0JyODV4QnlsQ2F2RW5TcUtSRFo4VnJwMWdiQnZJQThzZE13YVlLSjlmNDJDNFZDb2xtT1MvejdIeWlqbitjSTJyS0Y1N1Vod0FudHI1dW5TeENGN2lhTHZPWGNxSUYwbEN6eWFYZ3FOUTd5aG01V0ZZL1ZGZm44aEVndmJSVUM2MmYxb2xSZnFNbWZ4OC9hbmVad3B3a0FSeURXb2NGS1p0RnUraGtiYjFmc0NVMm5BOVBzUmJ4SFJVelc3UFJyT3BuVENvWVp6eitXS2dCaXV0dGZIS2hINHlQVTJBQnNoRnVkYVdJT2xDeURYelhMQU95bmRKYlFtTFpwSXlTUDJpRWZNMjM5NHY5c3E4b2g1S3d0TVFLSmpuMWZDM1VYRXFQK2ZPd3cvYUs0c3lvMkhSem9kdktFcVhxUW15Y1JzSTl1VnRlamlZdHl4M1lodFFVeWFZNTVvZUtHeWwiLCJtYWMiOiIyY2QxZTAzZjk1ZTAzYmFhNzA2NzZhZTY1MjUwYWU1ZGJmMWNlY2I1ZmVlMGFjNTAxYjg5NmNhOTNlOGY0OGJkIiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 18:03:24 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImladjFaV21yR2IzMTYybHhwbDVDSlE9PSIsInZhbHVlIjoiTjk3aDZKWjQ2MVI0Q1NxcjA4OG5zK1hXczVIM00ya3plRjFlQkIvR2tSUk1iUXY3VkUzcmlOUHFpdldUa3hWc0lkeEEyTW10M2dwTzc5T3UzNDBoQ1FwZ2x1NXU1K013aW5acVVKcGdmY08vNTlubll1a1ZkQXovUU9iMkRwcEZhOXpsQWhxbEJSMDNEUHJ5cGNrQlV3c2N1YmxsYXlhUUthQmF5RHhnalc5NlRjaWFmb2p1VTJWdHFuZjZXeHhXOEM4WXBSV3d2c3Y2bkQ1ckZSRTlxMTNMSzNWUkpMYVBkRU5CYTdxV1RjMWE5ZUx3cm9USC9XS3A3cEZQQktGTjI3TDhza1ZtSWJyckRTR29tWXQ5MGphTmRMR0Rtc1pucWEwc1NSVGxmUDRUcnA1MlNKOTNTdWdPdGxseVRMdlhZYURjYVFDWmNtSmV1THU0SlJWeEVQTkQrMHJaRngvWjkvdkxRd2NNUzlGQTd3aElhS25oMmJFUDhTTjVMNFBURDVuWUJiVk9UNytpOUlUeE05dWJ0dm1xNktONzhlM1J1UFJDM1p4bW16RjJaak5YbERVWFY4bkI0NkRFSkI3SWhIOG9KWlpqaUppK016cFp5MTJEMWZac0M0OTFqVysxODdCQit1ZTlpb3l0SWJMSlNnaXI1N1pRRWt3KzFNWXMiLCJtYWMiOiI4NzQwNjYzMzk0YTkyM2I0MDdlNjhlNGIxZjQ2MzJlYjI2NDk1YmZkYjAxYzdiZjZmMzdmNjRhNjRiY2IxMTU1IiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 18:03:24 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InpjWVlhNVBwc09QK0FkTXpXUHZRMWc9PSIsInZhbHVlIjoiRHBsOVFVdEJWYUNSaHo5Ylg0ZUtCajhFY2ZTUjMzazN4ck1HK2JLbnhKaGFXL1VWaVdRNjF5RDQzOEphT1B3U1d0TGxYUzRSK2hmdzRndDVZWXBtUkRMQU9WRS9rWkFRNVNHdkVBNWNjcDR2cDVBT1Z5SGJoMldwUjB5cGxFeU5LTnVBYVFST1UrQ2hLai84VjJoVTVmaDBiUlROK3dmTHlJb0JyODV4QnlsQ2F2RW5TcUtSRFo4VnJwMWdiQnZJQThzZE13YVlLSjlmNDJDNFZDb2xtT1MvejdIeWlqbitjSTJyS0Y1N1Vod0FudHI1dW5TeENGN2lhTHZPWGNxSUYwbEN6eWFYZ3FOUTd5aG01V0ZZL1ZGZm44aEVndmJSVUM2MmYxb2xSZnFNbWZ4OC9hbmVad3B3a0FSeURXb2NGS1p0RnUraGtiYjFmc0NVMm5BOVBzUmJ4SFJVelc3UFJyT3BuVENvWVp6eitXS2dCaXV0dGZIS2hINHlQVTJBQnNoRnVkYVdJT2xDeURYelhMQU95bmRKYlFtTFpwSXlTUDJpRWZNMjM5NHY5c3E4b2g1S3d0TVFLSmpuMWZDM1VYRXFQK2ZPd3cvYUs0c3lvMkhSem9kdktFcVhxUW15Y1JzSTl1VnRlamlZdHl4M1lodFFVeWFZNTVvZUtHeWwiLCJtYWMiOiIyY2QxZTAzZjk1ZTAzYmFhNzA2NzZhZTY1MjUwYWU1ZGJmMWNlY2I1ZmVlMGFjNTAxYjg5NmNhOTNlOGY0OGJkIiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 18:03:24 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImladjFaV21yR2IzMTYybHhwbDVDSlE9PSIsInZhbHVlIjoiTjk3aDZKWjQ2MVI0Q1NxcjA4OG5zK1hXczVIM00ya3plRjFlQkIvR2tSUk1iUXY3VkUzcmlOUHFpdldUa3hWc0lkeEEyTW10M2dwTzc5T3UzNDBoQ1FwZ2x1NXU1K013aW5acVVKcGdmY08vNTlubll1a1ZkQXovUU9iMkRwcEZhOXpsQWhxbEJSMDNEUHJ5cGNrQlV3c2N1YmxsYXlhUUthQmF5RHhnalc5NlRjaWFmb2p1VTJWdHFuZjZXeHhXOEM4WXBSV3d2c3Y2bkQ1ckZSRTlxMTNMSzNWUkpMYVBkRU5CYTdxV1RjMWE5ZUx3cm9USC9XS3A3cEZQQktGTjI3TDhza1ZtSWJyckRTR29tWXQ5MGphTmRMR0Rtc1pucWEwc1NSVGxmUDRUcnA1MlNKOTNTdWdPdGxseVRMdlhZYURjYVFDWmNtSmV1THU0SlJWeEVQTkQrMHJaRngvWjkvdkxRd2NNUzlGQTd3aElhS25oMmJFUDhTTjVMNFBURDVuWUJiVk9UNytpOUlUeE05dWJ0dm1xNktONzhlM1J1UFJDM1p4bW16RjJaak5YbERVWFY4bkI0NkRFSkI3SWhIOG9KWlpqaUppK016cFp5MTJEMWZac0M0OTFqVysxODdCQit1ZTlpb3l0SWJMSlNnaXI1N1pRRWt3KzFNWXMiLCJtYWMiOiI4NzQwNjYzMzk0YTkyM2I0MDdlNjhlNGIxZjQ2MzJlYjI2NDk1YmZkYjAxYzdiZjZmMzdmNjRhNjRiY2IxMTU1IiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 18:03:24 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-265916072\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-831821650 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">jEiKRXRaeQw7kpqtZmXNWu3bpmEnaxKAnNI8mVhc</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>17</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-831821650\", {\"maxDepth\":0})</script>\n"}}