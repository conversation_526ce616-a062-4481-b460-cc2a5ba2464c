{"__meta": {"id": "X437899aadab00ef438da1700aaac4628", "datetime": "2025-06-26 15:57:58", "utime": **********.947039, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.419032, "end": **********.947054, "duration": 0.5280218124389648, "duration_str": "528ms", "measures": [{"label": "Booting", "start": **********.419032, "relative_start": 0, "end": **********.795329, "relative_end": **********.795329, "duration": 0.3762969970703125, "duration_str": "376ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.795337, "relative_start": 0.37630486488342285, "end": **********.947056, "relative_end": 2.1457672119140625e-06, "duration": 0.1517190933227539, "duration_str": "152ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45649392, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00292, "accumulated_duration_str": "2.92ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.922347, "duration": 0.00204, "duration_str": "2.04ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 69.863}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.932894, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 69.863, "width_percent": 16.096}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.9396749, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 85.959, "width_percent": 14.041}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "G4oUC4dvL91DvSdXtTSUa87JqfM4sfNNzjOiK6ns", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/bill/eyJpdiI6InNzWlhKbTYwWndnMXhFSnlrbWhnK0E9PSIsInZhbHVlIjoiaWhTMHpYT3kzU0JqQTdHK01oV1JvUT09IiwibWFjIjoiODg3MTAzOGJiNDk1MzcyODU5MWZmZTNkMTdhYTAzMDJkNmI1NjlmODFkYzk1MzA3NTljODFhMzc1MTkyMzg5YiIsInRhZyI6IiJ9/edit\"\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1908405549 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1908405549\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-989211111 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-989211111\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1691835702 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G4oUC4dvL91DvSdXtTSUa87JqfM4sfNNzjOiK6ns</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1691835702\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2096115695 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"227 characters\">http://localhost/bill/eyJpdiI6InNzWlhKbTYwWndnMXhFSnlrbWhnK0E9PSIsInZhbHVlIjoiaWhTMHpYT3kzU0JqQTdHK01oV1JvUT09IiwibWFjIjoiODg3MTAzOGJiNDk1MzcyODU5MWZmZTNkMTdhYTAzMDJkNmI1NjlmODFkYzk1MzA3NTljODFhMzc1MTkyMzg5YiIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1ap6d1q%7C2%7Cfx3%7C0%7C1998; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clsk=p74v85%7C1750952249218%7C77%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjVjUGVvMEdScXVGdFpzb1h4N2JKSkE9PSIsInZhbHVlIjoiNkk5eEpPOG44bkVJM0QyM1VibWJ3SGhoWnNBM0g3QndsdzVkNnpySlJGcjFCL2Q1QnhPQjN4UnBEZk9Dd0VJRjJadTZta3R6WHUvWWNmNkZwZElkcVdkeFdMUi9oZDFQVE5IVzI3REFrK1Z4TlZSdEdIV3BrNGFLY0lhUVV0RVJLLy80OFgzNnp3YWtFUEl2UFFrcjgwY3BvcFRXQXVGQW11cmFBZ0RrYUpXWFdyQW1vMDc1OTdKcHV4YzhCREgxbzZxbmF3K3gxNm5NSG5NZDd4WGRvdGEycVlqR1YrdndxankrQWlxaXVZQVVXVisveUU3bE5uSnlUVmhFWGZuS2JPK0t5U2s0eHdtLzZmODlEaWt0S0dCWUdyUFRIVFlFN2lGY0I3OW44OXlNWmhYdEMydW9lOHdEZTRLNU8wTlUzc2JpUDVLa1dSb05ybVFQUzhLZ0RQWFJqSHRhZVVaMW5FYVR5L0xwN3NOK0hDVUcyRDZ1ZFVQaXYvdGhRNzZPUHZjRU95RGtuUHcveFlJLzZUT2lJRVhDdVJSY2o1K1BnRldpdmF4emZ4K3RQamF0clpiR0J5dHdDRkdWb01EZ1JhM29URCtDWTdBZ2NKako0eWxQLzd1eDZ6K3ZmSlVoRzlMcm5mKzk5V3dpcEFyR0FyaHhVcjRGUUtSdlZWQUwiLCJtYWMiOiIxMDE1YTFiNGE4YTMwZTczZDBlMTkyNTMyNjE4YzA2YmI4MmNhYzJiZjkxOGMwZjNjZGI1NzU1YWQ2ZWFkOTZkIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ik1wZUFYUFpSNVptUng1bEtWTUNhUkE9PSIsInZhbHVlIjoib3hLRmUrTkZBa3NYZUFLT0tPeVE3MHZEWXkyaVZXSjZqNkZsQXA2R0NZVXY3WkRxbmRJZndDTE1hbWIrM1RHdkhKYjRDSitxV1pqUEl1N0Rrd2J5RzNNUm5vM3k2b0RxZ0lQY0ZCV1BnSEU3eXBNTGxKWWR6SWdjcmVPeHM4NXo4TjRCc0EwQTlCU1ZsWG1IZjYvM0VRYU1XcnhLcG9ZZFRLTjl0TTI4ZjdJTWxqUTdjQ3hFRHJDNnBsZytkajBGcG5qRHcvUTN5aE1XRFB3R1JwM0ZzRndYcElaZlc0M1ZyTS95U1JCYjhXdUhlZVgyZjJBd0FtR2dFbEw0WS9GSFRDVDYwcE02RVVxd3hqak9qQVp4SGxoK3U2a0dZYmNNbjVuMVNQcFhlM2c2MmZkc0dDVnp5SjgrY1Q3ZGRXaWZiM2Z0TlFRK1AwbklqazQ4R1RRVS80bzc1aE9mVTk3QzRhWDQ2QnNuMUhOWWFpMVkwTlNxc3QwUEpzdDZVTWV0RWxvL04wenVCM1ZhNXVPMnpFdFhtZUF5eTBmM0NwdEx2eEtWaDdxZkFjcnFrV0V4YmpNTjhwNkE4blIvV09qaXd5eDhndkhKWk9QelRadlVVZmg2VGYxQVZQbDBlbFh6YXRhZVZ4a3A0Y0hwVDZhaE4vRUdnNmxRWit4LzlydkwiLCJtYWMiOiI0OTJiZGI3YjMyNTUxYzc1Njg1NjRiZDgyMTgyZjQ1OWE3MWRhODYyODhmZDBhN2JlYThhZTFkMjE5YzkwMmRhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2096115695\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-727776005 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G4oUC4dvL91DvSdXtTSUa87JqfM4sfNNzjOiK6ns</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">16z2m35FIhU8zNuOqkDvqLO3UyIjTT7e4c1Y0Zqf</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-727776005\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1991213261 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 15:57:58 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjZtRUlDQVpYcUpjZjFUQ1lscGhvRUE9PSIsInZhbHVlIjoidy83UUJBRVloRHQwTXlhTjFRVmk3VnliYWxvR2wxQmtVVDlDR0VUMktVeWxEb2hmVW1DRVg1TUxDaDRQVVlndFd4VkluTWlWdWFROUZaOEJOZ3FRNEovZThWNVJ1bnVSWWpGcnB3Z243OC91MWhvWWlaUWpJd0RZMU44VStXaVMzM3FVZXU1WlYrdTJhWm8weUJMRHdJeTQySE5kZFF4czlZRkhxdmN6UW9nUW9uS05PcGtGY3E4aHNMekNObHF2dkpZS1REK2I1ak9xUkp2V0NNQmxTV2xIR1g2OTRZYVlUcU5tUlI0bXJWcndjRXZ2TlIvakh0S1kyWnpNNThxZkkycVJKSGljZUVQdFd6UThqRndLQU9nNWxPRzJqZmdQNVZPMFVYUlhRWFBWa1BuZWtKeFJBQ3JPQ1hwK0VFaitBbHVDMThYZUxFTFVCckk4YURyUVFwRTEwYTZkQWJhRW1DVk0xekk2ZEJSbDU3M1VDZkJUeThEYnFEWkVpVU5EcVhmTmJZTE5qT0xOcU1WL0c4Y05qNkJOeWpXWllnKzdrYmdtOUR3Y2RkWjg0N29UYUx4WWUyL3Y5RTVKSExJbXNBQm41RlhuelVaWWpzZU1RelBkbkg5Z1pJZWxic2RaUllIako4d2dLdmZ2eXVqeldDb0JOcEppbW9tUjJJYUkiLCJtYWMiOiI3Zjk1ZDNlYTg5ZmYxYzBlMzZjNzViY2VkMGI1MjZlZWQwNmNjNTJhMjMxZmI5MjgwMmY5MDVlOGRjODk1ZTg5IiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 17:57:58 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImdKS3dYL2MyVDhxNDJQTUZ0TWkvYUE9PSIsInZhbHVlIjoiZVBhcWh0TkU4dTV5cmtkc2I1WWZDZHVGbW1EYnplTDVYSWVnam9JKzQ3cmdqa0JPOHdXL0tpdDRzSmRmZzdQdnFhQXVlaDFBUlRQRzU2K2JTdEJ2Nmp5U1c3MkFMQmJ3RGVyOHdoZkhhK0RaTEF0UkJMVFZveHFpVGV5enVyZnFwYnpUMyt2WWE0S1BObmlCeUQxRWlGcGhMYWx3V2RzSHdqNnFRN050ZlNYenJ1MGprekZybUhjS096OVBPM1Rtb21sbEdYYS91NWZhNjhMNTRyKzA2TzhZZDNjT09raEJ5K0RMSWxJTzNBRS9wSHg3dXNHYy9pejZEUSswUlNZQzZTR3lrUGlhcEF0dkFKOCtsRGtpSThaOFJVeUx2dlREQzNUbDJnSHlXN1FpK2lzd29GYmhVdzNJaW0rcEpyWjVYYS8zbmN3TFk1V3FQOU56ZHF0SVNBcFpoZHZzOWpNSkJFYXRjMGhiUFovNzUwdEgzelJObDhyb25jbXhEM2RmM1B0UVlKZnh5ZmI5VmlzVWdtOGtNenl5NkZsUlMwWVJBNEorSk9uQTdGSGEzQ09rY2ZUc0N0VmluN2RUYWY1Z08rbEdhL1poL3FndTJxYlNhN1J3VjFNR1ZSdGE5MVozTkh5L3VzUG1wTjJNdTlKUFlxdzVrSktRMTFEMlJJN0giLCJtYWMiOiJjY2JiYTgwZDBkNmUwNjM4Nzk4MjIzNWVkMmU5OTJjMzI0MDcyYTZlNjE1NjkyNDU0NDkwZDljYTEyNTA2Y2I4IiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 17:57:58 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjZtRUlDQVpYcUpjZjFUQ1lscGhvRUE9PSIsInZhbHVlIjoidy83UUJBRVloRHQwTXlhTjFRVmk3VnliYWxvR2wxQmtVVDlDR0VUMktVeWxEb2hmVW1DRVg1TUxDaDRQVVlndFd4VkluTWlWdWFROUZaOEJOZ3FRNEovZThWNVJ1bnVSWWpGcnB3Z243OC91MWhvWWlaUWpJd0RZMU44VStXaVMzM3FVZXU1WlYrdTJhWm8weUJMRHdJeTQySE5kZFF4czlZRkhxdmN6UW9nUW9uS05PcGtGY3E4aHNMekNObHF2dkpZS1REK2I1ak9xUkp2V0NNQmxTV2xIR1g2OTRZYVlUcU5tUlI0bXJWcndjRXZ2TlIvakh0S1kyWnpNNThxZkkycVJKSGljZUVQdFd6UThqRndLQU9nNWxPRzJqZmdQNVZPMFVYUlhRWFBWa1BuZWtKeFJBQ3JPQ1hwK0VFaitBbHVDMThYZUxFTFVCckk4YURyUVFwRTEwYTZkQWJhRW1DVk0xekk2ZEJSbDU3M1VDZkJUeThEYnFEWkVpVU5EcVhmTmJZTE5qT0xOcU1WL0c4Y05qNkJOeWpXWllnKzdrYmdtOUR3Y2RkWjg0N29UYUx4WWUyL3Y5RTVKSExJbXNBQm41RlhuelVaWWpzZU1RelBkbkg5Z1pJZWxic2RaUllIako4d2dLdmZ2eXVqeldDb0JOcEppbW9tUjJJYUkiLCJtYWMiOiI3Zjk1ZDNlYTg5ZmYxYzBlMzZjNzViY2VkMGI1MjZlZWQwNmNjNTJhMjMxZmI5MjgwMmY5MDVlOGRjODk1ZTg5IiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 17:57:58 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImdKS3dYL2MyVDhxNDJQTUZ0TWkvYUE9PSIsInZhbHVlIjoiZVBhcWh0TkU4dTV5cmtkc2I1WWZDZHVGbW1EYnplTDVYSWVnam9JKzQ3cmdqa0JPOHdXL0tpdDRzSmRmZzdQdnFhQXVlaDFBUlRQRzU2K2JTdEJ2Nmp5U1c3MkFMQmJ3RGVyOHdoZkhhK0RaTEF0UkJMVFZveHFpVGV5enVyZnFwYnpUMyt2WWE0S1BObmlCeUQxRWlGcGhMYWx3V2RzSHdqNnFRN050ZlNYenJ1MGprekZybUhjS096OVBPM1Rtb21sbEdYYS91NWZhNjhMNTRyKzA2TzhZZDNjT09raEJ5K0RMSWxJTzNBRS9wSHg3dXNHYy9pejZEUSswUlNZQzZTR3lrUGlhcEF0dkFKOCtsRGtpSThaOFJVeUx2dlREQzNUbDJnSHlXN1FpK2lzd29GYmhVdzNJaW0rcEpyWjVYYS8zbmN3TFk1V3FQOU56ZHF0SVNBcFpoZHZzOWpNSkJFYXRjMGhiUFovNzUwdEgzelJObDhyb25jbXhEM2RmM1B0UVlKZnh5ZmI5VmlzVWdtOGtNenl5NkZsUlMwWVJBNEorSk9uQTdGSGEzQ09rY2ZUc0N0VmluN2RUYWY1Z08rbEdhL1poL3FndTJxYlNhN1J3VjFNR1ZSdGE5MVozTkh5L3VzUG1wTjJNdTlKUFlxdzVrSktRMTFEMlJJN0giLCJtYWMiOiJjY2JiYTgwZDBkNmUwNjM4Nzk4MjIzNWVkMmU5OTJjMzI0MDcyYTZlNjE1NjkyNDU0NDkwZDljYTEyNTA2Y2I4IiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 17:57:58 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1991213261\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-849313201 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G4oUC4dvL91DvSdXtTSUa87JqfM4sfNNzjOiK6ns</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"227 characters\">http://localhost/bill/eyJpdiI6InNzWlhKbTYwWndnMXhFSnlrbWhnK0E9PSIsInZhbHVlIjoiaWhTMHpYT3kzU0JqQTdHK01oV1JvUT09IiwibWFjIjoiODg3MTAzOGJiNDk1MzcyODU5MWZmZTNkMTdhYTAzMDJkNmI1NjlmODFkYzk1MzA3NTljODFhMzc1MTkyMzg5YiIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-849313201\", {\"maxDepth\":0})</script>\n"}}