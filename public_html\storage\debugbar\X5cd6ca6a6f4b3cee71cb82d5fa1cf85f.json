{"__meta": {"id": "X5cd6ca6a6f4b3cee71cb82d5fa1cf85f", "datetime": "2025-06-26 16:01:49", "utime": **********.314388, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750953708.850598, "end": **********.314405, "duration": 0.4638068675994873, "duration_str": "464ms", "measures": [{"label": "Booting", "start": 1750953708.850598, "relative_start": 0, "end": **********.249615, "relative_end": **********.249615, "duration": 0.3990168571472168, "duration_str": "399ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.249624, "relative_start": 0.39902591705322266, "end": **********.314407, "relative_end": 2.1457672119140625e-06, "duration": 0.06478309631347656, "duration_str": "64.78ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45663480, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01286, "accumulated_duration_str": "12.86ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.280131, "duration": 0.012240000000000001, "duration_str": "12.24ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 95.179}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.3012679, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 95.179, "width_percent": 3.033}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.307453, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 98.212, "width_percent": 1.788}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "G4oUC4dvL91DvSdXtTSUa87JqfM4sfNNzjOiK6ns", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-820725540 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-820725540\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-851230818 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-851230818\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-962670380 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G4oUC4dvL91DvSdXtTSUa87JqfM4sfNNzjOiK6ns</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-962670380\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-813430739 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1ap6d1q%7C2%7Cfx3%7C0%7C1998; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clsk=p74v85%7C1750953557602%7C82%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImdVZFdZVGZySFRlZHFHV3Bmc3piVlE9PSIsInZhbHVlIjoiMWNMc2pEL1pCb3N1N1ErdllRYk1qa3AvS0Y4UGk1djcyR01iMjJTVlNEemx2L1JlTzRCVXdNTU12NmZwaGtDYk1TVmkrVHd5R1YzdWpTZy9pa0xBeGhDdVFOcHZTSHpabjduSUlMMW02V211d2VRU1FGVXBpUWxaZExEdjFzOGRXWHF2RXY2SkFQc2YwUDROZTVFZlduckpDR0ZZaVVnVkI3b0pLaVMxcjhFQkJPZWgvalBXS0FQTDNueEVLZlpzSy9UK2JkQ1pSMzYvTjBRbUdmZ04yMFhLL21qNEhzSXg0R2czS3VCeXBqVFkwTGZGOG9MRlQ2aDFCaWllVXh6aGpLOVdWN0ZpNFJ5ZUxXMlpjS3JncDFEQ05jVUdxdW05QlVvRmJ2c1M2NmNKM0hpZGtPRHdsOWdkdWs4a2lpZk95TXBqcW5sYnNzZjJ1NlFTaUFtWlAxcHVGb0cvVWJMd3daYXlQM050d1FKTnBWSDNYL0JNRmpScmJ5ZHJCamFxSU9DY25SbGtlMTlvTENnQUJodHRIVW9DSXRZa29oaUV0NGlXUHF1bGlVd255aCtHTFpWQUhVa3dKcDdYaEl1U0ZKUXJHLy9uZ1lja004T1I5RXVlcUFsakEvejZmVGdnSG01R0pxTjgyQWhTSzRUNW9xQlhVbTZ2QjFPbmptenEiLCJtYWMiOiI5NmJlMGI1YWM4MWU5Y2MyYTMyN2IyMDIxNzI0ZDBiZGEwM2ZmNDdhMDhhOGZjNTllNDc3NmVjZjk4MTg5Zjk5IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ik16dEo5K3FBL2xoSUhkeVNBb0NzdkE9PSIsInZhbHVlIjoiQ0orM1BGbHdiVVh0V3BHWFpGVXpJc2RrSVdYR1B2cHJTMnNLY1VrUXhKWmZSYm0yZGFoN1hZYm4ybHFyQTN1SUtKY0JYd3ZCcW1CNGUzZE1sdnIwQjgya2xxSzZKQ0MwOUNFMG5RMnlmVkc5bnRrRERPVkJZUzdvRmFwSWpqZGE5V1RFc3piRm42U1MyajlqRmwrYzdDQTgvU2pwUGs4OFl2cjBkNGhnVjE4WU1rVjZNRXhWYWo1WEwyaER1TnAzdG9hakVBdTlCM1c5S3pNckhOUFBJUzUzNm9uZUszUDBjdGt3S094bUVWaTlkN0k4YXBzVzYwN25XU2pPRWl1OGxZZTNnbitTMkhpdnp5WUlseWxhTkQyRmtQWjZrYnIxV2wxbGJHTVh5SWxONDFDTnR2OE9nTlR4WDdOQzFCK2hCdmNLWm5ndVdqUWp4MloyR1Jla0FRbnppU2YraHpTTm1GY2M2Z1FUUE9rU2dubGFFc0xnZE0zcVNpRlNKbFNnM3VBLzdvVGQyUTUvUEIrVGdFQ05nR1U0aWNPcTVROFdVUEZsQkIrNVQ3VDdqa2h6RUVwV1JwbUNCVk02TkhJR1JSRVhIMVk5QmozN3p1K0NaajRXYjh6MHJjcHFXTFVEZCtqVTlFSkk0enNFSXE1VjRYTkJHZjIzRmIybzhJRDciLCJtYWMiOiI1MTIzMWYzYTMxYTZiMDUzNDc5NzRiYTA3YTY0NWYxNzVjY2MzMDJmZGFlZWIwNWVmM2YyYWQyYTAwZDU1MTc4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-813430739\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1689618946 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G4oUC4dvL91DvSdXtTSUa87JqfM4sfNNzjOiK6ns</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">16z2m35FIhU8zNuOqkDvqLO3UyIjTT7e4c1Y0Zqf</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1689618946\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1320526171 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 16:01:49 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImZhM2pZTWtqR0VnY0phTHpvNUlWdmc9PSIsInZhbHVlIjoiMkU2MXNRdy9EOVlMZUFsQ2kvSEJUVFgxcVpOSVJUWnByYnVSOTMra1lnWjQwSStvZnQ2cTVub3hLVFZjdFJLUTBqa29aTWZkbElMUURUdENmRGR5ekRKUVNCWkJMNTQrZXFHT1lkSnFzZGl3Vld0T2JCTXBSY0dtSCt2Myt2L1ZWYWFtMkZYVFdTSE82TkVVaEZpNFhEaHJOcldPcEJtOHJ5L3B1MDg0RWoybWF2S3Z6QmgreVQzQVExSndVZ0JhQjhQTU9JcUVITHJOalFZQzFYTWNUeXJ0NXBzSjFkQ2l2czdqOGZnZkJUS0ZJQXRkdTBNdlYveThjeStUQnpFaTFKN0VoUzIvYWhDN1hCVEFwSmhzQ3AvWTdLWjQ2V1FObVZWUHQ3Znk3RTZYeS9XOWlpdndRR2U0WnpoRUlFSlVTRzlWUUJ4NUZXSEdyME94bStxTXNNWnRiT3o0L0JqY0VUNUFoNjlQckpCUHZVK2lZbG9pbEZSTlhXR2ZBZ1NCZzlDdlVVOHhCMS9yakJOSmlTT3QxNE9NSjBIYzFRVzJhRTZmNFdBZmhtc1kwUEF6dS9rcmV3cGdGazlGT3pnQVJVK3M4SXErRUVkSXZKdEY0a2tEbm5HUDBPS2JDa1RPN0FIa1pWcTZueHNPYU9jYWRWcFlTNnFFM0diZ3hHWlgiLCJtYWMiOiIyM2NhN2ZmNmM3OTkwMGQxNjI3YmVkZjk1ZmZjNDFjMDE3MzE2MmY5MTMwZmU5OThmODcxZmVmZWI3ZTE4NzZkIiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 18:01:49 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImNvVU44bTBnaGtlQXRGaTRiakYxN1E9PSIsInZhbHVlIjoiaDY2NlVLN3c4NFZTVVRkVDQrbEEwT1FBWmREdDVqU2F5MjNpWlVSR2MrU1IycmtlOEJDQmdYQXVraTdtc2ZRNW5uQ1ErL0lvRGQ4S0FKbWhUZGFzbGY0U1RhY2hLODBYOC9HcDNGOUh6U1h1dmR2ZGFPbzJUUk5lQnYycHhjbXdOM3M5S0ViMEhzdCtsRU5IMkR3bHJ5OUQ3L2ZhT2hhWEJjOU9xUklmTjBWNXIvNlVUak8rK0JORGFlUjRzR0hjMXpVb0ZLNUNTWnVMajd3ZnhCUkt1RWo1VUQ1TFFmck0wc0dOQ2RmTHR3Zml1UUxQcGordWhYQU5ickp6RlAwZGpmbktGUXNKN21tNHpDSWl4dnNNZCtuZzVDRldoTENTUHhYU0RSdENDYkJweERJK1pQaFRmOGNscXZiWjlReHpQNTFaMjJnZHJialZvbVc4TFpHM2lFU2hLUkcvVGJiQ2YyalA4V1ExMWFRZEpsWTlrQlZWWWJIYVo1VnRKUUxRemQxUVg3VUF6NEhlNml3RVpOY2l3MWRneXZwcWRzT1JVdGV4LzNQOWx0OGJCKy85RlJEeGh6eTF1U0QwUDVIcFIrbnU0NmpIb21ZYkppUmJPMGFGUFV3RXlRN0Z4aFdkQ0xzV21pM01aMnl0K1lYd3VPY1BqQ21jZ2pxd2FCRSsiLCJtYWMiOiJkMmQwZGY4NmEyYTFiOTExMzRiNmQyNmY3MzRkYzYwOGEzNGYzN2RkNjJhNTg4OWIwNmVlMWVjZjE2ZWUxOWFmIiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 18:01:49 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImZhM2pZTWtqR0VnY0phTHpvNUlWdmc9PSIsInZhbHVlIjoiMkU2MXNRdy9EOVlMZUFsQ2kvSEJUVFgxcVpOSVJUWnByYnVSOTMra1lnWjQwSStvZnQ2cTVub3hLVFZjdFJLUTBqa29aTWZkbElMUURUdENmRGR5ekRKUVNCWkJMNTQrZXFHT1lkSnFzZGl3Vld0T2JCTXBSY0dtSCt2Myt2L1ZWYWFtMkZYVFdTSE82TkVVaEZpNFhEaHJOcldPcEJtOHJ5L3B1MDg0RWoybWF2S3Z6QmgreVQzQVExSndVZ0JhQjhQTU9JcUVITHJOalFZQzFYTWNUeXJ0NXBzSjFkQ2l2czdqOGZnZkJUS0ZJQXRkdTBNdlYveThjeStUQnpFaTFKN0VoUzIvYWhDN1hCVEFwSmhzQ3AvWTdLWjQ2V1FObVZWUHQ3Znk3RTZYeS9XOWlpdndRR2U0WnpoRUlFSlVTRzlWUUJ4NUZXSEdyME94bStxTXNNWnRiT3o0L0JqY0VUNUFoNjlQckpCUHZVK2lZbG9pbEZSTlhXR2ZBZ1NCZzlDdlVVOHhCMS9yakJOSmlTT3QxNE9NSjBIYzFRVzJhRTZmNFdBZmhtc1kwUEF6dS9rcmV3cGdGazlGT3pnQVJVK3M4SXErRUVkSXZKdEY0a2tEbm5HUDBPS2JDa1RPN0FIa1pWcTZueHNPYU9jYWRWcFlTNnFFM0diZ3hHWlgiLCJtYWMiOiIyM2NhN2ZmNmM3OTkwMGQxNjI3YmVkZjk1ZmZjNDFjMDE3MzE2MmY5MTMwZmU5OThmODcxZmVmZWI3ZTE4NzZkIiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 18:01:49 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImNvVU44bTBnaGtlQXRGaTRiakYxN1E9PSIsInZhbHVlIjoiaDY2NlVLN3c4NFZTVVRkVDQrbEEwT1FBWmREdDVqU2F5MjNpWlVSR2MrU1IycmtlOEJDQmdYQXVraTdtc2ZRNW5uQ1ErL0lvRGQ4S0FKbWhUZGFzbGY0U1RhY2hLODBYOC9HcDNGOUh6U1h1dmR2ZGFPbzJUUk5lQnYycHhjbXdOM3M5S0ViMEhzdCtsRU5IMkR3bHJ5OUQ3L2ZhT2hhWEJjOU9xUklmTjBWNXIvNlVUak8rK0JORGFlUjRzR0hjMXpVb0ZLNUNTWnVMajd3ZnhCUkt1RWo1VUQ1TFFmck0wc0dOQ2RmTHR3Zml1UUxQcGordWhYQU5ickp6RlAwZGpmbktGUXNKN21tNHpDSWl4dnNNZCtuZzVDRldoTENTUHhYU0RSdENDYkJweERJK1pQaFRmOGNscXZiWjlReHpQNTFaMjJnZHJialZvbVc4TFpHM2lFU2hLUkcvVGJiQ2YyalA4V1ExMWFRZEpsWTlrQlZWWWJIYVo1VnRKUUxRemQxUVg3VUF6NEhlNml3RVpOY2l3MWRneXZwcWRzT1JVdGV4LzNQOWx0OGJCKy85RlJEeGh6eTF1U0QwUDVIcFIrbnU0NmpIb21ZYkppUmJPMGFGUFV3RXlRN0Z4aFdkQ0xzV21pM01aMnl0K1lYd3VPY1BqQ21jZ2pxd2FCRSsiLCJtYWMiOiJkMmQwZGY4NmEyYTFiOTExMzRiNmQyNmY3MzRkYzYwOGEzNGYzN2RkNjJhNTg4OWIwNmVlMWVjZjE2ZWUxOWFmIiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 18:01:49 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1320526171\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-80448895 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G4oUC4dvL91DvSdXtTSUa87JqfM4sfNNzjOiK6ns</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-80448895\", {\"maxDepth\":0})</script>\n"}}