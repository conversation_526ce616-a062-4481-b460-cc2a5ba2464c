{"__meta": {"id": "Xdca363143171f3970fa71104f0c73d18", "datetime": "2025-06-26 16:03:27", "utime": **********.57011, "method": "POST", "uri": "/warehouse-empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.15755, "end": **********.570124, "duration": 0.41257381439208984, "duration_str": "413ms", "measures": [{"label": "Booting", "start": **********.15755, "relative_start": 0, "end": **********.520168, "relative_end": **********.520168, "duration": 0.36261796951293945, "duration_str": "363ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.520176, "relative_start": 0.3626258373260498, "end": **********.570125, "relative_end": 1.1920928955078125e-06, "duration": 0.04994916915893555, "duration_str": "49.95ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45879960, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST warehouse-empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@warehouseemptyCart", "namespace": null, "prefix": "", "where": [], "as": "warehouse-empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1627\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1627-1637</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00228, "accumulated_duration_str": "2.28ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 17 limit 1", "type": "query", "params": [], "bindings": ["17"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.552073, "duration": 0.00172, "duration_str": "1.72ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 75.439}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.563223, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 75.439, "width_percent": 24.561}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "jEiKRXRaeQw7kpqtZmXNWu3bpmEnaxKAnNI8mVhc", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "17"}, "request": {"path_info": "/warehouse-empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-717605568 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-717605568\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1153186645 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1153186645\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1618765906 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1618765906\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">jEiKRXRaeQw7kpqtZmXNWu3bpmEnaxKAnNI8mVhc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1843 characters\">_clck=1lqc4d8%7C2%7Cfx3%7C0%7C2003; _clsk=1h4itwx%7C1750953725629%7C12%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InpjWVlhNVBwc09QK0FkTXpXUHZRMWc9PSIsInZhbHVlIjoiRHBsOVFVdEJWYUNSaHo5Ylg0ZUtCajhFY2ZTUjMzazN4ck1HK2JLbnhKaGFXL1VWaVdRNjF5RDQzOEphT1B3U1d0TGxYUzRSK2hmdzRndDVZWXBtUkRMQU9WRS9rWkFRNVNHdkVBNWNjcDR2cDVBT1Z5SGJoMldwUjB5cGxFeU5LTnVBYVFST1UrQ2hLai84VjJoVTVmaDBiUlROK3dmTHlJb0JyODV4QnlsQ2F2RW5TcUtSRFo4VnJwMWdiQnZJQThzZE13YVlLSjlmNDJDNFZDb2xtT1MvejdIeWlqbitjSTJyS0Y1N1Vod0FudHI1dW5TeENGN2lhTHZPWGNxSUYwbEN6eWFYZ3FOUTd5aG01V0ZZL1ZGZm44aEVndmJSVUM2MmYxb2xSZnFNbWZ4OC9hbmVad3B3a0FSeURXb2NGS1p0RnUraGtiYjFmc0NVMm5BOVBzUmJ4SFJVelc3UFJyT3BuVENvWVp6eitXS2dCaXV0dGZIS2hINHlQVTJBQnNoRnVkYVdJT2xDeURYelhMQU95bmRKYlFtTFpwSXlTUDJpRWZNMjM5NHY5c3E4b2g1S3d0TVFLSmpuMWZDM1VYRXFQK2ZPd3cvYUs0c3lvMkhSem9kdktFcVhxUW15Y1JzSTl1VnRlamlZdHl4M1lodFFVeWFZNTVvZUtHeWwiLCJtYWMiOiIyY2QxZTAzZjk1ZTAzYmFhNzA2NzZhZTY1MjUwYWU1ZGJmMWNlY2I1ZmVlMGFjNTAxYjg5NmNhOTNlOGY0OGJkIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImladjFaV21yR2IzMTYybHhwbDVDSlE9PSIsInZhbHVlIjoiTjk3aDZKWjQ2MVI0Q1NxcjA4OG5zK1hXczVIM00ya3plRjFlQkIvR2tSUk1iUXY3VkUzcmlOUHFpdldUa3hWc0lkeEEyTW10M2dwTzc5T3UzNDBoQ1FwZ2x1NXU1K013aW5acVVKcGdmY08vNTlubll1a1ZkQXovUU9iMkRwcEZhOXpsQWhxbEJSMDNEUHJ5cGNrQlV3c2N1YmxsYXlhUUthQmF5RHhnalc5NlRjaWFmb2p1VTJWdHFuZjZXeHhXOEM4WXBSV3d2c3Y2bkQ1ckZSRTlxMTNMSzNWUkpMYVBkRU5CYTdxV1RjMWE5ZUx3cm9USC9XS3A3cEZQQktGTjI3TDhza1ZtSWJyckRTR29tWXQ5MGphTmRMR0Rtc1pucWEwc1NSVGxmUDRUcnA1MlNKOTNTdWdPdGxseVRMdlhZYURjYVFDWmNtSmV1THU0SlJWeEVQTkQrMHJaRngvWjkvdkxRd2NNUzlGQTd3aElhS25oMmJFUDhTTjVMNFBURDVuWUJiVk9UNytpOUlUeE05dWJ0dm1xNktONzhlM1J1UFJDM1p4bW16RjJaak5YbERVWFY4bkI0NkRFSkI3SWhIOG9KWlpqaUppK016cFp5MTJEMWZac0M0OTFqVysxODdCQit1ZTlpb3l0SWJMSlNnaXI1N1pRRWt3KzFNWXMiLCJtYWMiOiI4NzQwNjYzMzk0YTkyM2I0MDdlNjhlNGIxZjQ2MzJlYjI2NDk1YmZkYjAxYzdiZjZmMzdmNjRhNjRiY2IxMTU1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-200904247 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">jEiKRXRaeQw7kpqtZmXNWu3bpmEnaxKAnNI8mVhc</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PSbLK6QAWuUzu3JpIF2TDtYQ1L7TOqCcejFUVX3V</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-200904247\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-412190243 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 16:03:27 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik9Bb0xJdWl3WW5LbHZUWnBReDg4TFE9PSIsInZhbHVlIjoiejk1ZENWT1hBelpIQWxaV0FZUzZaRG5kKzMrOS80aThJVURUMXBYbnZOWHAva1hTWmVpZHkvaEE0cUdXN1pJN3JQUXhsWkg1cnUrd0ltK1RqZk1UNWlnQko3SnJYZkdrOVB0R2I0NDU0UWZIUXg0cmxLYlh1RzFId2JHcEE0MHZXRlU2UG1mcTMxVS9wdmtiQVI5b2tYZFJ5WWl1TmpUWGZTOE5NZk5rU2RPYkY1RzB3b0dLNm5qc3hQT0RyTmRoczlSWThaZzRxMmVnTm1VVHN6NGZTaEZhNENEaDAyU0liU3lPRHlYMVA4VDZ3cmRZZ3BVK2FDSE0wa2d1Y2xKQmxJVXp5ZlJzeVc4NW9jbjJsZlNnUGZJQ0xoeUlvejNCWEhVVEtNUHZ4RVlidEtwdXpkTXg1NkJpTlhaQWpMTm9veUU0UUlGc3RaNDJtb1V4Mm9na1JEOVIxSG43alRBL1RpVml3WVJiWnBVWWNZcUMrMzFPNHJKVWVhbytubkRhb21IdmtkWnhNMzU2U3B0TXFJY3NPN0Z6cXcvaXRwUlBhaVJmRlRLN1NZVGh6SzZqRmxzaVRaMmVzNnU4aDhGUjd2QWlQY0cwTmJ4V2FOZjM5Ui83azZNbjRobUdyanpoUjlhR1o0RHUxTndqSUtCc1h3ZEUwMjA4U3lhd0tEV0oiLCJtYWMiOiJlZWU2ZGYwNWRkNDExMzI1YzllODE4ZTVlNDE4NDBhMTcwYmJkZDk3ODUxYTM5MmI3ODRkNmVjMTkxMTBlNzYxIiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 18:03:27 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ilg0ZGxIUGZJd1BobEQyVjRhQkxDbmc9PSIsInZhbHVlIjoiQldnUXNqcFpmT3cyMW52RnNZMURCSnN4TFY0N3hsWVFuaGNlaVhkYjc5OUNSRzRabkxabW5BcUx3cXFiRlhhZm9rSm1NemkvVllybWZ3U29lcDNhTWFsV1BCMWtNT3k2OWpqNWRNb3NiRnlrTmtkSnBVV3laSGJBa2hEanYyeTNFeW1vTU1XbUdhNmJZMU9jL1Bob3FySVhZZ1lydHNPOXorRVFWejQxMFovQzJQZ2Z5MkxvNkNEUkFQNXFQVE9DMjlPS3dqallVY2ZwSnBrWEtLMzl0RmMzQWRLaGoxTUdaWEh2VUV0aTl0ekF5TEZnaWlGRUNuYnJzN2VjR0RyVXVaTW9KV0JzdDlpaUFWSVQwRTBGN004OWpDK0xvRWtDNGVxQ2dCT2FvaWtLcERQSVFLWFZicEtlWkVOK2JwTk5hREpvMHZITy9rTXRZVmM0VkkyMmZ1Qi9wcmtyTk5EbXNZUkI5NnlBYUg5R2dMejBxSkV5akQxaWlvK0dBV1Vxd3lNTDZqYU9vVDZ0b2c2NlphMHorVGVMenQ1bm9QOGY5dXVDRm16VFRoTm5LRndHSEQ5Zk1oSmxpenV0R3VqT2xueXc2Zk8wNTBBMzVKdjFvWnltaVdUUGpqYVJVNkhtWGtMd3RBVGsrbG5OTWttRjJKb1diV1hBYlowY054eVAiLCJtYWMiOiJlMDg4NGJiZmVkN2EyMTQ1YjlhMzM0NjEyYjJkYWExNmZmMDVjMWJlNmQ5ZDc2MDg4ZDM5OWZkNjUxNGU0OTRhIiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 18:03:27 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik9Bb0xJdWl3WW5LbHZUWnBReDg4TFE9PSIsInZhbHVlIjoiejk1ZENWT1hBelpIQWxaV0FZUzZaRG5kKzMrOS80aThJVURUMXBYbnZOWHAva1hTWmVpZHkvaEE0cUdXN1pJN3JQUXhsWkg1cnUrd0ltK1RqZk1UNWlnQko3SnJYZkdrOVB0R2I0NDU0UWZIUXg0cmxLYlh1RzFId2JHcEE0MHZXRlU2UG1mcTMxVS9wdmtiQVI5b2tYZFJ5WWl1TmpUWGZTOE5NZk5rU2RPYkY1RzB3b0dLNm5qc3hQT0RyTmRoczlSWThaZzRxMmVnTm1VVHN6NGZTaEZhNENEaDAyU0liU3lPRHlYMVA4VDZ3cmRZZ3BVK2FDSE0wa2d1Y2xKQmxJVXp5ZlJzeVc4NW9jbjJsZlNnUGZJQ0xoeUlvejNCWEhVVEtNUHZ4RVlidEtwdXpkTXg1NkJpTlhaQWpMTm9veUU0UUlGc3RaNDJtb1V4Mm9na1JEOVIxSG43alRBL1RpVml3WVJiWnBVWWNZcUMrMzFPNHJKVWVhbytubkRhb21IdmtkWnhNMzU2U3B0TXFJY3NPN0Z6cXcvaXRwUlBhaVJmRlRLN1NZVGh6SzZqRmxzaVRaMmVzNnU4aDhGUjd2QWlQY0cwTmJ4V2FOZjM5Ui83azZNbjRobUdyanpoUjlhR1o0RHUxTndqSUtCc1h3ZEUwMjA4U3lhd0tEV0oiLCJtYWMiOiJlZWU2ZGYwNWRkNDExMzI1YzllODE4ZTVlNDE4NDBhMTcwYmJkZDk3ODUxYTM5MmI3ODRkNmVjMTkxMTBlNzYxIiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 18:03:27 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ilg0ZGxIUGZJd1BobEQyVjRhQkxDbmc9PSIsInZhbHVlIjoiQldnUXNqcFpmT3cyMW52RnNZMURCSnN4TFY0N3hsWVFuaGNlaVhkYjc5OUNSRzRabkxabW5BcUx3cXFiRlhhZm9rSm1NemkvVllybWZ3U29lcDNhTWFsV1BCMWtNT3k2OWpqNWRNb3NiRnlrTmtkSnBVV3laSGJBa2hEanYyeTNFeW1vTU1XbUdhNmJZMU9jL1Bob3FySVhZZ1lydHNPOXorRVFWejQxMFovQzJQZ2Z5MkxvNkNEUkFQNXFQVE9DMjlPS3dqallVY2ZwSnBrWEtLMzl0RmMzQWRLaGoxTUdaWEh2VUV0aTl0ekF5TEZnaWlGRUNuYnJzN2VjR0RyVXVaTW9KV0JzdDlpaUFWSVQwRTBGN004OWpDK0xvRWtDNGVxQ2dCT2FvaWtLcERQSVFLWFZicEtlWkVOK2JwTk5hREpvMHZITy9rTXRZVmM0VkkyMmZ1Qi9wcmtyTk5EbXNZUkI5NnlBYUg5R2dMejBxSkV5akQxaWlvK0dBV1Vxd3lNTDZqYU9vVDZ0b2c2NlphMHorVGVMenQ1bm9QOGY5dXVDRm16VFRoTm5LRndHSEQ5Zk1oSmxpenV0R3VqT2xueXc2Zk8wNTBBMzVKdjFvWnltaVdUUGpqYVJVNkhtWGtMd3RBVGsrbG5OTWttRjJKb1diV1hBYlowY054eVAiLCJtYWMiOiJlMDg4NGJiZmVkN2EyMTQ1YjlhMzM0NjEyYjJkYWExNmZmMDVjMWJlNmQ5ZDc2MDg4ZDM5OWZkNjUxNGU0OTRhIiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 18:03:27 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-412190243\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-119587233 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">jEiKRXRaeQw7kpqtZmXNWu3bpmEnaxKAnNI8mVhc</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>17</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-119587233\", {\"maxDepth\":0})</script>\n"}}