{"__meta": {"id": "Xae7ec4445a6092aabadcb8642b50ac5e", "datetime": "2025-06-26 16:02:05", "utime": **********.680473, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.234808, "end": **********.680489, "duration": 0.445681095123291, "duration_str": "446ms", "measures": [{"label": "Booting", "start": **********.234808, "relative_start": 0, "end": **********.614048, "relative_end": **********.614048, "duration": 0.3792400360107422, "duration_str": "379ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.614057, "relative_start": 0.37924909591674805, "end": **********.680491, "relative_end": 1.9073486328125e-06, "duration": 0.06643390655517578, "duration_str": "66.43ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46188872, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01479, "accumulated_duration_str": "14.79ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 17 limit 1", "type": "query", "params": [], "bindings": ["17"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.641671, "duration": 0.01384, "duration_str": "13.84ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 93.577}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.664997, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 93.577, "width_percent": 3.584}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (17) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.6709218, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.16, "width_percent": 2.84}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "jEiKRXRaeQw7kpqtZmXNWu3bpmEnaxKAnNI8mVhc", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "17", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-2138619906 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-2138619906\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-486296824 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-486296824\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1783327931 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">jEiKRXRaeQw7kpqtZmXNWu3bpmEnaxKAnNI8mVhc</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1783327931\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1843 characters\">_clck=1lqc4d8%7C2%7Cfx3%7C0%7C2003; _clsk=1h4itwx%7C1750953722878%7C11%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik1KOTJNeEkvNnNOcEl4cEN4eEtCZXc9PSIsInZhbHVlIjoiTlJLUEhXY3BWa0NLZVVpc3VrSDUxVm5OWlFYaG5GdnJmSmI3WllFbEJLM2k1UXhsa3RGVXF1SVdUeVlIWnFNWGRRQnQ4TW9DRjFzMkdza2M5UzJMVW13MjV5TzloelhTSmlneERCWFI1VERsK21KWTY5K3JxUVR0MmhzOHloYndVL1dtWWlLaVg0N3VZTXBNVUlmaHUxdWFpamQzeEw3M2xTS0lkQ20ydHkwZHdLTXRSM1BFcjAvdFRDcjN6RzFjUDlhUzBNZE5FVzdDc2RXd2ZLc1FobitZekdCTForUDZBSVV2cHA3VUxwckwrbXd3RXlQdE1qcjVqeUpHT21DNHFWeHVnZGRyQldQa3U4ZVR3Z2hnNzdNV0RmR1FVYVIveFozUmxScWV4ZGxXUWNvd3h5OU1lOUZCWXJ6bHFFZVhZZUVKRStYc1MxL1RRaGNUQWM2WDF5dEdDK0UwbzRHSGZ5amdWcmxsUVpqY3g2TjJaaGZFZjJ2dTRwUzcxMVNtOXhiYnRDeThQS1A0b3RsSVFDeU50UDBhOUhtZ1lCOUtxMlRIN3VwQ2pHNG92Zkh6eGpPbXJFMmJvbG03c1RYWTdPL3BtT3RuR0tFQnRjUWdqRDhCdlRxL3JSMVdtQzRjdUFFcFRQSnM2OWF6U0NKMGg0OXAzUCtHb2VPdEtvdEoiLCJtYWMiOiI4ODE4OGI2NmMwMTY5Y2RkM2ZiMTY0ZWU2OWZhYWViY2MxZWNlYzdkODRiNTI4MzgwZjAwZDdlYWI1NWRhMGI2IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjBXcno3WGxSL1d3Q0NpdzRiU1NTR2c9PSIsInZhbHVlIjoibGJqa0l2RzMvU1hyTFRHbGFEY1l3K1R6U2hqWmo0eTkxNjNaWmxOUmJ1LzFsclQ5RStSODZFdUw0NFIyYVdKdWQxK1hrVkdXdnlsZG0rMHVGcklGdFdpNTFJOTZVTGNoYlY0aDVHTnI3TGpsYzJWNFRVWHRpZjBXdytzUFBxKzczVG5KQjJ3VTNLUTF1WHRGanhuVElpQUkvWWZHU3VXeVhHT24vcVNKenFPOGFyUjY0ZmFaU0FWNXUrbWM4MUljbjcwMHlDelRXeUk3aFRYUzhOdzV0L0s2WTFySjljL0NGc0dsRVQyR3NRbDh2UjV1djg2YUNiWGFpYTFSMlBqaHFwbm1DYTlCa3AzUWlLeEFTaXRSK0ZIM1JoTGpsQzU1dTBhSEtYdG9Dc250ZUZieXlTRGRDN2t6VEVzK1puT0dPWmJPdkNzcTdpOExzaWdVdDZ2OEVtelQrTDBOcUd3b1dmRWwxZlV1Uk5vbk91cjNSVkd5amRwMUx6NjR6cnVqYkpESlJpT2JxaS9tdWxvYnpPbVBPTThoN0p5c2Mxd09yQnl5eHZpSzBQOTRyM25tTDBnaUVCbWRtL05xcEtaMlhJQVRkMVVURm9MYzhra3QvNTJwSXRIRW1Za3ZsRldlRFlhWFYyUXltMGZEOS95YzhqenZaRzRtSGFObTFRZVYiLCJtYWMiOiJlNmNjYjg2ZGZmOGVmZTljOTYzODAyODlmM2U2ZTYxYTg0ZGQ2YjU5NjY2OTY1MTkxYTQyYjNlOTVjMDExODA1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1800380608 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">jEiKRXRaeQw7kpqtZmXNWu3bpmEnaxKAnNI8mVhc</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PSbLK6QAWuUzu3JpIF2TDtYQ1L7TOqCcejFUVX3V</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1800380608\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1842433237 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 16:02:05 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik4vT1Jha3NDRHVwZGg1cVJSRW1odlE9PSIsInZhbHVlIjoiUWdpeFFndTVkdFUyc2hTQzJSQ0p3SlhucEtRUnlpNTRaWUhtRmNrUlU2K2JDNUp5T0UybVMrZTBwSTAxMnFCT1d3a09iZFNpZzBqRmREcithcUFxUHlGM1ZxdDBNa0lKS01KcHA5Zi9zclhqZGw2a2VYM1FDK2FRSlIxNHRYZTIrdExMUW1KSHFUU1NzYXU3UUg0MGJLSkhGNE12eTlkQ2FSd0ZWMkswQjVyRDVnWmliT3JzalkvaXEwMHBiMWZkME5vbkQ4bGxGZFhWMmRJYU9ieEUwOXdtbE9jZGJxNm9peFVhaTZMV2NPNXRwdjBoVFVQYm9MOS9SbnZJNXlxS3lyWGIvVTRtMUtnMENsMnFaQm5QV1dPTHh5MlBRNTlWMjRTSzRjVVF6ZG1hTlMwSmlYbmNGREVpZm5lMVVUeTdmbFhkems0dUNGeldzcE96bVA3d0swVzV5VkJlWUZ6QjQvU3VKdFQwRVRCL3Q3djI2c0VZVFZmc0JHUU5JN3BXdEs3cU1FWlU0NThjMk8zektwRkdzVElEWUQ3WFNPOFNZd3pEL0JUOXJBVGYxVGk4RWsrdlRGUVB5cXczTDhQeDZyT1JmbkJQOEpBZTBEYXZnMDY4cDViOXN1LytTbStOdWw0MElDZGQ1dmVYSUVWWXV3aTFlRGNWb1RVVnVSSEEiLCJtYWMiOiI3ZTFiNjE0NjdkOTY4ZjZhMWE5MjQ4ZjIxZTI2NDE1MWE0MzU5ODc5OWMyMGE2ZDM2ODU5N2FhNWIyMWIyNTNkIiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 18:02:05 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkVzUEdYSjVkcXQ2N0xqUmYzT1ZvWlE9PSIsInZhbHVlIjoiOGZmRVBhSGNSVjdIM0pBR3dqdUpvNUlzNlFRRnB4SVVlMU42NzhWcnNrTGMwdWF2NkFkRHR1anJiUG1yNUxLMUNLL2g1NmhtcWJzQ2U1YjdJQU1OMlI0Q0FnZFFJbXZLbkZnVmZROGM3eU83TldZQm1CSzAxWkJ3aXR0elRwcE9LZE5TYmxCTGFQS2ZvTm5WS1ZBcXd0MUxsWWRoaGdORUtmcWx0MVg5cHR0RXFxVXQ1UTFaYmZ5TFlnY2ZGRzhUSklTYWxmaFV0ZTN3Wm9CT25NL1Zsc3lzRjBpcTkzSlBPNUsraFB5T3dzWTlTRDNMMWtLNllObzRVU1FwT2c0NEIvWDVzcVptYzZjUWZBWkdvS0ZzN0tOTXowS09BU25VN1JiSm9HWE0rTlRlM3NUeHBEN3pxNmlsZU1LRnZyREZINnBsYWZERGFJU3QybEpIV28vL04zdHJDWlJWUWJGdmEweTFjaU5KNFIvVEdQYlRaSnZNUGg2TGt1MXdXdkViaEpwQXUwZURUb2NTaHMraW5GakdXRWFDZjdMeitVSUM5NktWR0xKRTZJeDlmUm1VMDUvQ3BTRkFYbWJENjI2dXFwcW5wNkhSdEJ5UlI0eFRRaTU0N2NDMk1PeWJUL0NPRnJTNjlBa2RTREhmSXNBK1BGUWQ5U3RhV0xEYzcrRHIiLCJtYWMiOiIyODhjNWUzZDkzNTVlMWY4ZDFiZDRlMDNjYjQzODExN2ZhZDE2ZjY2OWI2ZThhODgyZGIyY2E3ZGFjODFkMzVlIiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 18:02:05 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik4vT1Jha3NDRHVwZGg1cVJSRW1odlE9PSIsInZhbHVlIjoiUWdpeFFndTVkdFUyc2hTQzJSQ0p3SlhucEtRUnlpNTRaWUhtRmNrUlU2K2JDNUp5T0UybVMrZTBwSTAxMnFCT1d3a09iZFNpZzBqRmREcithcUFxUHlGM1ZxdDBNa0lKS01KcHA5Zi9zclhqZGw2a2VYM1FDK2FRSlIxNHRYZTIrdExMUW1KSHFUU1NzYXU3UUg0MGJLSkhGNE12eTlkQ2FSd0ZWMkswQjVyRDVnWmliT3JzalkvaXEwMHBiMWZkME5vbkQ4bGxGZFhWMmRJYU9ieEUwOXdtbE9jZGJxNm9peFVhaTZMV2NPNXRwdjBoVFVQYm9MOS9SbnZJNXlxS3lyWGIvVTRtMUtnMENsMnFaQm5QV1dPTHh5MlBRNTlWMjRTSzRjVVF6ZG1hTlMwSmlYbmNGREVpZm5lMVVUeTdmbFhkems0dUNGeldzcE96bVA3d0swVzV5VkJlWUZ6QjQvU3VKdFQwRVRCL3Q3djI2c0VZVFZmc0JHUU5JN3BXdEs3cU1FWlU0NThjMk8zektwRkdzVElEWUQ3WFNPOFNZd3pEL0JUOXJBVGYxVGk4RWsrdlRGUVB5cXczTDhQeDZyT1JmbkJQOEpBZTBEYXZnMDY4cDViOXN1LytTbStOdWw0MElDZGQ1dmVYSUVWWXV3aTFlRGNWb1RVVnVSSEEiLCJtYWMiOiI3ZTFiNjE0NjdkOTY4ZjZhMWE5MjQ4ZjIxZTI2NDE1MWE0MzU5ODc5OWMyMGE2ZDM2ODU5N2FhNWIyMWIyNTNkIiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 18:02:05 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkVzUEdYSjVkcXQ2N0xqUmYzT1ZvWlE9PSIsInZhbHVlIjoiOGZmRVBhSGNSVjdIM0pBR3dqdUpvNUlzNlFRRnB4SVVlMU42NzhWcnNrTGMwdWF2NkFkRHR1anJiUG1yNUxLMUNLL2g1NmhtcWJzQ2U1YjdJQU1OMlI0Q0FnZFFJbXZLbkZnVmZROGM3eU83TldZQm1CSzAxWkJ3aXR0elRwcE9LZE5TYmxCTGFQS2ZvTm5WS1ZBcXd0MUxsWWRoaGdORUtmcWx0MVg5cHR0RXFxVXQ1UTFaYmZ5TFlnY2ZGRzhUSklTYWxmaFV0ZTN3Wm9CT25NL1Zsc3lzRjBpcTkzSlBPNUsraFB5T3dzWTlTRDNMMWtLNllObzRVU1FwT2c0NEIvWDVzcVptYzZjUWZBWkdvS0ZzN0tOTXowS09BU25VN1JiSm9HWE0rTlRlM3NUeHBEN3pxNmlsZU1LRnZyREZINnBsYWZERGFJU3QybEpIV28vL04zdHJDWlJWUWJGdmEweTFjaU5KNFIvVEdQYlRaSnZNUGg2TGt1MXdXdkViaEpwQXUwZURUb2NTaHMraW5GakdXRWFDZjdMeitVSUM5NktWR0xKRTZJeDlmUm1VMDUvQ3BTRkFYbWJENjI2dXFwcW5wNkhSdEJ5UlI0eFRRaTU0N2NDMk1PeWJUL0NPRnJTNjlBa2RTREhmSXNBK1BGUWQ5U3RhV0xEYzcrRHIiLCJtYWMiOiIyODhjNWUzZDkzNTVlMWY4ZDFiZDRlMDNjYjQzODExN2ZhZDE2ZjY2OWI2ZThhODgyZGIyY2E3ZGFjODFkMzVlIiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 18:02:05 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1842433237\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1908284994 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">jEiKRXRaeQw7kpqtZmXNWu3bpmEnaxKAnNI8mVhc</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>17</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1908284994\", {\"maxDepth\":0})</script>\n"}}