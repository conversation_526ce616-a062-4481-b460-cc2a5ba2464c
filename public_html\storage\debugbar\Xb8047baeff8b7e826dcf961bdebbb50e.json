{"__meta": {"id": "Xb8047baeff8b7e826dcf961bdebbb50e", "datetime": "2025-06-26 15:59:29", "utime": **********.325878, "method": "GET", "uri": "/", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750953568.918858, "end": **********.325897, "duration": 0.40703892707824707, "duration_str": "407ms", "measures": [{"label": "Booting", "start": 1750953568.918858, "relative_start": 0, "end": **********.260704, "relative_end": **********.260704, "duration": 0.3418459892272949, "duration_str": "342ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.260714, "relative_start": 0.3418560028076172, "end": **********.325899, "relative_end": 1.9073486328125e-06, "duration": 0.0651848316192627, "duration_str": "65.18ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43985904, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET /", "middleware": "web, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@landingpage", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=59\" onclick=\"\">app/Http/Controllers/DashboardController.php:59-74</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.02598, "accumulated_duration_str": "25.98ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Controllers\\DashboardController.php", "line": 66}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.285173, "duration": 0.02598, "duration_str": "25.98ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 100}]}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "0ps0kU08Y9VObrS8MGH1isQjRNeK7Gn2ObkN9gvL", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-207943544 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-207943544\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1972609678 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1972609678\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-921737130 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">prefetch;prerender</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">prefetch</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-921737130\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1747968582 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1747968582\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-541660248 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 15:59:29 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImhBYjBvMVdTTlJVaE5rZGVUYmlrS1E9PSIsInZhbHVlIjoiTDlQdEorMnp0ZlVvK1RYLzhlZXB1VExoblR6U3I3QUhob3NOOElOOVduODIzMGpDRVJwK3NxbEx0VTVCUXh6dno5akQxTlBDYXFLU2JsREdqdVNNRlJKYWhoc1pqYmNFRDU4clRXZisrREFkZGRKRHlaRlN2SzIzRWhRdXlZTmlWK21sYjhJUzhDTTIwS1JxV2IrUjZ1Z1FEUTNjWXVFU21lbGQ3UDlNNmlQRkpIS0VTRlN3UTlRNCs5Wm04SjZNQ0VET2xjZEMwZ3JVNUlwZk5rNG42Q2hkT2NQL1BVaFVWV0kxUDByVHdlTDVwaWpudnF0OWZPY1ArTXAvVUgwOFlNeXRhK0dPdCtIeWNSZE5ObjRTbnlEMXNySHVSQmRMQ1lDQXVtdnhZbTUzNkRpd2RiOGZIcmk0U0F3MERxRGhoc21XeDlwZXNCWnhnRFpuOWpKazFUVDhGTXpQcitKb1JGOUsrN0JRRjl3Uk5yM3ZZMGdwQmhMZlduWXFubGVzNmcyYys1Q1NEZm1URjJ2TTFpa2M5WUQxcDVYMTdDOWFRRzA1UmJnME4zdExxSVNQY3hPV3Z5emJDVzZta2pKMnhoNFdtaE4wZ2xrN1dxVzRhY09tSStXTEdRUWpsaGdhZk1vTmRySTRJaWhodUtvYVZEblhRaVRianMwYkxEeTkiLCJtYWMiOiI5ODdmMTZkYjUzODk3NTkyNzFkNDRkMmVkNWU5YTQ5Zjk0ZWFlZTI4ODhjOWI4Y2M4ZDZmODQ5OGIzNjI5MzJjIiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 17:59:29 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InZwN0l6dFlKbWhKRDlCUmlWaWdtTXc9PSIsInZhbHVlIjoiU3NtTDI4Y3E2YVJUZXQ2TXorKzRXeVZKaDR6NG4waUVGTlZvb21ZVFE2QzhXVlJwV3ZoS2VzdEkyTElFSlJZdHlrSUdKcTRoUlJ3UG1GeXJ2QndodGJzRVJLdmd4M3hLbm51U3pVbm5kc041dlZWRzhjVHBPdTVTWEJDVEFxdUpnRDFEaGE5WFBUSjcySXBaUXhrem4wYnhiODl3b0g3ZGhhNVlnWjRuM25NNWVTWk0yaWh1b1cyVFNjam82Ty8wQWtBTVRtTUZwM2ExUFdoNGdtMDNsMUQ0UXhNenZEUjNIUGMzSml4Wm45NFBEU1NNM0pqR0hsMlIrOHFhRFB6U2FKcVcrS1pxaGdwUFFFOFQ4Ukt4S1FPL25zbm9WMzNEOGJWK2txOG5sajVsdHF1WUJKbmVDcTNkbUk3MFZQSUlBNEhTb2s2UnErSnlIWDFvWXR4ZXFmVjY1RFZRbzVndytrTmRrbVR3aEduNUMrWmtjc0s3WGtSYmRLRGJzYlFRNldBUSszQU5MV2YyT2hQSENjSXBQWUg3SS9EM2pRTGcwaUFQclNUbklhZXVYcHdrTHAraDZNbzhNbSt5MkVvekVvMW5TRmtnSERGNnZJWXlZbFVCYUh1Y3NZVndwdWxhN1QvUU1UNElSeUx5YUhvWm9NdzRZWGM4cDdOTDlkcHYiLCJtYWMiOiI5Y2U5OTkxODZhZmEzY2Q0MzZlMzdjOWU5OGVmNWRkNTZhYTQ3OGZkYTU3ZDhmYTg4MTFjYWY1ZWRhMDIyM2ViIiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 17:59:29 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImhBYjBvMVdTTlJVaE5rZGVUYmlrS1E9PSIsInZhbHVlIjoiTDlQdEorMnp0ZlVvK1RYLzhlZXB1VExoblR6U3I3QUhob3NOOElOOVduODIzMGpDRVJwK3NxbEx0VTVCUXh6dno5akQxTlBDYXFLU2JsREdqdVNNRlJKYWhoc1pqYmNFRDU4clRXZisrREFkZGRKRHlaRlN2SzIzRWhRdXlZTmlWK21sYjhJUzhDTTIwS1JxV2IrUjZ1Z1FEUTNjWXVFU21lbGQ3UDlNNmlQRkpIS0VTRlN3UTlRNCs5Wm04SjZNQ0VET2xjZEMwZ3JVNUlwZk5rNG42Q2hkT2NQL1BVaFVWV0kxUDByVHdlTDVwaWpudnF0OWZPY1ArTXAvVUgwOFlNeXRhK0dPdCtIeWNSZE5ObjRTbnlEMXNySHVSQmRMQ1lDQXVtdnhZbTUzNkRpd2RiOGZIcmk0U0F3MERxRGhoc21XeDlwZXNCWnhnRFpuOWpKazFUVDhGTXpQcitKb1JGOUsrN0JRRjl3Uk5yM3ZZMGdwQmhMZlduWXFubGVzNmcyYys1Q1NEZm1URjJ2TTFpa2M5WUQxcDVYMTdDOWFRRzA1UmJnME4zdExxSVNQY3hPV3Z5emJDVzZta2pKMnhoNFdtaE4wZ2xrN1dxVzRhY09tSStXTEdRUWpsaGdhZk1vTmRySTRJaWhodUtvYVZEblhRaVRianMwYkxEeTkiLCJtYWMiOiI5ODdmMTZkYjUzODk3NTkyNzFkNDRkMmVkNWU5YTQ5Zjk0ZWFlZTI4ODhjOWI4Y2M4ZDZmODQ5OGIzNjI5MzJjIiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 17:59:29 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InZwN0l6dFlKbWhKRDlCUmlWaWdtTXc9PSIsInZhbHVlIjoiU3NtTDI4Y3E2YVJUZXQ2TXorKzRXeVZKaDR6NG4waUVGTlZvb21ZVFE2QzhXVlJwV3ZoS2VzdEkyTElFSlJZdHlrSUdKcTRoUlJ3UG1GeXJ2QndodGJzRVJLdmd4M3hLbm51U3pVbm5kc041dlZWRzhjVHBPdTVTWEJDVEFxdUpnRDFEaGE5WFBUSjcySXBaUXhrem4wYnhiODl3b0g3ZGhhNVlnWjRuM25NNWVTWk0yaWh1b1cyVFNjam82Ty8wQWtBTVRtTUZwM2ExUFdoNGdtMDNsMUQ0UXhNenZEUjNIUGMzSml4Wm45NFBEU1NNM0pqR0hsMlIrOHFhRFB6U2FKcVcrS1pxaGdwUFFFOFQ4Ukt4S1FPL25zbm9WMzNEOGJWK2txOG5sajVsdHF1WUJKbmVDcTNkbUk3MFZQSUlBNEhTb2s2UnErSnlIWDFvWXR4ZXFmVjY1RFZRbzVndytrTmRrbVR3aEduNUMrWmtjc0s3WGtSYmRLRGJzYlFRNldBUSszQU5MV2YyT2hQSENjSXBQWUg3SS9EM2pRTGcwaUFQclNUbklhZXVYcHdrTHAraDZNbzhNbSt5MkVvekVvMW5TRmtnSERGNnZJWXlZbFVCYUh1Y3NZVndwdWxhN1QvUU1UNElSeUx5YUhvWm9NdzRZWGM4cDdOTDlkcHYiLCJtYWMiOiI5Y2U5OTkxODZhZmEzY2Q0MzZlMzdjOWU5OGVmNWRkNTZhYTQ3OGZkYTU3ZDhmYTg4MTFjYWY1ZWRhMDIyM2ViIiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 17:59:29 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-541660248\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-912405682 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0ps0kU08Y9VObrS8MGH1isQjRNeK7Gn2ObkN9gvL</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-912405682\", {\"maxDepth\":0})</script>\n"}}