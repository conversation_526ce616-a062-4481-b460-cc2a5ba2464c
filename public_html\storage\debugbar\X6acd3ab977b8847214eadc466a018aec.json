{"__meta": {"id": "X6acd3ab977b8847214eadc466a018aec", "datetime": "2025-06-26 16:01:47", "utime": **********.177803, "method": "POST", "uri": "/user-reset-password/17", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750953706.741504, "end": **********.177821, "duration": 0.43631696701049805, "duration_str": "436ms", "measures": [{"label": "Booting", "start": 1750953706.741504, "relative_start": 0, "end": **********.060027, "relative_end": **********.060027, "duration": 0.31852293014526367, "duration_str": "319ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.060036, "relative_start": 0.31853199005126953, "end": **********.177823, "relative_end": 2.1457672119140625e-06, "duration": 0.11778712272644043, "duration_str": "118ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45228792, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST user-reset-password/{id}", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\UserController@userPasswordReset", "namespace": null, "prefix": "", "where": [], "as": "user.password.update", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FHttp%2FControllers%2FUserController.php&line=584\" onclick=\"\">app/Http/Controllers/UserController.php:584-617</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00628, "accumulated_duration_str": "6.28ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.085777, "duration": 0.0016200000000000001, "duration_str": "1.62ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 25.796}, {"sql": "select * from `users` where `id` = '17' limit 1", "type": "query", "params": [], "bindings": ["17"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Controllers\\UserController.php", "line": 598}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.094733, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "UserController.php:598", "source": "app/Http/Controllers/UserController.php:598", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FHttp%2FControllers%2FUserController.php&line=598", "ajax": false, "filename": "UserController.php", "line": "598"}, "connection": "kdmkjkqknb", "start_percent": 25.796, "width_percent": 8.439}, {"sql": "update `users` set `password` = '$2y$10$ZkE2h3EHNl8FjJDASjcbbOF0C8oCnAgeQyJEGv0dR.7aDZ4VY65ki', `users`.`updated_at` = '2025-06-26 16:01:47' where `id` = 17", "type": "query", "params": [], "bindings": ["$2y$10$ZkE2h3EHNl8FjJDASjcbbOF0C8oCnAgeQyJEGv0dR.7aDZ4VY65ki", "2025-06-26 16:01:47", "17"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Controllers\\UserController.php", "line": 602}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.162709, "duration": 0.00413, "duration_str": "4.13ms", "memory": 0, "memory_str": null, "filename": "UserController.php:602", "source": "app/Http/Controllers/UserController.php:602", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FHttp%2FControllers%2FUserController.php&line=602", "ajax": false, "filename": "UserController.php", "line": "602"}, "connection": "kdmkjkqknb", "start_percent": 34.236, "width_percent": 65.764}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "G4oUC4dvL91DvSdXtTSUa87JqfM4sfNNzjOiK6ns", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"success\"\n  ]\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "success": "User Password successfully updated.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/user-reset-password/17", "status_code": "<pre class=sf-dump id=sf-dump-71012333 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-71012333\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-2079459130 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2079459130\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1675615873 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G4oUC4dvL91DvSdXtTSUa87JqfM4sfNNzjOiK6ns</span>\"\n  \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>password_confirmation</span>\" => \"<span class=sf-dump-str title=\"6 characters\">112233</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1675615873\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-483234996 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">92</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1ap6d1q%7C2%7Cfx3%7C0%7C1998; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clsk=p74v85%7C1750953557602%7C82%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImRWdWZDaGlncE80L1BXWXE1ZUEwQlE9PSIsInZhbHVlIjoiMzlDTWNxZW5lUDdLZjMycms0aFFFbE1pQXRIeXh2R0VuZzNFeU5HRVRUdG1WbzdpemlUZkUvVGpvaVk0aC9qVmxXbE5PZTgxemt0RUkvc3A3dTZKeVpya1V1T1NiS3o2SEtyTVIvc1o4YjE3eUp0cU5TTGd6QUpZakUxUEFLK0toemM1T1kzblgxZ05LcDNPOW9PcFh2U3hjL3JpaVBaTGVFZ2t5blo4RlAxa1RpODJ1UzJnMjg0RXd4enBoVVB4amhaczY0cXhndTBDbVVWTkdoQjNwM0lnVXN0SjJPcmFKL0VTR0VlQ2hRSXh5N1dXOUplc2RmcEE2T1hHZ2xHWmNveXlsaHlnYi90ZVZEQ3dJSWVwL3hWQktjbXJYVDRJK2lRY1E3UzJ1OTFvVUM4ckFqY3Q4d0d3Tmg0OEFJT1hwZHNGNFdhYUxBcVdtOElwaDNIUzVveVZiTWVqSmlwY1dzWUduTUFkd1Rwb1JSd0cwWlIxaXpXTTY0T3ZCZ0drR3BYK0phblRWV3EyeTBTUGFReXlpNk5JYm9kdDdVQUxnZXpnYTBiMXdDVE56S05UNHkxcy9vazU5WEFkaTVGRWNVeGxJV0VJUVpWR21yZUdsM3g1THYyeGQyQ3drMlAyeEY2a2lSVkYwblU2bXU5cXE2QWdlOHI5c2VGYTBwM3kiLCJtYWMiOiI2M2VkMGRlYThlOTU0MmY1MTUyYzdmMTlkZGNlZjI3NzgyMDZkMDI3YzA2ZmZkOTg5YzQwYzQ5MjcyYTExMTY0IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImhDT3oyaGZsZVdvVUVOQk9QbWVHM3c9PSIsInZhbHVlIjoiQVNIaWpER1lSS054OVFSNHdFODJjRTVTR3hQUlpXUHV1NUVSQkpPSXdsUWRQdTRad3dtczgva0RtbWJpOXBVTUVKQVJHMlZHL0dxRzJ6aENQMGFPU0wyNHZxNmZjSkE0YkZMNDFXcHVYSUliQzdRTUJSUisxTzk2Y3VXMkx6ZGd1UzRmYUIzTWNqMkdUNWxjMnN5UDBndkd0Y0RWSUtvblJqZHFIRDNRZVdZVFlIYllhNXoyRU44RzdxNlZUd1Z0MjBtaENLS29TS3NqWVpvTWpjb3NHTjIxREtUUWlJZHlqTW8xU082UHVkL2xsTWc5QmRsM3hBd1dXMWVhVWR2TUlDOE1xOWJtcWRpU0MxZmJ5dWttdGVMdXNCWnZ4cGY1ZzZMS3RrcEtyS0VkbmJiMVB4RkRqZWY2TjVBMzBuUmV6ZDEyNlhCMzMrRkd2OGZ4VjdScEZlTTdWNExzZDQrbEpVL0JKZTBOcFQ0NXlJQkxnTHlaOFFHalFXMkwxM3BZdFprdFQxYkg2eVlTb1hQWTRKQWRsSXk4YWt4eHc4bnUyQ04zaXI5UG9rZ2xId1RLMS9FYkpQZXJXckxiTFM4TTBXSjYwQlpqTzB1WThOZlAwMVlLaFIrWUU3NkN3dDB4SGRHdGt2U3NXd2tKOVhWOURna3VOU3dyL2p3ZlEramsiLCJtYWMiOiI4YjUwOTQ5YTE2NTRkZmMyZTM5YTg3ZDY2MGQyMWUyNzllMTQzZjBiOTk2OTFiMzFlYmRiZWIyZDIwYTE0ZDBkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-483234996\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1117496719 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G4oUC4dvL91DvSdXtTSUa87JqfM4sfNNzjOiK6ns</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">16z2m35FIhU8zNuOqkDvqLO3UyIjTT7e4c1Y0Zqf</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1117496719\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-360538133 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 16:01:47 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlRaTHVJcVZDUnJDOGJIVjNyNlJqclE9PSIsInZhbHVlIjoiMnRHNzJvbnFobG9KYjJLM0FqL3I5Q296eU9EZEVBNU5SOFphbDQwQWcwRFJxUFZvVy9sdHhYZmhHdWlIUmpsRUJwSDdZQk13V1dXTE14SVhXSUlqREJtWTh3YjdOVTJUY1A4aGNWSkc5ZE5nYjJuRDRMSEJpM3NWRld6VXVIWktnWmlOdWVhc1dNTndqRFcyNEhKMFVGNDdZek8ydlRESDdLWG0rVnpNMmNIeEdtLzAzWU80WE5GelFnUzAxbTZIQzdqQnB3azJPaFcweW9LUVA1V0xSYVBXd2tuL0dtTG9BaXpXK1grMFBOcmF0VW9BVXBtWEJSblB5WmF1WjI3YjVYWll6UkVWcnh1NGg4UmhpMy9NLzNyRHB6YXROTFNHeENMUG5ZL0ZMbVB1UzY5YXc4eWFGc1NTMnM4ZWpxSWtDdWpwM2k4S2NNMXM1QURnVnU0QkNlNG5qREh6NnNLMHZxNFVXUUVoTEZwWnhiUXBLOEpNK0kyakdqcTZuL3phdXQ5T0dOcFJmYUQ2TmdSbnRwWU4vUVlHa1V2dEx6eTdyZERsZG4yek9BZ2wyNXVLTEdrS2JXOEM1UzF2RGZUSnByRWhFbEYwa3ZmOTRHbUZ6YXBmQTl0RGpablJ3dTZ1RGhkM1dqK0NHbGtkREtSN3orZHhjbExvWEVGUkFEY1EiLCJtYWMiOiI4ZDFiNWI4ZDU3OWQzZmI3YjgzYjFjNDc2MzVkNzUyN2YyZGJhNDBkOTVhM2Y3M2Y0ZWMyMTZhYzAyZmM3MWZkIiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 18:01:47 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkRtZnV4UEZFWXlBcXhOSmNPY1NsRVE9PSIsInZhbHVlIjoicEFBM01ybW9xSEtEYTVvaDFqYXk3eDNXMlRTUm40UnNrWGJGZVUxRmU5eHozb0tFcFVPaVFhL1pNNTVnclV0TlBZQkYzSFRTQ2Noay9xUGdMTzM4SUdtdWpqN3hudlVoQS80ZWtYalR0Umc3RlA5Q1JTSUwveDI4cGVJS1J3NC9jRk5Tdk11aUNEK0dMUkhpQ1hrbnNJMWV5MHk4bnNpVWFZalpYTmtuUkxEb0RPUmlqMU1sb0pUQ29pMWZxSThoYjJMQitFMVpiM081bThNT0FoVW1uRC9Va1VlaVM0cncwSE5oZGdPOTBxYmN2cithclVScHIxVjVTY1lxdmJsUEVoL0FsT3FVeU80VEdhakdvY0MzdzA4RlZ6UjZRV285K1NrKzloaUtNK2xZejErWVNWc2diaFZKQ2VFT0xJWnBGMTY2TEVac3FFV09xUjVoNnVsbG41NEtPME8xaE5WekpNOUJwNHY3VFNOZktiTUlpQkJ0YVdWNGNRUUVCY2JiTmIzRllvRnpvdzF2ZGRscktSVVdjVEQxdkRKdW5teVdaUEpFdVgzNy9QV0lITmttMlNBOERENkZFYXZCTG1WRUgzZXpFWk51SnFBcHVwVGV2L3NMNFduZnBzVFdWK3BXSy9CM3pmdHVTazlKSDBFQnNGZGpTbkIwY1kydzkwdzIiLCJtYWMiOiIyZmE2YWJjZmU4NDgwZmVlYzgzODBjNjYxNmJkNjlmYjA4MjJmOTM5NjlmZDZlOTcxZGU5ODM5YjkwNzg4MmFiIiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 18:01:47 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlRaTHVJcVZDUnJDOGJIVjNyNlJqclE9PSIsInZhbHVlIjoiMnRHNzJvbnFobG9KYjJLM0FqL3I5Q296eU9EZEVBNU5SOFphbDQwQWcwRFJxUFZvVy9sdHhYZmhHdWlIUmpsRUJwSDdZQk13V1dXTE14SVhXSUlqREJtWTh3YjdOVTJUY1A4aGNWSkc5ZE5nYjJuRDRMSEJpM3NWRld6VXVIWktnWmlOdWVhc1dNTndqRFcyNEhKMFVGNDdZek8ydlRESDdLWG0rVnpNMmNIeEdtLzAzWU80WE5GelFnUzAxbTZIQzdqQnB3azJPaFcweW9LUVA1V0xSYVBXd2tuL0dtTG9BaXpXK1grMFBOcmF0VW9BVXBtWEJSblB5WmF1WjI3YjVYWll6UkVWcnh1NGg4UmhpMy9NLzNyRHB6YXROTFNHeENMUG5ZL0ZMbVB1UzY5YXc4eWFGc1NTMnM4ZWpxSWtDdWpwM2k4S2NNMXM1QURnVnU0QkNlNG5qREh6NnNLMHZxNFVXUUVoTEZwWnhiUXBLOEpNK0kyakdqcTZuL3phdXQ5T0dOcFJmYUQ2TmdSbnRwWU4vUVlHa1V2dEx6eTdyZERsZG4yek9BZ2wyNXVLTEdrS2JXOEM1UzF2RGZUSnByRWhFbEYwa3ZmOTRHbUZ6YXBmQTl0RGpablJ3dTZ1RGhkM1dqK0NHbGtkREtSN3orZHhjbExvWEVGUkFEY1EiLCJtYWMiOiI4ZDFiNWI4ZDU3OWQzZmI3YjgzYjFjNDc2MzVkNzUyN2YyZGJhNDBkOTVhM2Y3M2Y0ZWMyMTZhYzAyZmM3MWZkIiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 18:01:47 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkRtZnV4UEZFWXlBcXhOSmNPY1NsRVE9PSIsInZhbHVlIjoicEFBM01ybW9xSEtEYTVvaDFqYXk3eDNXMlRTUm40UnNrWGJGZVUxRmU5eHozb0tFcFVPaVFhL1pNNTVnclV0TlBZQkYzSFRTQ2Noay9xUGdMTzM4SUdtdWpqN3hudlVoQS80ZWtYalR0Umc3RlA5Q1JTSUwveDI4cGVJS1J3NC9jRk5Tdk11aUNEK0dMUkhpQ1hrbnNJMWV5MHk4bnNpVWFZalpYTmtuUkxEb0RPUmlqMU1sb0pUQ29pMWZxSThoYjJMQitFMVpiM081bThNT0FoVW1uRC9Va1VlaVM0cncwSE5oZGdPOTBxYmN2cithclVScHIxVjVTY1lxdmJsUEVoL0FsT3FVeU80VEdhakdvY0MzdzA4RlZ6UjZRV285K1NrKzloaUtNK2xZejErWVNWc2diaFZKQ2VFT0xJWnBGMTY2TEVac3FFV09xUjVoNnVsbG41NEtPME8xaE5WekpNOUJwNHY3VFNOZktiTUlpQkJ0YVdWNGNRUUVCY2JiTmIzRllvRnpvdzF2ZGRscktSVVdjVEQxdkRKdW5teVdaUEpFdVgzNy9QV0lITmttMlNBOERENkZFYXZCTG1WRUgzZXpFWk51SnFBcHVwVGV2L3NMNFduZnBzVFdWK3BXSy9CM3pmdHVTazlKSDBFQnNGZGpTbkIwY1kydzkwdzIiLCJtYWMiOiIyZmE2YWJjZmU4NDgwZmVlYzgzODBjNjYxNmJkNjlmYjA4MjJmOTM5NjlmZDZlOTcxZGU5ODM5YjkwNzg4MmFiIiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 18:01:47 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-360538133\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1210815496 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G4oUC4dvL91DvSdXtTSUa87JqfM4sfNNzjOiK6ns</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"35 characters\">User Password successfully updated.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1210815496\", {\"maxDepth\":0})</script>\n"}}