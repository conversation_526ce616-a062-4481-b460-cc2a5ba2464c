<?php

namespace App\Http\Controllers;

use App\Models\BankAccount;
use App\Models\Bill;
use App\Models\Customer;
use App\Models\Invoice;
use App\Models\Budget;
use App\Models\Payment;
use App\Models\Pos;
use App\Models\ProductServiceCategory;
use App\Models\Purchase;
use App\Models\Revenue;
use App\Models\Utility;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Crypt;

class BudgetController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        if(\Auth::user()->can('manage budget plan'))
        {
            $budgets = Budget::where('created_by', '=', \Auth::user()->creatorId())->get();
            $periods = Budget::$period;
            return view('budget.index', compact('budgets', 'periods'));
        }
        else
        {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {

        if(\Auth::user()->can('create budget plan'))
        {
            $periods = Budget::$period;

            $data['monthList'] = $month = $this->yearMonth();          //Monthly

            $data['quarterly_monthlist'] = [                          //Quarterly
                                                                      'Jan-Mar',
                                                                      'Apr-Jun',
                                                                      'Jul-Sep',
                                                                      'Oct-Dec',
            ];

            $data['half_yearly_monthlist'] = [                     // Half - Yearly
                                                                   'Jan-Jun',
                                                                   'Jul-Dec',
            ];

            $data['yearly_monthlist'] = [                   // Yearly
                                                            'Jan-Dec',
            ];


            $data['yearList'] = $this->yearList();

            $incomeproduct  = ProductServiceCategory::where('created_by', '=', \Auth::user()->creatorId())->where('type', '=', 'income')->get();
            $expenseproduct = ProductServiceCategory::where('created_by', '=', \Auth::user()->creatorId())->where('type', '=', 'expense')->get();


            return view('budget.create', compact('periods', 'incomeproduct', 'expenseproduct'), $data);
        }
        else
        {
            return response()->json(['error' => __('Permission denied.')], 401);

        }

    }


    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {

        if(\Auth::user()->can('create budget plan'))
        {
            $validator = \Validator::make($request->all(), [
                'name' => 'required',
            //                'from' => 'required',
            //                'to' => 'required',
                'period' => 'required',


            ]);
            if($validator->fails())
            {
                $messages = $validator->getMessageBag();

                return redirect()->back()->with('error', $messages->first());
            }

            $budget               = new Budget();
            $budget->name         = $request->name;
            $budget->from         = $request->year;
            $budget->period       = $request->period;
            $budget->income_data  = json_encode($request->income);
            $budget->expense_data = json_encode($request->expense);

            // Ensure display_settings is properly saved
            $displaySettings = [];

            // Set budget type (income, expense, or both)
            $displaySettings['budget_type'] = isset($request->display_settings['budget_type']) ?
                $request->display_settings['budget_type'] : 'both';

            // Handle category filter
            if (isset($request->display_settings['category_filter'])) {
                // If it's already a JSON string, use it directly
                if (is_string($request->display_settings['category_filter']) &&
                    json_decode($request->display_settings['category_filter'], true) !== null) {
                    $displaySettings['category_filter'] = $request->display_settings['category_filter'];
                }
                // If it's an array, encode it
                else if (is_array($request->display_settings['category_filter'])) {
                    $displaySettings['category_filter'] = json_encode($request->display_settings['category_filter']);
                }
                // Otherwise, use it as is
                else {
                    $displaySettings['category_filter'] = json_encode([$request->display_settings['category_filter']]);
                }
            } else {
                // Default to all categories if not provided
                $displaySettings['category_filter'] = json_encode(['all']);
            }

            $budget->display_settings = json_encode($displaySettings);
            $budget->created_by   = \Auth::user()->creatorId();
            $budget->save();

            //For Notification
            $setting  = Utility::settings(\Auth::user()->creatorId());
            $budgetNotificationArr = [
                'budget_period' => \App\Models\Budget::$period[$request->period],
                'budget_year' => $request->year,
                'budget_name' => $request->name,
            ];
            //Slack Notification
            if(isset($setting['budget_notification']) && $setting['budget_notification'] ==1)
            {
                Utility::send_slack_msg('new_budget', $budgetNotificationArr);
            }
            //Telegram Notification
            if(isset($setting['telegram_budget_notification']) && $setting['telegram_budget_notification'] ==1)
            {
                Utility::send_telegram_msg('new_budget', $budgetNotificationArr);
            }

            //webhook
            $module ='New Budget';
            $webhook =  Utility::webhookSetting($module);
            if($webhook)
            {
                $parameter = json_encode($budget);
                $status = Utility::WebhookCall($webhook['url'],$parameter,$webhook['method']);
                if($status == true)
                {
                    return redirect()->route('budget.index')->with('success', __('Budget Plan successfully created.'));
                }
                else
                {
                    return redirect()->back()->with('error', __('Webhook call failed.'));
                }
            }


            return redirect()->route('budget.index')->with('success', __('Budget Plan successfully created.'));
        }
        else
        {
            return redirect()->back()->with('error', __('Permission denied.'));
        }



    }


    /**
     * Display the specified resource.
     *
     * @param \App\Models\Budget $budget
     *
     * @return \Illuminate\Http\Response
     */
    public function show($ids)
    {

        if(\Auth::user()->can('view budget plan'))
        {
            try {
                $id       = Crypt::decrypt($ids);
            } catch (\Throwable $th) {
                return redirect()->back()->with('error', __('Budget Not Found.'));
            }

            $id                    = Crypt::decrypt($ids);
            $budget                = Budget::find($id);
            $budget['income_data'] = json_decode($budget->income_data, true);
            // Check if display_settings is already an array or needs to be decoded
            if (is_string($budget->display_settings)) {
                $budget['display_settings'] = json_decode($budget->display_settings, true);
            } else {
                $budget['display_settings'] = $budget->display_settings;
            }

            // Log para depuración
            \Log::info("Datos de ingresos del presupuesto: " . json_encode($budget['income_data']));

            $budgetTotalArrs = !empty($budget['income_data']) ? (array_values($budget['income_data'])) : [];

            // Log para depuración
            \Log::info("Budget Total Arrays: " . json_encode($budgetTotalArrs));

            $budgetTotal = array();
            foreach($budgetTotalArrs as $budgetTotalArr)
            {
                foreach($budgetTotalArr as $k => $value)
                {
                    $budgetTotal[$k] = (isset($budgetTotal[$k]) ? $budgetTotal[$k] + $value : $value);
                }
            }

            // Log para depuración
            \Log::info("Budget Total: " . json_encode($budgetTotal));


            $budget['expense_data'] = json_decode($budget->expense_data, true);
            $budgetExpenseTotalArrs       = !empty ($budget['expense_data']) ? (array_values($budget['expense_data']))  : [] ;

            $budgetExpenseTotal = array();
            foreach($budgetExpenseTotalArrs as $budgetExpenseTotalArr)
            {

                foreach($budgetExpenseTotalArr as $k => $value)
                {
                    $budgetExpenseTotal[$k] = (isset($budgetExpenseTotal[$k]) ? $budgetExpenseTotal[$k] + $value : $value);

                }


            }

            $data['monthList']      = $month = $this->yearMonth();          //Monthly

            $data['quarterly_monthlist'] = [                          //Quarterly
                                                                      '1-3' => 'Jan-Mar',
                                                                      '4-6' => 'Apr-Jun',
                                                                      '7-9' => 'Jul-Sep',
                                                                      '10-12' => 'Oct-Dec',
            ];

            $data['half_yearly_monthlist'] = [                     // Half - Yearly
                                                                   '1-6' => 'Jan-Jun',
                                                                   '7-12' => 'Jul-Dec',
            ];

            $data['yearly_monthlist'] = [                   // Yearly
                                                            '1-12' => 'Jan-Dec',
            ];

            // Add individual months for yearly view to prevent "Undefined array key" errors
            $data['yearly_individual_months'] = $this->yearMonth();

            $data['yearList'] = $this->yearList();
            if(!empty($budget->from))
            {
                $year = $budget->from;
            }
            else
            {
                $year = date('Y');
            }
            $data['currentYear'] = $year;

            // جلب جميع فئات الدخل بدلاً من الفئات الموجودة في البيانات فقط
            // هذا يضمن ظهور جميع الفئات حتى لو لم تكن لها بيانات في الميزانية
            $incomeproduct = ProductServiceCategory::where('created_by', '=', \Auth::user()->creatorId())->where('type', '=', 'income')->get();
            $income_product = array_keys($budget->income_data);


            $incomeArr      = [];
            $incomeTotalArr = [];

            foreach($incomeproduct as $cat)
            {

                if($budget->period == 'monthly')
                {
                    $monthIncomeArr      = [];
                    $monthTotalIncomeArr = [];
                    for($i = 1; $i <= 12; $i++)
                    {
                        // حساب الإيرادات من فواتير البيع فقط (إزالة Revenue و POS)
                        $invoiceAmount = 0;

                        // البحث عن فواتير البيع التي لها فئة مرتبطة مباشرة
                        $invoicesByCategory = Invoice::where('created_by', '=', \Auth::user()->creatorId())
                            ->where('category_id', $cat->id)
                            ->whereRAW('YEAR(send_date) =?', [$year])
                            ->whereRAW('MONTH(send_date) =?', [$i])
                            ->get();

                        foreach($invoicesByCategory as $invoice) {
                            try {
                                $invoiceAmount += $invoice->getTotal();
                                // Log para depuración
                                \Log::info("فاتورة بيع مباشرة - الفئة: {$cat->name}, رقم الفاتورة: {$invoice->id}, المبلغ: {$invoice->getTotal()}");
                            } catch (\Exception $e) {
                                // Log error or handle it silently
                                \Log::error('خطأ في حساب إجمالي الفاتورة: ' . $e->getMessage());
                            }
                        }

                        // البحث عن فواتير البيع التي تحتوي منتجات من هذه الفئة
                        // ولكن الفاتورة نفسها ليس لها فئة مرتبطة (لتجنب العد المزدوج)
                        $invoicesByProduct = Invoice::where('created_by', '=', \Auth::user()->creatorId())
                            ->where(function($query) use ($cat) {
                                $query->where('category_id', '!=', $cat->id)
                                    ->orWhereNull('category_id');
                            })
                            ->whereRAW('YEAR(send_date) =?', [$year])
                            ->whereRAW('MONTH(send_date) =?', [$i])
                            ->with(['items.product'])
                            ->get();

                        foreach($invoicesByProduct as $invoice) {
                            // حساب المنتجات التي تنتمي لهذه الفئة فقط
                            $categoryTotal = 0;
                            foreach($invoice->items as $item) {
                                $product = $item->getProduct();
                                if($product && $product->category_id == $cat->id) {
                                    $categoryTotal += ($item->price * $item->quantity);
                                    // Log para depuración
                                    \Log::info("منتج في فاتورة بيع - الفئة: {$cat->name}, رقم الفاتورة: {$invoice->id}, رقم المنتج: {$item->product_id}, المبلغ: " . ($item->price * $item->quantity));
                                }
                            }
                            $invoiceAmount += $categoryTotal;
                        }

                        // حساب إجمالي فواتير البيع لهذا الشهر (جميع الفئات)
                        $invoicesTotal = Invoice::where('created_by', '=', \Auth::user()->creatorId());
                        $invoicesTotal->whereRAW('YEAR(send_date) =?', [$year]);
                        $invoicesTotal->whereRAW('MONTH(send_date) =?', [$i]);
                        $invoicesTotal = $invoicesTotal->get();

                        $invoiceTotalAmount = 0;
                        foreach($invoicesTotal as $invoiceTotal)
                        {
                            try {
                                $invoiceTotalAmount += $invoiceTotal->getTotal();
                            } catch (\Exception $e) {
                                // Log error or handle it silently
                                // \Log::error('خطأ في حساب إجمالي الفاتورة: ' . $e->getMessage());
                            }
                        }

                        $month = date("F", strtotime(date('Y-' . $i)));

                        // حفظ البيانات - فواتير البيع فقط (إزالة Revenue و POS)
                        $monthIncomeArr[$month] = $invoiceAmount;
                        $incomeTotalArr[$month] = $invoiceTotalAmount;

                        // Log para depuración
                        \Log::info("Categoría: {$cat->name}, Mes: {$month}, Monto: {$monthIncomeArr[$month]}");
                    }
                    $incomeArr[$cat->id] = $monthIncomeArr;


                }

                else if($budget->period == 'quarterly' || $budget->period == 'half-yearly' || $budget->period == 'yearly')
                {

                    if($budget->period == 'quarterly')
                    {
                        $durations = $data['quarterly_monthlist'];
                    }
                    elseif($budget->period == 'yearly')
                    {
                        $durations = $data['yearly_monthlist'];
                    }
                    else
                    {
                        $durations = $data['half_yearly_monthlist'];
                    }

                    $monthIncomeArr = [];
                    foreach($durations as $monthnumber => $monthName)
                    {
                        $month = explode('-', $monthnumber);

                        // حساب الإيرادات من فواتير البيع فقط (إزالة Revenue و POS)
                        $invoiceAmount = 0;

                        // البحث عن فواتير البيع التي لها فئة مرتبطة مباشرة
                        $invoicesByCategory = Invoice::where('created_by', '=', \Auth::user()->creatorId())
                            ->where('category_id', $cat->id)
                            ->whereRAW('YEAR(send_date) =?', [$year])
                            ->whereRAW('MONTH(send_date) >=?', $month[0])
                            ->whereRAW('MONTH(send_date) <=?', $month[1])
                            ->get();

                        foreach($invoicesByCategory as $invoice) {
                            try {
                                $invoiceAmount += $invoice->getTotal();
                                // Log para depuración
                                \Log::info("فاتورة بيع مباشرة (فترة) - الفئة: {$cat->name}, رقم الفاتورة: {$invoice->id}, المبلغ: {$invoice->getTotal()}");
                            } catch (\Exception $e) {
                                // Log error or handle it silently
                                \Log::error('خطأ في حساب إجمالي الفاتورة: ' . $e->getMessage());
                            }
                        }

                        // البحث عن فواتير البيع التي تحتوي منتجات من هذه الفئة
                        // ولكن الفاتورة نفسها ليس لها فئة مرتبطة (لتجنب العد المزدوج)
                        $invoicesByProduct = Invoice::where('created_by', '=', \Auth::user()->creatorId())
                            ->where(function($query) use ($cat) {
                                $query->where('category_id', '!=', $cat->id)
                                    ->orWhereNull('category_id');
                            })
                            ->whereRAW('YEAR(send_date) =?', [$year])
                            ->whereRAW('MONTH(send_date) >=?', $month[0])
                            ->whereRAW('MONTH(send_date) <=?', $month[1])
                            ->with(['items.product'])
                            ->get();

                        foreach($invoicesByProduct as $invoice) {
                            // حساب المنتجات التي تنتمي لهذه الفئة فقط
                            $categoryTotal = 0;
                            foreach($invoice->items as $item) {
                                $product = $item->getProduct();
                                if($product && $product->category_id == $cat->id) {
                                    $categoryTotal += ($item->price * $item->quantity);
                                    // Log para depuración
                                    \Log::info("منتج في فاتورة بيع (فترة) - الفئة: {$cat->name}, رقم الفاتورة: {$invoice->id}, رقم المنتج: {$item->product_id}, المبلغ: " . ($item->price * $item->quantity));
                                }
                            }
                            $invoiceAmount += $categoryTotal;
                        }

                        // حساب إجمالي فواتير البيع لهذه الفترة (جميع الفئات)
                        $invoicesTotal = Invoice::where('created_by', '=', \Auth::user()->creatorId());
                        $invoicesTotal->whereRAW('YEAR(send_date) =?', [$year]);
                        $invoicesTotal->whereRAW('MONTH(send_date) >=?', $month[0]);
                        $invoicesTotal->whereRAW('MONTH(send_date) <=?', $month[1]);
                        $invoicesTotal = $invoicesTotal->get();

                        $invoiceTotalAmount = 0;
                        foreach($invoicesTotal as $invoiceTotal)
                        {
                            try {
                                $invoiceTotalAmount += $invoiceTotal->getTotal();
                            } catch (\Exception $e) {
                                // Log error or handle it silently
                                \Log::error('خطأ في حساب إجمالي الفاتورة: ' . $e->getMessage());
                            }
                        }

                        // حفظ البيانات - فواتير البيع فقط (إزالة Revenue و POS)
                        $monthIncomeArr[$monthName] = $invoiceAmount;
                        $incomeTotalArr[$monthName] = $invoiceTotalAmount;

                        // Log para depuración
                        \Log::info("Período: {$budget->period}, Categoría: {$cat->name}, Mes: {$monthName}, Monto: {$monthIncomeArr[$monthName]}");


                    }
                    $incomeArr[$cat->id] = $monthIncomeArr;


                }

            }
            // جلب جميع فئات المصروفات بدلاً من الفئات الموجودة في البيانات فقط
            // هذا يضمن ظهور جميع الفئات حتى لو لم تكن لها بيانات في الميزانية
            $expenseproduct = ProductServiceCategory::where('created_by', '=', \Auth::user()->creatorId())->where('type', '=', 'expense')->get();
            $expense_product = array_keys($budget->expense_data);

            $expenseArr = [];
            $expenseTotalArr = [];

            foreach($expenseproduct as $expense)
            {
                if($budget->period == 'monthly')
                {
                    $monthExpenseArr = [];
                    $monthTotalExpenseArr = [];
                    for($i = 1; $i <= 12; $i++)
                    {

                        $paymentAmount = Payment::where('created_by', '=', \Auth::user()->creatorId());
                        $paymentAmount->where('category_id', $expense->id);
                        $paymentAmount->whereRAW('YEAR(date) =?', [$year]);
                        $paymentAmount->whereRAW('MONTH(date) =?', [$i]);
                        $paymentAmount = $paymentAmount->sum('amount');

                        $paymentTotalAmount = Payment::where('created_by', '=', \Auth::user()->creatorId());
                        $paymentTotalAmount->whereRAW('YEAR(date) =?', [$year]);
                        $paymentTotalAmount->whereRAW('MONTH(date) =?', [$i]);
                        $paymentTotalAmount = $paymentTotalAmount->sum('amount');


                        $bills = Bill::where('created_by', '=', \Auth::user()->creatorId());
                        $bills->where('category_id', $expense->id);
                        $bills->whereRAW('YEAR(send_date) =?', [$year]);
                        $bills->whereRAW('MONTH(send_date) =?', [$i]);
                        $bills = $bills->with(['items','accounts'])->get();

                        $billAmount = 0;
                        foreach($bills as $bill)
                        {
                            $billAmount += $bill->getTotal();

                        }

                        $billsTotal = Bill::where('created_by', '=', \Auth::user()->creatorId());
                        $billsTotal->whereRAW('YEAR(send_date) =?', [$year]);
                        $billsTotal->whereRAW('MONTH(send_date) =?', [$i]);
                        $billsTotal = $billsTotal->get();

                        $billTotalAmount =0;
                        foreach($billsTotal as $billTotal)
                        {
                            $billTotalAmount += $billTotal->getTotal();
                        }

                        // Get Purchase data for this category and month
                        $purchases = Purchase::where('created_by', '=', \Auth::user()->creatorId());
                        $purchases->where('category_id', $expense->id);
                        $purchases->whereRAW('YEAR(purchase_date) =?', [$year]);
                        $purchases->whereRAW('MONTH(purchase_date) =?', [$i]);
                        $purchases = $purchases->with(['items'])->get();

                        $purchaseAmount = 0;
                        foreach($purchases as $purchase)
                        {
                            $purchaseAmount += $purchase->getTotal();
                        }

                        $purchasesTotal = Purchase::where('created_by', '=', \Auth::user()->creatorId());
                        $purchasesTotal->whereRAW('YEAR(purchase_date) =?', [$year]);
                        $purchasesTotal->whereRAW('MONTH(purchase_date) =?', [$i]);
                        $purchasesTotal = $purchasesTotal->get();

                        $purchaseTotalAmount = 0;
                        foreach($purchasesTotal as $purchaseTotal)
                        {
                            $purchaseTotalAmount += $purchaseTotal->getTotal();
                        }

                        $month                   = date("F", strtotime(date('Y-' . $i)));
                        $monthExpenseArr[$month] = $billAmount + $paymentAmount + $purchaseAmount;
                        $expenseTotalArr[$month] = $billTotalAmount + $paymentTotalAmount + $purchaseTotalAmount;


                    }
                    $expenseArr[$expense->id] = $monthExpenseArr;
                }

                else if($budget->period == 'quarterly' || $budget->period == 'half-yearly' || $budget->period == 'yearly')

                {
                    if($budget->period == 'quarterly')
                    {
                        $durations = $data['quarterly_monthlist'];
                    }
                    elseif($budget->period == 'yearly')
                    {
                        $durations = $data['yearly_monthlist'];
                    }
                    else
                    {
                        $durations = $data['half_yearly_monthlist'];
                    }

                    $monthExpenseArr = [];
                    foreach($durations as $monthnumber => $monthName)
                    {
                        $month         = explode('-', $monthnumber);
                        $paymentAmount = Payment::where('created_by', '=', \Auth::user()->creatorId());
                        $paymentAmount->where('category_id', $expense->id);
                        $paymentAmount->whereRAW('YEAR(date) =?', [$year]);
                        $paymentAmount->whereRAW('MONTH(date) >=?', $month[0]);
                        $paymentAmount->whereRAW('MONTH(date) <=?', $month[1]);
                        $paymentAmount = $paymentAmount->sum('amount');


                        $month         = explode('-', $monthnumber);
                        $paymentTotalAmount = Payment::where('created_by', '=', \Auth::user()->creatorId());
                        $paymentTotalAmount->whereRAW('YEAR(date) =?', [$year]);
                        $paymentTotalAmount->whereRAW('MONTH(date) >=?', $month[0]);
                        $paymentTotalAmount->whereRAW('MONTH(date) <=?', $month[1]);
                        $paymentTotalAmount = $paymentTotalAmount->sum('amount');

                        $bills = Bill::where('created_by', '=', \Auth::user()->creatorId());
                        $bills->where('category_id', $expense->id);
                        $bills->whereRAW('YEAR(send_date) =?', [$year]);
                        $bills->whereRAW('MONTH(send_date) >=?', $month[0]);
                        $bills->whereRAW('MONTH(send_date) <=?', $month[1]);
                        $bills = $bills->get();

                        $billAmount = 0;
                        foreach($bills as $bill)
                        {
                            $billAmount += $bill->getTotal();
                        }


                        $billsTotal = Bill::where('created_by', '=', \Auth::user()->creatorId());
                        $billsTotal->whereRAW('YEAR(send_date) =?', [$year]);
                        $billsTotal->whereRAW('MONTH(send_date) >=?', $month[0]);
                        $billsTotal->whereRAW('MONTH(send_date) <=?', $month[1]);
                        $billsTotal = $billsTotal->get();

                        $BillTotalAmount = 0;
                        foreach($billsTotal as $billTotal)
                        {
                            $BillTotalAmount += $billTotal->getTotal();
                        }

                        // Get Purchase data for this category and period
                        $purchases = Purchase::where('created_by', '=', \Auth::user()->creatorId());
                        $purchases->where('category_id', $expense->id);
                        $purchases->whereRAW('YEAR(purchase_date) =?', [$year]);
                        $purchases->whereRAW('MONTH(purchase_date) >=?', $month[0]);
                        $purchases->whereRAW('MONTH(purchase_date) <=?', $month[1]);
                        $purchases = $purchases->get();

                        $purchaseAmount = 0;
                        foreach($purchases as $purchase)
                        {
                            $purchaseAmount += $purchase->getTotal();
                        }

                        $purchasesTotal = Purchase::where('created_by', '=', \Auth::user()->creatorId());
                        $purchasesTotal->whereRAW('YEAR(purchase_date) =?', [$year]);
                        $purchasesTotal->whereRAW('MONTH(purchase_date) >=?', $month[0]);
                        $purchasesTotal->whereRAW('MONTH(purchase_date) <=?', $month[1]);
                        $purchasesTotal = $purchasesTotal->get();

                        $purchaseTotalAmount = 0;
                        foreach($purchasesTotal as $purchaseTotal)
                        {
                            $purchaseTotalAmount += $purchaseTotal->getTotal();
                        }

                        $monthExpenseArr[$monthName] = $billAmount + $paymentAmount + $purchaseAmount;
                        $expenseTotalArr[$monthName] = $BillTotalAmount + $paymentTotalAmount + $purchaseTotalAmount;

                    }
                    $expenseArr[$expense->id] = $monthExpenseArr;

                }

                // Define selected categories
                $selectedCategories = ['all']; // Default: all categories
                if (isset($budget['display_settings']) && isset($budget['display_settings']['category_filter'])) {
                    if (is_string($budget['display_settings']['category_filter'])) {
                        $decodedCategories = json_decode($budget['display_settings']['category_filter'], true);
                        if (is_array($decodedCategories)) {
                            $selectedCategories = $decodedCategories;
                        } else {
                            $selectedCategories = [$budget['display_settings']['category_filter']];
                        }
                    } else if (is_array($budget['display_settings']['category_filter'])) {
                        $selectedCategories = $budget['display_settings']['category_filter'];
                    }
                }

                // Log selected categories for debugging
                \Log::info("Selected categories: " . json_encode($selectedCategories));

                // Create arrays for filtered totals
                $filteredIncomeTotalArr = [];
                $filteredExpenseTotalArr = [];
                $filteredBudgetTotal = [];
                $filteredBudgetExpenseTotal = [];

                // If "all" is selected, use all categories
                if (in_array('all', $selectedCategories)) {
                    $filteredIncomeTotalArr = $incomeTotalArr;
                    $filteredExpenseTotalArr = $expenseTotalArr;
                    $filteredBudgetTotal = $budgetTotal;
                    $filteredBudgetExpenseTotal = $budgetExpenseTotal;

                    // Log for debugging
                    \Log::info("Using all categories");
                } else {
                    // Filter income totals
                    foreach ($incomeproduct as $income) {
                        if (in_array('income-'.$income->id, $selectedCategories)) {
                            // Log for debugging
                            \Log::info("Including income category: {$income->name} (ID: {$income->id})");

                            // Add this category's totals to the filtered totals
                            if (isset($incomeArr[$income->id])) {
                                foreach ($incomeArr[$income->id] as $month => $amount) {
                                    $filteredIncomeTotalArr[$month] = (isset($filteredIncomeTotalArr[$month]) ? $filteredIncomeTotalArr[$month] : 0) + $amount;
                                }
                            }

                            // Add budget totals for this category
                            if (isset($budget['income_data'][$income->id])) {
                                foreach ($budget['income_data'][$income->id] as $month => $amount) {
                                    $filteredBudgetTotal[$month] = (isset($filteredBudgetTotal[$month]) ? $filteredBudgetTotal[$month] : 0) + $amount;
                                }
                            }
                        }
                    }

                    // Filter expense totals
                    foreach ($expenseproduct as $expense) {
                        if (in_array('expense-'.$expense->id, $selectedCategories)) {
                            // Log for debugging
                            \Log::info("Including expense category: {$expense->name} (ID: {$expense->id})");

                            // Add this category's totals to the filtered totals
                            if (isset($expenseArr[$expense->id])) {
                                foreach ($expenseArr[$expense->id] as $month => $amount) {
                                    $filteredExpenseTotalArr[$month] = (isset($filteredExpenseTotalArr[$month]) ? $filteredExpenseTotalArr[$month] : 0) + $amount;
                                }
                            }

                            // Add budget totals for this category
                            if (isset($budget['expense_data'][$expense->id])) {
                                foreach ($budget['expense_data'][$expense->id] as $month => $amount) {
                                    $filteredBudgetExpenseTotal[$month] = (isset($filteredBudgetExpenseTotal[$month]) ? $filteredBudgetExpenseTotal[$month] : 0) + $amount;
                                }
                            }
                        }
                    }
                }

                // Log filtered totals for debugging
                \Log::info("Filtered income totals: " . json_encode($filteredIncomeTotalArr));
                \Log::info("Filtered expense totals: " . json_encode($filteredExpenseTotalArr));
                \Log::info("Filtered budget totals: " . json_encode($filteredBudgetTotal));
                \Log::info("Filtered budget expense totals: " . json_encode($filteredBudgetExpenseTotal));

                // Replace original arrays with filtered arrays
                $incomeTotalArr = !empty($filteredIncomeTotalArr) ? $filteredIncomeTotalArr : [];
                $expenseTotalArr = !empty($filteredExpenseTotalArr) ? $filteredExpenseTotalArr : [];
                $budgetTotal = !empty($filteredBudgetTotal) ? $filteredBudgetTotal : [];
                $budgetExpenseTotal = !empty($filteredBudgetExpenseTotal) ? $filteredBudgetExpenseTotal : [];

                // NET PROFIT OF BUDGET
                $budgetprofit = [];
                $keys   = array_keys($budgetTotal + $budgetExpenseTotal);
                foreach($keys as $v)
                {
                    $budgetprofit[$v] = (empty($budgetTotal[$v]) ? 0 : $budgetTotal[$v]) - (empty($budgetExpenseTotal[$v]) ? 0 : $budgetExpenseTotal[$v]);
                }
                $data['budgetprofit'] = $budgetprofit;

                // NET PROFIT OF ACTUAL
                $actualprofit = [];
                $keys   = array_keys($incomeTotalArr + $expenseTotalArr);
                foreach($keys as $v)
                {
                    $actualprofit[$v] = (empty($incomeTotalArr[$v]) ? 0 : $incomeTotalArr[$v]) - (empty($expenseTotalArr[$v]) ? 0 : $expenseTotalArr[$v]);
                }
                $data['actualprofit'] = $actualprofit;

            }

            // Get all income and expense categories for filter dropdown
            $allIncomeproduct = ProductServiceCategory::where('created_by', '=', \Auth::user()->creatorId())->where('type', '=', 'income')->get();
            $allExpenseproduct = ProductServiceCategory::where('created_by', '=', \Auth::user()->creatorId())->where('type', '=', 'expense')->get();

            // Asegurarse de que los datos de ingresos se muestren correctamente en las categorías
            $budgetTotal = [];
            $monthList = $budget->period == 'monthly' ? $this->yearMonth() : ($budget->period == 'quarterly' ? $data['quarterly_monthlist'] : ($budget->period == 'half-yearly' ? $data['half_yearly_monthlist'] : $data['yearly_monthlist']));

            // Depuración de los datos
            \Log::info("Período: {$budget->period}");
            \Log::info("Categorías de ingresos: " . $incomeproduct->pluck('name')->implode(', '));
            \Log::info("Datos de ingresos: " . json_encode($incomeArr));

            foreach ($monthList as $month => $monthName) {
                if (!is_string($month)) {
                    $month = $monthName;
                }
                $budgetTotal[$month] = 0;
                foreach ($incomeproduct as $cat) {
                    if (isset($budget['income_data'][$cat->id][$month])) {
                        $budgetTotal[$month] += $budget['income_data'][$cat->id][$month];
                    }
                }
            }

            return view('budget.show', compact('id', 'budget', 'incomeproduct', 'expenseproduct', 'incomeArr', 'expenseArr', 'incomeTotalArr','expenseTotalArr','budgetTotal','budgetExpenseTotal', 'allIncomeproduct', 'allExpenseproduct'
            ), $data);

        }
        else
        {
            return response()->json(['error' => __('Permission denied.')], 401);
        }



    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param \App\Models\Budget $budget
     *
     * @return \Illuminate\Http\Response
     */
    public function edit($ids)
    {

        if(\Auth::user()->can('edit budget plan'))
        {
            try {
                $id       = Crypt::decrypt($ids);
            } catch (\Throwable $th) {
                return redirect()->back()->with('error', __('Budget Not Found.'));
            }
            $id     = Crypt::decrypt($ids);
            $budget = Budget::find($id);

            $budget['income_data']  = json_decode($budget->income_data, true);
            $budget['expense_data'] = json_decode($budget->expense_data, true);

            // فك تشفير إعدادات العرض
            if (is_string($budget->display_settings)) {
                $budget['display_settings'] = json_decode($budget->display_settings, true);
            } else {
                $budget['display_settings'] = $budget->display_settings;
            }

            $periods = Budget::$period;

            $data['monthList'] = $month = $this->yearMonth();        //Monthly

            $data['quarterly_monthlist'] = [                      //Quarterly
                                                                  'Jan-Mar',
                                                                  'Apr-Jun',
                                                                  'Jul-Sep',
                                                                  'Oct-Dec',
            ];

            $data['half_yearly_monthlist'] = [                      // Half - Yearly
                                                                    'Jan-Jun',
                                                                    'Jul-Dec',
            ];

            $data['yearly_monthlist'] = [                           // Yearly
                                                                    'Jan-Dec',
            ];


            $data['yearList'] = $this->yearList();


            $incomeproduct  = ProductServiceCategory::where('created_by', '=', \Auth::user()->creatorId())->where('type', '=', 'income')->get();
            $expenseproduct = ProductServiceCategory::where('created_by', '=', \Auth::user()->creatorId())->where('type', '=', 'expense')->get();


            return view('budget.edit', compact('periods', 'budget', 'incomeproduct', 'expenseproduct'), $data);
        }

        else
        {
            return redirect()->back()->with('error', __('Permission denied.'));
        }


    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Budget $budget
     *
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Budget $budget)
    {

        if(\Auth::user()->can('edit budget plan'))
        {
            if($budget->created_by == \Auth::user()->creatorId())
            {
                $validator = \Validator::make($request->all(), [
                    'name' => 'required',
                    'period' => 'required',

                ]);
                if($validator->fails())
                {
                    $messages = $validator->getMessageBag();

                    return redirect()->back()->with('error', $messages->first());
                }

                // تشخيص البيانات المرسلة
                \Log::info("بيانات الدخل المرسلة: " . json_encode($request->income));
                \Log::info("بيانات المصروفات المرسلة: " . json_encode($request->expense));
                \Log::info("إعدادات العرض المرسلة: " . json_encode($request->display_settings));

                $budget->name         = $request->name;
                $budget->from         = $request->year;
                $budget->period       = $request->period;

                // التأكد من أن البيانات ليست فارغة قبل التشفير
                $incomeData = $request->income ?? [];
                $expenseData = $request->expense ?? [];

                // إزالة القيم الفارغة والصفرية إذا لزم الأمر
                $incomeData = array_filter($incomeData, function($categoryData) {
                    return is_array($categoryData) && array_filter($categoryData, function($value) {
                        return !empty($value) && $value != '0';
                    });
                });

                $expenseData = array_filter($expenseData, function($categoryData) {
                    return is_array($categoryData) && array_filter($categoryData, function($value) {
                        return !empty($value) && $value != '0';
                    });
                });

                \Log::info("بيانات الدخل بعد التنظيف: " . json_encode($incomeData));
                \Log::info("بيانات المصروفات بعد التنظيف: " . json_encode($expenseData));

                $budget->income_data  = json_encode($request->income); // حفظ البيانات الأصلية
                $budget->expense_data = json_encode($request->expense); // حفظ البيانات الأصلية

                // Ensure display_settings is properly saved
                $displaySettings = [];

                // Set budget type (income, expense, or both)
                $displaySettings['budget_type'] = isset($request->display_settings['budget_type']) ?
                    $request->display_settings['budget_type'] : 'both';

                // Handle category filter
                \Log::info("فلتر الفئات الخام: " . json_encode($request->display_settings['category_filter'] ?? null));

                if (isset($request->display_settings['category_filter'])) {
                    $categoryFilter = $request->display_settings['category_filter'];

                    // If it's already a JSON string, decode and re-encode to ensure consistency
                    if (is_string($categoryFilter)) {
                        $decodedFilter = json_decode($categoryFilter, true);
                        if (is_array($decodedFilter)) {
                            $displaySettings['category_filter'] = json_encode($decodedFilter);
                        } else {
                            // If not valid JSON, treat as single value
                            $displaySettings['category_filter'] = json_encode([$categoryFilter]);
                        }
                    }
                    // If it's an array (which it should be with multiple select), encode it
                    else if (is_array($categoryFilter)) {
                        // Remove empty values and ensure we have valid data
                        $cleanFilter = array_filter($categoryFilter, function($value) {
                            return !empty($value) && $value !== '';
                        });

                        if (empty($cleanFilter)) {
                            $displaySettings['category_filter'] = json_encode(['all']);
                        } else {
                            $displaySettings['category_filter'] = json_encode(array_values($cleanFilter));
                        }
                    }
                    // Otherwise, treat as single value
                    else {
                        $displaySettings['category_filter'] = json_encode([$categoryFilter]);
                    }
                } else {
                    // Default to all categories if not provided
                    $displaySettings['category_filter'] = json_encode(['all']);
                }

                \Log::info("فلتر الفئات النهائي: " . $displaySettings['category_filter']);

                $budget->display_settings = json_encode($displaySettings);
                $budget->save();


                return redirect()->route('budget.index')->with('success', __('Budget Plan successfully updated.'));
            }
            else
            {
                return redirect()->back()->with('error', __('Permission denied.'));
            }
        }
        else
        {
            return response()->json(['error' => __('Permission denied.')], 401);
        }



    }

    /**
     * Remove the specified resource from storage.
     *
     * @param \App\Models\Budget $budget
     *
     * @return \Illuminate\Http\Response
     */
    public function destroy(Budget $budget)
    {
        if(\Auth::user()->can('delete budget plan'))
        {
            if($budget->created_by == \Auth::user()->creatorId())
            {
                $budget->delete();
                return redirect()->route('budget.index')->with('success', __('Budget Plan successfully deleted.'));
            }
            else
            {
                return redirect()->back()->with('error', __('Permission denied.'));
            }
        }
        else
        {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

    }


    public function yearMonth()
    {

        $month[] = 'January';
        $month[] = 'February';
        $month[] = 'March';
        $month[] = 'April';
        $month[] = 'May';
        $month[] = 'June';
        $month[] = 'July';
        $month[] = 'August';
        $month[] = 'September';
        $month[] = 'October';
        $month[] = 'November';
        $month[] = 'December';

        return $month;
    }


    public function yearList()
    {
        $starting_year = date('Y', strtotime('-5 year'));
        $ending_year   = date('Y');

        foreach(range($ending_year, $starting_year) as $year)
        {
            $years[$year] = $year;
        }

        return $years;
    }

}
