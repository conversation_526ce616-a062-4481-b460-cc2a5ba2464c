{"__meta": {"id": "X3c6fc76a623e9c0f6896a100f169ff8d", "datetime": "2025-06-26 16:00:17", "utime": **********.574991, "method": "GET", "uri": "/", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.212368, "end": **********.575006, "duration": 0.362637996673584, "duration_str": "363ms", "measures": [{"label": "Booting", "start": **********.212368, "relative_start": 0, "end": **********.537147, "relative_end": **********.537147, "duration": 0.32477903366088867, "duration_str": "325ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.537157, "relative_start": 0.32478904724121094, "end": **********.575008, "relative_end": 1.9073486328125e-06, "duration": 0.03785085678100586, "duration_str": "37.85ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44018408, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET /", "middleware": "web, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@landingpage", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=59\" onclick=\"\">app/Http/Controllers/DashboardController.php:59-74</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.00147, "accumulated_duration_str": "1.47ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Controllers\\DashboardController.php", "line": 66}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.5627859, "duration": 0.00147, "duration_str": "1.47ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 100}]}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "c7Olbm9j3VGWU97WwGbmhGgptmjEatJHEweNGUI4", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1497239543 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1497239543\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-89345009 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-89345009\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-930583603 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1842 characters\">_clck=1lqc4d8%7C2%7Cfx3%7C0%7C2003; _clsk=1h4itwx%7C1750953610407%7C6%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkZnOC9yTWgxNGJBUVR0aGJGK0kvaGc9PSIsInZhbHVlIjoiQ2ZhdXg3TUZWR2ZyVEE2YUh6RCtscGJMTnVaMjVlODdBbXd0NkVmKzdjTEZhcThUM0ZXbmEvbnlUTDN2YXpxd24xbjBhNlRZUFpveElmTk95dG1rcEVwRVpvZGZHeGxyR1ZBWlNqMlpDNnNtNGViWDVpT2JVY3M5aXNWV0p5U1pTMEtMTERHZVR0S3FRUVNvcXdPVVVIN0ZpUFJnNUtUNXBYOFhKRUhGeHlLczZUcm1pMnU1d1hBSkJvZHdveGprQlFCSTlRUERwVTVrYTZvYTNrcUc2Qm9ZempoSmp1SVUvZ3E5U3lwK3RPd0k4d1hKM281VjAxd3JpUHExYnllZTUyK2FyOE1NZXNrRWE5cUNCYXEwUTJ4bjF0LzRkQWZJTWdmRmt4S0lxMzBqaUREM0hQS3hVaDM3MnVrMlAvOThWL0dBNUVldmY3N3p3U1VwMTM3QTBDM0I4YXJSUVVBVEk1cnNQU3MxMHpNUXBDVFFjcXBSZjZFSFVqMW5FOXpnbzF5THVsRlRHL25pNmkzang1Z3JlOWdUdm44eVo5OS9HcUgxcU1nZHVtTFRBaDBUWkRPajNjeHBRSDRiNy9sdHlPbHJGanY4UzJrMDhyeUwydnRMV3FGZHlubmZZSXQ4c1pEaDVNM3U0aUYxelJRWXhyNzFMQy96cGNCVm1UVmciLCJtYWMiOiIzZThlNTYwYjY3NWExOTFmYmM5YjM0YWVhNjVjN2JmMzMzYmRkNjM2MzE4ZTYxNmExOGZiN2Q2MTE2OTUwNmZjIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InVKY0ZVY1BhbmlaUmttZUFFMlRQMHc9PSIsInZhbHVlIjoiUFFYQzJ1T1pKVXk3MU1ISjRhZVdyZjBveFAyL2V2RUQ1Y1pWZytNWmpmZXllNzQzM0VmbE84YkN2c28wUzZUVGxBT096R2tZK21OakRKUkFCSzBXdTFhU0FhVzdsZHlrNGl0QzlUVThidmxuclYySFFpQ0ZvWmM0N3lVTHBtUEo4QzVFYTlEZ1IzUklwdG5ScGZSUVBibjJkQ0krMVBERU4xMlZhZmNxaGFNREw2TXA3WFFjV0tsd1hFSkd3MnA4WFVmOHFBSjFCZnVlZEFHbFNKbnhnVHdnZEF0WTdiaGFvM3g2RmcvQ09uSktOV3ZVWTJ4VUo3YlNsc3BYcllobGNaUDhoWmJEVHlsMFE1b0xPZE41Z1p3bFlIUnYxOWFlK1B3OU9hQ2huTHc0WEVYbEZQcUNITm1lbmFYNEdBV3UzMlhxblczSTdVVDgyZHdrenFWUnFDeVhibXRSeU55Y0RxL0xPbmhiVjlmNE5zWkhoRXV0ck5QbkFSdE1EZVRqbTlqaEorUlF0aE02Tkd0ZldXc0tjYUF0VFdURnRqa2NxNlVZblh5OGZscGMwSldFTjVDL0NOWG05cW1BWEFqSFg0ZWxXTGtTVjFPL0lwM3Bka0NvVVlSMUtzN2JTT3Z4WWhnY3cxN3lVdzdDYWRINzFXNjdXcWRNSjE0dzVENEQiLCJtYWMiOiJiNDkzYWMyMGRjODhiMzZiOWE3YjVjMDIxODEzNTczOGRjN2ZmZTY1NzNiYWY1ODQ2MDg3MjhlNzQ4ZjQ5OWQ4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-930583603\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1742055325 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c7Olbm9j3VGWU97WwGbmhGgptmjEatJHEweNGUI4</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ISP8BnnxcALCnxfnm8xYKkDYjUF7hhDCxrzDRCoE</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1742055325\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1214904451 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 16:00:17 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkNybnFmUjNDODVDS1RlWmZyQmcyWkE9PSIsInZhbHVlIjoiRm51YjZodE12MkowUWlaSlo5V3ZyZkFUZXhjVXZTYzVLbjRLR1dkU1phc1dCSERDaTN3ZzlqSm1idDh4OTZGTEJRWTA1c1JudGpld2lqYnY4RXk1QktBelQ4V09EdUJWaVY3eVc0Ukp0QnFNcWluS2VDMjlqRVphVjRMVHl5ZVBrY0E2ZW05NkhWTzZ5NlAySWl4M3hsK0x4MFU5bzNXZ3BLM2hmaXVabzNnSHJlc0M1MWdRWDUwSnBGa21JUjBLUnNlZzNSaWtxRnV3VkwzWVBacDRPYW01QlFBbUsvL0J0MGgxeUtWQnRYeEFxVEdwRHYxK3kyVUdzUzc0TVY0WGM5a0VRQjJJOGl0LzJ4ejBFdzFrU3Y2Q0RRUWk4SVljRSthUU5VT2ZjTDhsSWQrbVN2bVl3V3ZnMitaUFd6TnZ5UXJKL2xhenFIekhsakhqSy80bHFuTFNueXlJZDlWNnJLOFRHRGFmT1hYdDNLODYwWjZGc0IydTBLWWxvdG4rUW1QcDFoYm5RZHM2b3RXdzdnTDBrZ1o5RC9Za1lGUnYyNk03MUZ3VTFSZnZBM2lEcFNvUktUUTNkUVpzQ0tQb094UlZQTm4rT043VEdRM1ZBejBWOTFKQ1lNMWFzOEJXQnpEdEIxeElZT0pmQUh0ZGhpVE5lclZUcEVjUEpIaVciLCJtYWMiOiJkNjUxODM3ZjNkYzA4MTVkNDU5OTM4MjQxNGQwNTI2Zjk4M2VmZTVmNDg1YzA4MTM0YTViNGExMmIyYTdlODczIiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 18:00:17 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjNUMm1IYnh3cUw2cWtwSkY5YzFGSEE9PSIsInZhbHVlIjoiZ09wZ3NYb0x6K2NXMFR5S0xZNjJpSWkxMUN3MkZobXRKa0hEVHFJODgrUmZpVmIxZi9hM0hpSkJYbUJqdGtxc1dWdGVJM1UyeTNydVZKNXRrakZIVEtTWmpiYURCMGRMaUlLZ1FhQy9uellUdk1scXk4NUNlQnF6OE9jbGs4R2pOZkJyVVBsVkI4NXlYaFczdGtON3hsNXVXVmFQanBoSmZQQ1pKODcvR014V1h0ckd4VWxseVdpcm1lRWwvTmFlY1BHRWRBTXo2VWhYSDNjYXpDb2ptT1RzVGtZSU9lckh6NEJMQVZBVjk0U1Zoa1YzbVk5NmpBL3JPZ050bnRLU1RGRkJnVjUydmtlNFVhdERqVVpzNW5ud2k2L3oxa2FWWFlmeEJVRTJwT2RjQVkzd21uelFDYW96ajBDNk1hV3RrZWdlak95ZCsvTERabFIyR3F5NVExZnBHUnNCL05uS0ljYWZWZWs0UFFQYkdVckZXVHRhMzZjbm9QZ1VUQ2cvWXBmL3UwYVRjcWVWVThpWThSS1BPZ1V6YitnWUtnd3orZUNZekY4Qmd2VXR1aHhjOEdpdW1IMVhuSkx5Z2xpaUpmcXVNSzJscWhvNXJTVTV6L1ZyWjJaWldWMFRUSGF6V2Nwem9KRWxpSnEwcG5iRVUvMzREcWRMSFNYdlEvTEciLCJtYWMiOiIzZmFiYjgzYTFkMWY4YWU2NDczOTg5NTg2MGU0MjY5ZDRlNDcwY2FmODk5MGM3MjE1YmZhMjg2NjIyMzU1MDlmIiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 18:00:17 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkNybnFmUjNDODVDS1RlWmZyQmcyWkE9PSIsInZhbHVlIjoiRm51YjZodE12MkowUWlaSlo5V3ZyZkFUZXhjVXZTYzVLbjRLR1dkU1phc1dCSERDaTN3ZzlqSm1idDh4OTZGTEJRWTA1c1JudGpld2lqYnY4RXk1QktBelQ4V09EdUJWaVY3eVc0Ukp0QnFNcWluS2VDMjlqRVphVjRMVHl5ZVBrY0E2ZW05NkhWTzZ5NlAySWl4M3hsK0x4MFU5bzNXZ3BLM2hmaXVabzNnSHJlc0M1MWdRWDUwSnBGa21JUjBLUnNlZzNSaWtxRnV3VkwzWVBacDRPYW01QlFBbUsvL0J0MGgxeUtWQnRYeEFxVEdwRHYxK3kyVUdzUzc0TVY0WGM5a0VRQjJJOGl0LzJ4ejBFdzFrU3Y2Q0RRUWk4SVljRSthUU5VT2ZjTDhsSWQrbVN2bVl3V3ZnMitaUFd6TnZ5UXJKL2xhenFIekhsakhqSy80bHFuTFNueXlJZDlWNnJLOFRHRGFmT1hYdDNLODYwWjZGc0IydTBLWWxvdG4rUW1QcDFoYm5RZHM2b3RXdzdnTDBrZ1o5RC9Za1lGUnYyNk03MUZ3VTFSZnZBM2lEcFNvUktUUTNkUVpzQ0tQb094UlZQTm4rT043VEdRM1ZBejBWOTFKQ1lNMWFzOEJXQnpEdEIxeElZT0pmQUh0ZGhpVE5lclZUcEVjUEpIaVciLCJtYWMiOiJkNjUxODM3ZjNkYzA4MTVkNDU5OTM4MjQxNGQwNTI2Zjk4M2VmZTVmNDg1YzA4MTM0YTViNGExMmIyYTdlODczIiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 18:00:17 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjNUMm1IYnh3cUw2cWtwSkY5YzFGSEE9PSIsInZhbHVlIjoiZ09wZ3NYb0x6K2NXMFR5S0xZNjJpSWkxMUN3MkZobXRKa0hEVHFJODgrUmZpVmIxZi9hM0hpSkJYbUJqdGtxc1dWdGVJM1UyeTNydVZKNXRrakZIVEtTWmpiYURCMGRMaUlLZ1FhQy9uellUdk1scXk4NUNlQnF6OE9jbGs4R2pOZkJyVVBsVkI4NXlYaFczdGtON3hsNXVXVmFQanBoSmZQQ1pKODcvR014V1h0ckd4VWxseVdpcm1lRWwvTmFlY1BHRWRBTXo2VWhYSDNjYXpDb2ptT1RzVGtZSU9lckh6NEJMQVZBVjk0U1Zoa1YzbVk5NmpBL3JPZ050bnRLU1RGRkJnVjUydmtlNFVhdERqVVpzNW5ud2k2L3oxa2FWWFlmeEJVRTJwT2RjQVkzd21uelFDYW96ajBDNk1hV3RrZWdlak95ZCsvTERabFIyR3F5NVExZnBHUnNCL05uS0ljYWZWZWs0UFFQYkdVckZXVHRhMzZjbm9QZ1VUQ2cvWXBmL3UwYVRjcWVWVThpWThSS1BPZ1V6YitnWUtnd3orZUNZekY4Qmd2VXR1aHhjOEdpdW1IMVhuSkx5Z2xpaUpmcXVNSzJscWhvNXJTVTV6L1ZyWjJaWldWMFRUSGF6V2Nwem9KRWxpSnEwcG5iRVUvMzREcWRMSFNYdlEvTEciLCJtYWMiOiIzZmFiYjgzYTFkMWY4YWU2NDczOTg5NTg2MGU0MjY5ZDRlNDcwY2FmODk5MGM3MjE1YmZhMjg2NjIyMzU1MDlmIiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 18:00:17 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1214904451\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-857262758 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c7Olbm9j3VGWU97WwGbmhGgptmjEatJHEweNGUI4</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-857262758\", {\"maxDepth\":0})</script>\n"}}