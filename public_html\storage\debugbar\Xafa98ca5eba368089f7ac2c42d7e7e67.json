{"__meta": {"id": "Xafa98ca5eba368089f7ac2c42d7e7e67", "datetime": "2025-06-26 16:05:41", "utime": **********.873194, "method": "GET", "uri": "/add-to-cart/2301/pos", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.390435, "end": **********.873219, "duration": 0.4827840328216553, "duration_str": "483ms", "measures": [{"label": "Booting", "start": **********.390435, "relative_start": 0, "end": **********.777911, "relative_end": **********.777911, "duration": 0.38747596740722656, "duration_str": "387ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.777921, "relative_start": 0.38748598098754883, "end": **********.873222, "relative_end": 3.0994415283203125e-06, "duration": 0.09530115127563477, "duration_str": "95.3ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 49211632, "peak_usage_str": "47MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET add-to-cart/{id}/{session}", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@addToCart", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1320\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1320-1544</a>"}, "queries": {"nb_statements": 7, "nb_failed_statements": 0, "accumulated_duration": 0.00653, "accumulated_duration_str": "6.53ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 17 limit 1", "type": "query", "params": [], "bindings": ["17"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.821272, "duration": 0.00166, "duration_str": "1.66ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 25.421}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.831185, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 25.421, "width_percent": 10.107}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 17 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["17", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.845612, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 35.528, "width_percent": 11.332}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (17) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.847917, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 46.861, "width_percent": 5.666}, {"sql": "select * from `product_services` where `product_services`.`id` = '2301' limit 1", "type": "query", "params": [], "bindings": ["2301"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1324}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.853166, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1324", "source": "app/Http/Controllers/ProductServiceController.php:1324", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1324", "ajax": false, "filename": "ProductServiceController.php", "line": "1324"}, "connection": "kdmkjkqknb", "start_percent": 52.527, "width_percent": 6.891}, {"sql": "select sum(`quantity`) as aggregate from `warehouse_products` where `product_id` = 2301 and exists (select * from `warehouses` where `warehouse_products`.`warehouse_id` = `warehouses`.`id` and `created_by` = 15)", "type": "query", "params": [], "bindings": ["2301", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/ProductService.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\ProductService.php", "line": 155}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1328}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.857017, "duration": 0.00234, "duration_str": "2.34ms", "memory": 0, "memory_str": null, "filename": "ProductService.php:155", "source": "app/Models/ProductService.php:155", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FProductService.php&line=155", "ajax": false, "filename": "ProductService.php", "line": "155"}, "connection": "kdmkjkqknb", "start_percent": 59.418, "width_percent": 35.835}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 4748}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 4682}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1397}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.860665, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4748", "source": "app/Models/Utility.php:4748", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUtility.php&line=4748", "ajax": false, "filename": "Utility.php", "line": "4748"}, "connection": "kdmkjkqknb", "start_percent": 95.253, "width_percent": 4.747}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 17,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2020512243 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>17</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2020512243\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.852305, "xdebug_link": null}]}, "session": {"_token": "PKqZteC3IabV2rdCxsXBMLnTb72fzH4qdR2KdQSn", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "17", "pos": "array:4 [\n  2295 => array:9 [\n    \"name\" => \"منتوس علكة ابيض نعناع 54جم\"\n    \"quantity\" => 2\n    \"price\" => \"15.00\"\n    \"id\" => \"2295\"\n    \"tax\" => 0\n    \"subtotal\" => 30.0\n    \"originalquantity\" => 5\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  2296 => array:8 [\n    \"name\" => \"بطيخ إضافي 60 ثانية\"\n    \"quantity\" => 2\n    \"price\" => \"18.00\"\n    \"tax\" => 0\n    \"subtotal\" => 36.0\n    \"id\" => \"2296\"\n    \"originalquantity\" => 2\n    \"product_tax\" => \"-\"\n  ]\n  2297 => array:8 [\n    \"name\" => \"ساديا - دجاج مجمّد 800جم\"\n    \"quantity\" => 2\n    \"price\" => \"16.00\"\n    \"tax\" => 0\n    \"subtotal\" => 32.0\n    \"id\" => \"2297\"\n    \"originalquantity\" => 6\n    \"product_tax\" => \"-\"\n  ]\n  2301 => array:8 [\n    \"name\" => \"بريكستا ويفر المقرمش والمغطى بشوكولاتة الحليب 24 حبة\"\n    \"quantity\" => 1\n    \"price\" => \"13.00\"\n    \"tax\" => 0\n    \"subtotal\" => 13.0\n    \"id\" => \"2301\"\n    \"originalquantity\" => 0\n    \"product_tax\" => \"-\"\n  ]\n]"}, "request": {"path_info": "/add-to-cart/2301/pos", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-73021503 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-73021503\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1093413107 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1093413107\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1976033123 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PKqZteC3IabV2rdCxsXBMLnTb72fzH4qdR2KdQSn</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1bowxa%7C2%7Cfx3%7C0%7C1999; _clsk=1osc4j2%7C1750953875247%7C2%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjNiSUFnNGQvN3BYSjFmNndvVWJYOFE9PSIsInZhbHVlIjoiVFNKayt5YkFVamFiWC9xRFFYSnRsdUJRK1BYT1VVUytVNEFUNXFHMTBobFpmd2JnQ2RwSEYyVmExZ0sxUzloc3BRd2MvTDgyZ3p4NE9EWS9tV2FobEhmM0xPd1YraTluZ0tUZmttdmtUbEdEZ29GVU1yUjRCcmpBTHI5ZXRYaWhPamRjNXNoZExWaG0rZ1RHRlJPYm9wVi93ZUVsemorY283YVFvS1Z0OWVtZ2NGbllJT3BUeW9MT0JGZjU1QmpNcStyTEhyOCt4U3V5aU15M3QyZ2NxdC9PY0NjNzZ4bmc5QmNjVVVWSFNHNmRPSGYySEJxVlkrS0FDNzNoZjhGN2ZTZnJaZmlwRVV3M3hrOXozYXdlOXE5a3hRNzQyMmhzU2swckEzMGd2c1llSVAzTW9WVlJkY1RmVVNpYkhkMVlGc1hlZTBlempVa2ZBVFMyc0owR0lKem5JZFZwVUw2RGRpVk54UkxOMm1Za1hLMGlZNDNFbVFTc1pDeEp4bUZXYVJxd1k2OWtxK1NzelpWaXQ2VmRJOEs2M1J6M0xzVjZRSDU0Wk1pdWRnWDN5TEgxMVRHVENCUFRvR2xFY1FkS0xHWnpLSm1YVUZFNG1PdXFTck93MTZZSnNsMTdTRUt4ZEJPZXdqR3ZpQm9wR0U4cmR2SFlRWXQ4WXlZVkl4REEiLCJtYWMiOiI2ZTc2MTE4N2FkNTU3YzA4ODJmNmQzOTJhZWY5ZjVjMDZlZGFkMjdiNTM2M2Y5NTRjMmE4NzcwMTlkMjNkM2VhIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjN6U0Nxc0VtS2Y0dE5Eam5ocmV1Rmc9PSIsInZhbHVlIjoic2xwd1pwK0E1eXM3b1h5OGJRMWlMMHhUa0RWcnRLVVp6ZnJ4QVdhNlVkTThzVHdNcWdWTm05bzJ3bHNvWWg0azdRRjVmYzBKK09HcFJ5YUNiQWpYUFRvY0V2dW9CNGh0cCthV25tN1I0aC9RRnhRVEIwTjRucXlZN0JpM1czZW9vZkk4elhMR2JvK3JBS0h1TXQ2eG5CQVJDY21zcmZKWFE5V01iYmRSTE9YbjEvY1JuTE1LMXpjK21OUG9oRFpDY2NrS1dXM1cxREtFeHlBekxydVpHQWNuWCsxNnh0dW9qcmtLUVRvekVQNWorZ2pGL0xDVW1YOXhTTjhiQ2RFTHRqTWxVL3VkMEY3V2Zaajh4eERzQ2R4RjZtUlgwMGVzbkVDZ0xpcXhScmZmMGY4M1h3a2MzaUhrQ0xLdVpjZFFTd285cXNzYzJSTEh6SmZqbklqQnNiMkFJWUhrRkYvZHYvRTN6RUNCcDhFcHJsYmpseXc5MzVJNjg5SllLYTIzNm82NkN0aXFHa2xQbmVkbGtLblBHL2dLL3U2b3BGUmxrUTJML3ZIeU1Rbzc2SzYyeXpaMVhPU3dOTDV6RTdnMlc1WjZMb0UxVWhDbDcwMWJOUDJBV29nZGZ4R3FUUFlON3ljdk1VMU90dFY3S3lBUm11a25lM0Ria0JXMlZzRXIiLCJtYWMiOiI4YzVhNDNhZGI3NDAwMzE3ZGJlMjBmYjIwZjA0N2I5NjVjMGM4ZDFjN2RjYmRlNTIyMjE0MjAwNTA2OWE0ZDU0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1976033123\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PKqZteC3IabV2rdCxsXBMLnTb72fzH4qdR2KdQSn</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GUUMfjgJKgBqP5oaDcRP4Rfu0FM4k6V9zWwhkyoG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-602404303 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 16:05:41 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkgvcXBZNkdYd1FjUjNQNjZWSHgvVkE9PSIsInZhbHVlIjoiZVhtamczRkZsS3BqSDRFYjZVVmdOcm10UjA5dkN5SzNRTS8wSGc5bUo3Z1I3Wm5vejdYOC96RHlvTzlHSWV3OVBFZlh0aXVnR0xKdVI3RWRGMEdoSEQwclU3ZCtDNisrK01LakpzMkN1bHd6QmlyZGhSeDdVN0tjYkVPa1dzNytQL1VvNnoyNmlLOEFIWW1OM0RUZXd0Y28rYkdTU3F2cmdiOTQ3RmJURytQaWFmZktnTDhiY204S3JVMjJUUTlQWkh5bm9OeDllWGRrS0pCTDlBblRKSUZmT3U4OUNVU2JQdjFNVGp2SzgrQjczNmQwdnZlSFF0WFRyT1QwVUluTUo1bW1XemFXd3FMc2NFUzA3NWlkSHBuY1U3VXptZFMzRlo3OXZJRTg2MHBvR0QxM1BZSysyWG5HM3FwU2Qxc2hkUFFpQ0VKM0xsa0Q2ZE5pMGwvK0tUVFEvUDZoM01XMlE1akthZDNkWDRoYTVESENlblFRTk9ScE1BNmdlVWdsbDBhZS9xc0ZUc0M3US84aU81WHRNZHhDWEovOU5GNEhLYVlqdjFzb005RDduaTVUbW1WQ3IrdHJSa1RNM2ZhQ29xVW9ydExZaUJBZWlHRFpUTHFFa1FHbjN3MmZEeHpqWmhrcExOOCt5MnEvdnFNaFNKTnFOaEx3VmpiZlhSM08iLCJtYWMiOiI5Yjg2ZDA1N2FjZDg0YmEwOWMxMzczYWNlODUyYzk2ODgxZDU1NWE4MWY3MzcwNWQ3YzVkYzUzNGEyNDljNzE4IiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 18:05:41 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ink0TGtWMjhzMnZMNko2RWg4cUZYU0E9PSIsInZhbHVlIjoiNEZjMHgwMFRySzRrb2lPY1N6Z3c0a3hSTDJYUThqME5EbGJQVWNMd25NNFQ5WnNnc3Bna0lKTS81VGFUelFUdzRiNGxKSHV5RTFGYVJ0dDc4RDRQWHlGKzlWNmhPVVVScEV5Vm9UUzU2aGlERkRrdnhPMVY0dll3NDE3TzVtei84SjE5Z2QyZGEvSFBaS3lyNlZiKzl5ZzQva0RXemY1OEkvMzJUaXFJV0hNYzQ0aVJEUm5pNUZFeCtzRklkamNyS0ZkU2wvZytwVUJHNitPaHpvMUFXOTg5bW1xcFlMVDZFb3RuQnVhWjNENm5HN2E0NVVWTE1QQ1FST3ptYm1HOGVwMXUyV3JMVFJRUVJEYStMOXQwR0Z3WGYyemI2T1ZObmdsZVB3YjVxcWQ1dDNBTUtkZnZtenFSWmtMc3VtMlhGbC9ncDgyb0pJYjdBQ0tkZnZGblJLem95TnE2cWRienN4N0REd1g3SjJFd1ZiVUNBYllrRGNyTXNlZ0haMU1xS3lSbXNnR1pod0IwczFUS0Jad3RadndtbjVnbUMxMHhrcUhtamZPTWJmdFViSHlLbHNkRUFoQnNrVzFkNzNuMHU1L2JHVkFGN0pNL0NtMEVOYTVlK2padWJWdld3dkk3c3Rmb0N3anBJb1orVE5qVnFXTURheVplOHBVcHhJTGYiLCJtYWMiOiJhOWIxYjRjMTUwNmRiZGNhOTQyNDA1ZWFmNDI4NmRkNzA2ZGZkMjMzNmE4MTRkMjc1MzA2MTE1MDkwYThhN2VkIiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 18:05:41 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkgvcXBZNkdYd1FjUjNQNjZWSHgvVkE9PSIsInZhbHVlIjoiZVhtamczRkZsS3BqSDRFYjZVVmdOcm10UjA5dkN5SzNRTS8wSGc5bUo3Z1I3Wm5vejdYOC96RHlvTzlHSWV3OVBFZlh0aXVnR0xKdVI3RWRGMEdoSEQwclU3ZCtDNisrK01LakpzMkN1bHd6QmlyZGhSeDdVN0tjYkVPa1dzNytQL1VvNnoyNmlLOEFIWW1OM0RUZXd0Y28rYkdTU3F2cmdiOTQ3RmJURytQaWFmZktnTDhiY204S3JVMjJUUTlQWkh5bm9OeDllWGRrS0pCTDlBblRKSUZmT3U4OUNVU2JQdjFNVGp2SzgrQjczNmQwdnZlSFF0WFRyT1QwVUluTUo1bW1XemFXd3FMc2NFUzA3NWlkSHBuY1U3VXptZFMzRlo3OXZJRTg2MHBvR0QxM1BZSysyWG5HM3FwU2Qxc2hkUFFpQ0VKM0xsa0Q2ZE5pMGwvK0tUVFEvUDZoM01XMlE1akthZDNkWDRoYTVESENlblFRTk9ScE1BNmdlVWdsbDBhZS9xc0ZUc0M3US84aU81WHRNZHhDWEovOU5GNEhLYVlqdjFzb005RDduaTVUbW1WQ3IrdHJSa1RNM2ZhQ29xVW9ydExZaUJBZWlHRFpUTHFFa1FHbjN3MmZEeHpqWmhrcExOOCt5MnEvdnFNaFNKTnFOaEx3VmpiZlhSM08iLCJtYWMiOiI5Yjg2ZDA1N2FjZDg0YmEwOWMxMzczYWNlODUyYzk2ODgxZDU1NWE4MWY3MzcwNWQ3YzVkYzUzNGEyNDljNzE4IiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 18:05:41 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ink0TGtWMjhzMnZMNko2RWg4cUZYU0E9PSIsInZhbHVlIjoiNEZjMHgwMFRySzRrb2lPY1N6Z3c0a3hSTDJYUThqME5EbGJQVWNMd25NNFQ5WnNnc3Bna0lKTS81VGFUelFUdzRiNGxKSHV5RTFGYVJ0dDc4RDRQWHlGKzlWNmhPVVVScEV5Vm9UUzU2aGlERkRrdnhPMVY0dll3NDE3TzVtei84SjE5Z2QyZGEvSFBaS3lyNlZiKzl5ZzQva0RXemY1OEkvMzJUaXFJV0hNYzQ0aVJEUm5pNUZFeCtzRklkamNyS0ZkU2wvZytwVUJHNitPaHpvMUFXOTg5bW1xcFlMVDZFb3RuQnVhWjNENm5HN2E0NVVWTE1QQ1FST3ptYm1HOGVwMXUyV3JMVFJRUVJEYStMOXQwR0Z3WGYyemI2T1ZObmdsZVB3YjVxcWQ1dDNBTUtkZnZtenFSWmtMc3VtMlhGbC9ncDgyb0pJYjdBQ0tkZnZGblJLem95TnE2cWRienN4N0REd1g3SjJFd1ZiVUNBYllrRGNyTXNlZ0haMU1xS3lSbXNnR1pod0IwczFUS0Jad3RadndtbjVnbUMxMHhrcUhtamZPTWJmdFViSHlLbHNkRUFoQnNrVzFkNzNuMHU1L2JHVkFGN0pNL0NtMEVOYTVlK2padWJWdld3dkk3c3Rmb0N3anBJb1orVE5qVnFXTURheVplOHBVcHhJTGYiLCJtYWMiOiJhOWIxYjRjMTUwNmRiZGNhOTQyNDA1ZWFmNDI4NmRkNzA2ZGZkMjMzNmE4MTRkMjc1MzA2MTE1MDkwYThhN2VkIiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 18:05:41 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-602404303\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1996783729 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PKqZteC3IabV2rdCxsXBMLnTb72fzH4qdR2KdQSn</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>17</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2295</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"26 characters\">&#1605;&#1606;&#1578;&#1608;&#1587; &#1593;&#1604;&#1603;&#1577; &#1575;&#1576;&#1610;&#1590; &#1606;&#1593;&#1606;&#1575;&#1593; 54&#1580;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">15.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2295</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>30.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>5</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>2296</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"19 characters\">&#1576;&#1591;&#1610;&#1582; &#1573;&#1590;&#1575;&#1601;&#1610; 60 &#1579;&#1575;&#1606;&#1610;&#1577;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">18.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>36.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2296</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n    <span class=sf-dump-key>2297</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1587;&#1575;&#1583;&#1610;&#1575; - &#1583;&#1580;&#1575;&#1580; &#1605;&#1580;&#1605;&#1617;&#1583; 800&#1580;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">16.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>32.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2297</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>6</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n    <span class=sf-dump-key>2301</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"52 characters\">&#1576;&#1585;&#1610;&#1603;&#1587;&#1578;&#1575; &#1608;&#1610;&#1601;&#1585; &#1575;&#1604;&#1605;&#1602;&#1585;&#1605;&#1588; &#1608;&#1575;&#1604;&#1605;&#1594;&#1591;&#1609; &#1576;&#1588;&#1608;&#1603;&#1608;&#1604;&#1575;&#1578;&#1577; &#1575;&#1604;&#1581;&#1604;&#1610;&#1576; 24 &#1581;&#1576;&#1577;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">13.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>13.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2301</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1996783729\", {\"maxDepth\":0})</script>\n"}}