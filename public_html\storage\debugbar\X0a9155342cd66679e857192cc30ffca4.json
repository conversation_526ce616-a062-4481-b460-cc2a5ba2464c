{"__meta": {"id": "X0a9155342cd66679e857192cc30ffca4", "datetime": "2025-06-26 16:05:28", "utime": **********.687679, "method": "GET", "uri": "/add-to-cart/2296/pos", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.237762, "end": **********.687694, "duration": 0.4499320983886719, "duration_str": "450ms", "measures": [{"label": "Booting", "start": **********.237762, "relative_start": 0, "end": **********.605801, "relative_end": **********.605801, "duration": 0.3680391311645508, "duration_str": "368ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.605814, "relative_start": 0.36805200576782227, "end": **********.687696, "relative_end": 1.9073486328125e-06, "duration": 0.08188199996948242, "duration_str": "81.88ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 49206384, "peak_usage_str": "47MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET add-to-cart/{id}/{session}", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@addToCart", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1320\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1320-1544</a>"}, "queries": {"nb_statements": 7, "nb_failed_statements": 0, "accumulated_duration": 0.006889999999999999, "accumulated_duration_str": "6.89ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 17 limit 1", "type": "query", "params": [], "bindings": ["17"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.642136, "duration": 0.00176, "duration_str": "1.76ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 25.544}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.6524022, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 25.544, "width_percent": 6.386}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 17 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["17", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.665225, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 31.93, "width_percent": 11.611}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (17) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.6673772, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 43.541, "width_percent": 6.386}, {"sql": "select * from `product_services` where `product_services`.`id` = '2296' limit 1", "type": "query", "params": [], "bindings": ["2296"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1324}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.671836, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1324", "source": "app/Http/Controllers/ProductServiceController.php:1324", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1324", "ajax": false, "filename": "ProductServiceController.php", "line": "1324"}, "connection": "kdmkjkqknb", "start_percent": 49.927, "width_percent": 7.983}, {"sql": "select sum(`quantity`) as aggregate from `warehouse_products` where `product_id` = 2296 and exists (select * from `warehouses` where `warehouse_products`.`warehouse_id` = `warehouses`.`id` and `created_by` = 15)", "type": "query", "params": [], "bindings": ["2296", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/ProductService.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\ProductService.php", "line": 155}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1328}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.675514, "duration": 0.0024500000000000004, "duration_str": "2.45ms", "memory": 0, "memory_str": null, "filename": "ProductService.php:155", "source": "app/Models/ProductService.php:155", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FProductService.php&line=155", "ajax": false, "filename": "ProductService.php", "line": "155"}, "connection": "kdmkjkqknb", "start_percent": 57.91, "width_percent": 35.559}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 4748}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 4682}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1397}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.679198, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4748", "source": "app/Models/Utility.php:4748", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUtility.php&line=4748", "ajax": false, "filename": "Utility.php", "line": "4748"}, "connection": "kdmkjkqknb", "start_percent": 93.469, "width_percent": 6.531}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 17,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1741713413 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>17</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1741713413\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.670976, "xdebug_link": null}]}, "session": {"_token": "PKqZteC3IabV2rdCxsXBMLnTb72fzH4qdR2KdQSn", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "17", "pos": "array:2 [\n  2295 => array:9 [\n    \"name\" => \"منتوس علكة ابيض نعناع 54جم\"\n    \"quantity\" => 1\n    \"price\" => \"15.00\"\n    \"id\" => \"2295\"\n    \"tax\" => 0\n    \"subtotal\" => 15.0\n    \"originalquantity\" => 5\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  2296 => array:8 [\n    \"name\" => \"بطيخ إضافي 60 ثانية\"\n    \"quantity\" => 1\n    \"price\" => \"18.00\"\n    \"tax\" => 0\n    \"subtotal\" => 18.0\n    \"id\" => \"2296\"\n    \"originalquantity\" => 2\n    \"product_tax\" => \"-\"\n  ]\n]"}, "request": {"path_info": "/add-to-cart/2296/pos", "status_code": "<pre class=sf-dump id=sf-dump-1722436546 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1722436546\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1806253944 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1806253944\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PKqZteC3IabV2rdCxsXBMLnTb72fzH4qdR2KdQSn</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1bowxa%7C2%7Cfx3%7C0%7C1999; _clsk=1osc4j2%7C1750953875247%7C2%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkJ4T2lNWEwxVDdIMWlVcStraHd6UEE9PSIsInZhbHVlIjoiMjM0UXk5Tm1MOGNSczdjeVMxeGJibzdSb0NBeUtTS1BHUGRMSHhJQlA5c1IzV3JXTWpVcG1vVWZZN0REd3ovL1F4YVZ4OERxQ1creG8wdEVmOEUvQVptMjRNRmxNUXY0YndicmJVZ0dvZTJFMExwaVlOdDM3cm5iTitTS3NUTWdNQmxvZ085M2pNZHVYUGhMamNNVU9lejQ2Zzl4TitmQWhQazdNY2VBTHA3WG0wYWU4ZHU4UnY4RkgzLzMySndFNHJmWUxKZlhDRVEybER3dnlZUzU1VUtQcGQrdTJUZ2tXRk9IRHE3YVB3b20vcHNHS1RxMUtKYlk5NVFHdElXaytvMTlxK084d0JuNmprbWVPRURwVldxcjllQ3ZGdWl6ZmRBNUJTbFdNRFZ5djU0VWs0ejhOR3g3QUFwdndxYkZERThjR1hHSkVOQnlmUHZvRzVuc1lZNzNHVVRqSjl1S2xzckFiOStyVW9GeitYcWVZL3VQWHh3bEVTald5N09XeTRzUmVnb3JrWFA5VEFRUlJrcnNQUmoxdXR2aDJXa1hick14cnR4ay9ORWxUR0RoSjJOcVlOdGxRdHZPNEl0bEpISUJuQWtSd2lTWU5wYUgrUW9wTmQwNVUyWjhnM1h6WUNycXdlakJILzJ5KzJON1o4MlYxcjNNeUZJeU90cVMiLCJtYWMiOiIzYjZkNGI2ZDdiZjg2ODRlMjIwYmRhZGJkZmYzZGMyZDMxNzdjMjc5MzVlNjM2NzQzZDE1OTUxZWZiYzcwNGZjIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlpSa2hTMUZSalIyWkpGd0owZkJNVkE9PSIsInZhbHVlIjoidk1kRUxBRjBCZmNBMmU0SzFvZVFjK0lpbjlYM0UvRzNLT3BzeHkrTURFd1VVNlJzUFhEWTc4Slc3bU5UaVdkeVhNaE12enNtdUxlZEhWQWJ5UUtlMHRHL1p3NDMwMTlnZXFtSFFtQWllSHdjK3J6WTU2YVZFQjZwNThjT1B2ckNSbDJiNmZoZm5ZTThrN0RPYzEvUi81Q0tlc0Q3ZjFoRks3MXE0ZFlQNnJoNWU5M244d3lLZUlYRldRc25nVzNKcUtkYkpsbzlaUDZSV1hoVjUvNWNvMmprWnlaUFV0ZWRnRnptb0lyZ1I3cTBNZkdoeU4yNG5TYUNlcDRTdHlUbE9wWHU1dlFlUzc3MkVpeWd2NkpqL0VVY3F3TmtGUURCMVlPWSt3VHNoV3JHNGsxSEhVTlNoYzlqYXhCbE1JcjVlV0l4a2NldkdsQTlCS0JUb3ZraW9kWEhZMVcyRy9oWlU0UGtZVE5YYXdwSHJzRU1iUE54WC9ER1BCQ2dPZDZlNThudG1GcTkydkJtSTIrRldxMkF1TVNURjlrNUZUZUI2NDJ4a0l6eWNzKzBTVjYva2ppSnNhZHBMbkJVeDNta1NvNy9pRnFpUXJ6ZDZ4RXZiU2NtVWsrM25halp1UEtBRXpXTGRzeldqcTNINklZRlA0ZTdSS2NmT0pGVTRmL28iLCJtYWMiOiI3NDFiY2NhNmI1Y2JlN2EzZmMyNjlmZDhkNzljZGQ0MWI4MDNhZTRjMTJjZTgxYjEwMGQxMzk0ZDQ4ZjI0YzgxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-467840481 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PKqZteC3IabV2rdCxsXBMLnTb72fzH4qdR2KdQSn</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GUUMfjgJKgBqP5oaDcRP4Rfu0FM4k6V9zWwhkyoG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-467840481\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-142515241 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 16:05:28 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkNubkJBYU5TVmRQWFY3RVFvSDlocWc9PSIsInZhbHVlIjoieHRyKy8zeFhpSit6MHdTSlFNQXhISkRnYVF2dDRMY2ZUOCttSUMvK2x5cG9rU2tUTXdUckpVS0EzOEU3dFlFY0Q2SENzZDhPVVNhVThhUW8xVVcwWTNPbjNtMXE4a2JCSThRaUt6VkNkWTBTOCtDcld1WGQwUHdoK3Foa1pKcm1XSnZGU0pyOFdzUk1oS0h3RFExRzZrNDlJMkpwV2Z0MHBQOVRPUzFLV1JaYzZoZ2NTc1JPaHRaVnc4K1lHRVBMc0NDazJsQ3cyTkNIc0JTQitFVGtoVDVKYlhRdEdTMktLQlJqbUJKa0t2SDJOWFBqUisyQTVmRHRGU3hHVlZ0VkVWczd0N3VRK0krS3Q5cmM3R0ZzWnlCaUs2NjBNanIwL2NWZjJKV003dlJGUUlzV0c4NytiS2JCQnFYVFhlSkFOelBCUkVHS2hWM2tmRW9yVnRITUYyU1hVYmgwZXZrN2lPaXdCODBnR1BSakdlaSttUXRPbWgwVVpCYS84UlV1c3VDMG40MlN3VmJJQTF3b095K2VEcEI4SGlFdXE2V3AvSTFhdkc1L2Rsb0dzV04ramkrMnNsb1UyZEVVK0tHUFlqTGRad2ZqUW1PUEM5Nks3b1N6bG42QTNaUzZhc3VnZ1RaaFFKTlMzRDM2T2pTQ3dJWVRmRU8zNVh4Z1ZRTjMiLCJtYWMiOiJmNGIwYmM5NWFkZjY0NTkyY2VjOGIyMzAzMDE2NWM3MWFhNjQ5NjQxMjEzYjQzNWFjZjIyMDBkZTIxNTAxNzRjIiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 18:05:28 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IktiNHNFTDJKWFlQdUxZVHBCejIzYVE9PSIsInZhbHVlIjoiZk4rRzRzNUdxVk84UkhMOHFVS0J2L2JJWW1GbVF6Z1Z0ZG5JdVI1SmE4c00vQnd1SE5XYVV5cXFMTEZRbUN4dXg0cHNqaFliQjA3SlhaUFV5MGw3a1lMSHVrZnRMUkM2Wis5dVFhczVvd29QZzBCZEQwRzhUOHZiMGhEVlU3UjFBS3RBZHNLS0FRY2pUWXNPT0RoL1dsT2Evb1V0T3ErZHNBTldESjQ3L3YrY3BNbFErWDdMZVMzQWJhZEp1aVpUR3p5T3BUWXF1b1U0VXBKaHBTOGQ4UkIxaEpSZFFLKzlYRDA0WG5VM3kzc1lZS2U4dU9VSGo2YTBRekpOZ2xLYml0NzVYM25HOUpMQU5kT2lKUEpTMFdWSjYrS3JybENxZmZ5dzBKV0o4RTRYbmppdkZUNDR2ejBpbGF5ejZlSFJSRGh3M0tOY0cxbWxrQlduWHVsQVBkczdja1BVZVFtVXcyWGNUVWFtaloyMUIwWUVTcU1jS1AvbmYyTERObkZkZHZKVmdjTHJnUDZISjZVUTIreCsycEY2YnhNa1hxeFZjYUxpTkRkd0dBR1BCdk04UTMyZktHanpZREhXRmh6THYrRjl6eThRd1VmZWpZbUJ6anNjVFRxMHhJc2cwdzIxZWJCMWszL2xCY2hqZ2FoTFBlMnZJa0srUFFUdTRXYVAiLCJtYWMiOiIxYmJjY2ExODAwZDU3NWM0ZTVhMTQyNzI1ZTU3YjgxYjMzNzc3MDQ1MWI2Y2ZmOTNlYWNlNjZhN2NhNjY4NGY5IiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 18:05:28 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkNubkJBYU5TVmRQWFY3RVFvSDlocWc9PSIsInZhbHVlIjoieHRyKy8zeFhpSit6MHdTSlFNQXhISkRnYVF2dDRMY2ZUOCttSUMvK2x5cG9rU2tUTXdUckpVS0EzOEU3dFlFY0Q2SENzZDhPVVNhVThhUW8xVVcwWTNPbjNtMXE4a2JCSThRaUt6VkNkWTBTOCtDcld1WGQwUHdoK3Foa1pKcm1XSnZGU0pyOFdzUk1oS0h3RFExRzZrNDlJMkpwV2Z0MHBQOVRPUzFLV1JaYzZoZ2NTc1JPaHRaVnc4K1lHRVBMc0NDazJsQ3cyTkNIc0JTQitFVGtoVDVKYlhRdEdTMktLQlJqbUJKa0t2SDJOWFBqUisyQTVmRHRGU3hHVlZ0VkVWczd0N3VRK0krS3Q5cmM3R0ZzWnlCaUs2NjBNanIwL2NWZjJKV003dlJGUUlzV0c4NytiS2JCQnFYVFhlSkFOelBCUkVHS2hWM2tmRW9yVnRITUYyU1hVYmgwZXZrN2lPaXdCODBnR1BSakdlaSttUXRPbWgwVVpCYS84UlV1c3VDMG40MlN3VmJJQTF3b095K2VEcEI4SGlFdXE2V3AvSTFhdkc1L2Rsb0dzV04ramkrMnNsb1UyZEVVK0tHUFlqTGRad2ZqUW1PUEM5Nks3b1N6bG42QTNaUzZhc3VnZ1RaaFFKTlMzRDM2T2pTQ3dJWVRmRU8zNVh4Z1ZRTjMiLCJtYWMiOiJmNGIwYmM5NWFkZjY0NTkyY2VjOGIyMzAzMDE2NWM3MWFhNjQ5NjQxMjEzYjQzNWFjZjIyMDBkZTIxNTAxNzRjIiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 18:05:28 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IktiNHNFTDJKWFlQdUxZVHBCejIzYVE9PSIsInZhbHVlIjoiZk4rRzRzNUdxVk84UkhMOHFVS0J2L2JJWW1GbVF6Z1Z0ZG5JdVI1SmE4c00vQnd1SE5XYVV5cXFMTEZRbUN4dXg0cHNqaFliQjA3SlhaUFV5MGw3a1lMSHVrZnRMUkM2Wis5dVFhczVvd29QZzBCZEQwRzhUOHZiMGhEVlU3UjFBS3RBZHNLS0FRY2pUWXNPT0RoL1dsT2Evb1V0T3ErZHNBTldESjQ3L3YrY3BNbFErWDdMZVMzQWJhZEp1aVpUR3p5T3BUWXF1b1U0VXBKaHBTOGQ4UkIxaEpSZFFLKzlYRDA0WG5VM3kzc1lZS2U4dU9VSGo2YTBRekpOZ2xLYml0NzVYM25HOUpMQU5kT2lKUEpTMFdWSjYrS3JybENxZmZ5dzBKV0o4RTRYbmppdkZUNDR2ejBpbGF5ejZlSFJSRGh3M0tOY0cxbWxrQlduWHVsQVBkczdja1BVZVFtVXcyWGNUVWFtaloyMUIwWUVTcU1jS1AvbmYyTERObkZkZHZKVmdjTHJnUDZISjZVUTIreCsycEY2YnhNa1hxeFZjYUxpTkRkd0dBR1BCdk04UTMyZktHanpZREhXRmh6THYrRjl6eThRd1VmZWpZbUJ6anNjVFRxMHhJc2cwdzIxZWJCMWszL2xCY2hqZ2FoTFBlMnZJa0srUFFUdTRXYVAiLCJtYWMiOiIxYmJjY2ExODAwZDU3NWM0ZTVhMTQyNzI1ZTU3YjgxYjMzNzc3MDQ1MWI2Y2ZmOTNlYWNlNjZhN2NhNjY4NGY5IiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 18:05:28 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-142515241\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PKqZteC3IabV2rdCxsXBMLnTb72fzH4qdR2KdQSn</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>17</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2295</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"26 characters\">&#1605;&#1606;&#1578;&#1608;&#1587; &#1593;&#1604;&#1603;&#1577; &#1575;&#1576;&#1610;&#1590; &#1606;&#1593;&#1606;&#1575;&#1593; 54&#1580;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">15.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2295</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>15.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>5</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>2296</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"19 characters\">&#1576;&#1591;&#1610;&#1582; &#1573;&#1590;&#1575;&#1601;&#1610; 60 &#1579;&#1575;&#1606;&#1610;&#1577;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">18.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>18.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2296</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}