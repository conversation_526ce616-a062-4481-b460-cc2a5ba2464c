{"__meta": {"id": "X518339c65231f032f38fa6f585a06f06", "datetime": "2025-06-26 15:37:06", "utime": **********.98098, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.495155, "end": **********.980995, "duration": 0.48583984375, "duration_str": "486ms", "measures": [{"label": "Booting", "start": **********.495155, "relative_start": 0, "end": **********.923228, "relative_end": **********.923228, "duration": 0.4280729293823242, "duration_str": "428ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.923238, "relative_start": 0.4280829429626465, "end": **********.980997, "relative_end": 2.1457672119140625e-06, "duration": 0.05775904655456543, "duration_str": "57.76ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45647864, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0029300000000000003, "accumulated_duration_str": "2.93ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.95403, "duration": 0.0019, "duration_str": "1.9ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 64.846}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.9652312, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 64.846, "width_percent": 16.041}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.973192, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 80.887, "width_percent": 19.113}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "G4oUC4dvL91DvSdXtTSUa87JqfM4sfNNzjOiK6ns", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/bill/eyJpdiI6InNzWlhKbTYwWndnMXhFSnlrbWhnK0E9PSIsInZhbHVlIjoiaWhTMHpYT3kzU0JqQTdHK01oV1JvUT09IiwibWFjIjoiODg3MTAzOGJiNDk1MzcyODU5MWZmZTNkMTdhYTAzMDJkNmI1NjlmODFkYzk1MzA3NTljODFhMzc1MTkyMzg5YiIsInRhZyI6IiJ9/edit\"\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-257192968 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-257192968\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-93993212 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-93993212\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-574960168 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G4oUC4dvL91DvSdXtTSUa87JqfM4sfNNzjOiK6ns</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-574960168\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1675981012 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"227 characters\">http://localhost/bill/eyJpdiI6InNzWlhKbTYwWndnMXhFSnlrbWhnK0E9PSIsInZhbHVlIjoiaWhTMHpYT3kzU0JqQTdHK01oV1JvUT09IiwibWFjIjoiODg3MTAzOGJiNDk1MzcyODU5MWZmZTNkMTdhYTAzMDJkNmI1NjlmODFkYzk1MzA3NTljODFhMzc1MTkyMzg5YiIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1ap6d1q%7C2%7Cfx3%7C0%7C1998; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clsk=p74v85%7C1750951931371%7C75%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImVNb25IdGwwbWMyVTI0Z0VlRzlxQlE9PSIsInZhbHVlIjoibDVocTBBU2pubDFvRjlpSlZQS2M0OEthY0VaWXc1Tk5aU0J3RWtQaFl5MTd5Ynd5SWo1UXdoRXpVZVZsTlZDWkp5elR5T2hJYWRXdzRoTTg4T0pMdWN4YjRVVjdDODZvYUUrWEZUcVBiUFhWdS90SnJnUVloLy9UK1dSbkdCVWx6ck1DQ3RBaHVCeStwOXpzZU15akR0YlFqVnlIWGhMS2pBRS9HTXFEN05vTzVzYU9PNFJzQ0pNU0p5bThMLzhXZktJQk4yZ3l6dmlYY0g4MkpQYzZrRzdOSnBMdjRkR1BsTmg4d0YyVjhwOGpLejY3RVUzRVJhWlErR1VTV2gxZHNveEtYc2hvUHZ0aEJEUGJycWM4KzJRUnQ1OGlldVg2bVlQSmdGa0NaTXd2cEx5S2psb2o0VURCYkc4TjRkRnVVZktkUldMU1dLY0ZQcjZDS2s1Um5UdlFPVnhid1J0cWVsK0xWSmxIWkpRWlNsT2cvWmRSS0pRa2pYcURHdlJLMEc1WDhpdHoybHMxaElyNnhkZjEyTHA4d01kTjNtMGRrcEZQME1aRXp2bzRGTzZLWnhNMUNjRVhkVGdYYnQ1VG9lcDFqSDRKK1F5b2czUkFqTW1LdWdYUEduZnBKK0RFN21jendGSHZSSzhlbWZBT2drQW0zcllQR1FpdnJmalUiLCJtYWMiOiI3NTFkOTliNTI5N2IwNzRlZDRmZWQxMzk3YTgxMTIyMjI5NjBiZmEzZTM5ODZkOWU5MjFlMmQwMmQxZTk3OTk3IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ik9hYVZQVm82WkV2cVVMdHdXQjEzMHc9PSIsInZhbHVlIjoiMmNxc3lEeHh6bnp6YzBXclJJV0RIdkVrZ1orM09jbnRKN0ZhSXhybmpheFFYZEdUNVZPbCtkS2t1NzlDUjFHSGMzWUw0MjVjUllObXdycDk1VE9PL25DUFVodWE5VXVBVTArdHdHRkhJMFZ1N0J5TGE1OU0rOXNrWkNzQnRCVVZpaFNoVk1tRnFpdFFXTVhnVjBtVS8wUFlDWFljSzNEQm9tb2xwRjRDd3JhTHhnbWdjWnUwNWRIN1o0a24wSGY4U2dncXJpSXJPZjNjdkJDTkVtaVhoVVN5am5zQ2JBK2lJd3F0NmFDY0VSQU1naGg2KzJUTlBoYStQaGtCbnBqNlQxZXh4eGJ2TWNxQVg1eklmSm9FZ0YxaDhFRFlWbnEwQzQ1S3JwTUp3SGdDWUNObFcvb2lVYXprTHJTRlp5Q0w2dll3TWJvcDl2b3RUdkxVcjJ1VUt1OWNFRkF6RUVzOWtlemFHeEcrQlNwVy9ReG96V0pndnUvNUhkMHdCd1M2L1l5cHhXOW12S1NDblJmME0wR3ZFUzRMbHpyUE5xdjRUdEU0MGJlTnVsVnhlT2c5R3lFQ2tJTFRFY1BJcm5DUzlOR2dEdUZEYm0xcjhGN00walVYbithWUtDTm9lVWwvdE84K2RKM2M1MG53ZXc2cEJidFQxcXcvRVUwNXJpeDAiLCJtYWMiOiJiYTI5ODVjM2QzYjNiOWEyYTdjODJiMDdlMDZjY2Y5NjZmYzMyMGMwMzgyZDJiOGE0YWExMzdmYmI1MTRiNzQ0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1675981012\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2146736536 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G4oUC4dvL91DvSdXtTSUa87JqfM4sfNNzjOiK6ns</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">16z2m35FIhU8zNuOqkDvqLO3UyIjTT7e4c1Y0Zqf</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2146736536\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-791686330 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 15:37:06 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InBxVk1sNHNGb0pKQnZhcXY5WTBSdHc9PSIsInZhbHVlIjoiV21VNEdzVGV3UWJGbjBmcG5vQ09DZ1BqY0x3QmRWYXl1R2RVdGlhT0J6T25QUUhOWjNJaEI0UU16VVBvdkxtWkpSOXdCWFhndVFHcFdkUi8zRnF5NkE4MU85eWpLVG9CN0tzUHlDZWxxYTBhNFpsZUE1MjlrbmZ6dVFEcURWTndqMTZFdGoydFBhQkc5L0dBTzNibjRBK052bzZ4MTk0U0VyWmlRY3lsT2lteTRncGZ2N1EvOUN2aTFJWTRQT05EbnZycFBESWFtb2UwOFJiVWcvaFIyYWJhdjBaZThEZ1pCb0RBa2xBcU4vN0xUMUkzZ1ViN1JvbmgrSXU5WGlyOUF3YVAxMER3TFZRY0VpQ0tZWEg3K2hFVW92d20yTUlNZHRJdDludVpmM25yUUIzMHFvVyttLzgycjZPbUNxeU9TcHhPSTVMeVdjZnhGUWhWdHpPQmxGMmowUnk4Q2RLWDdyTXNnNEZyMTJjUWZKZDNoTXpKRkx3bC9Obm5tRzAyc3dNdlNHMERUV2pKVDVvK3dFTHVmMjB2VDlLUldhWW5FTm8zdmlRbUtjaG5FOWc3anBjeFQzSTh5VnZkRzhWckNYRHRCQ3p5NUpLTEdBVU1MT1hyZ1FQOFE4KzZFZDNONHcrN25NNURXd3RzWmpjZHlZUkJtYndnazZRbzBPZmciLCJtYWMiOiI0YTQ2YmE0YWJjM2RjOTg2OGQ1MWY2MDA4NDc5NTQxNmRiNDdlYzJhZmM4MzE2ZTQzOGFhNGFkOTE4OWNkMDFmIiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 17:37:06 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlJiTEhzVk5wZVEyZ0k4SWcvcVZnTGc9PSIsInZhbHVlIjoiVk1GdlpDNzUzMlE5ZzdQa1RBVCtrYjFWeWN0QTJGVTd4Z3p2NWNoRWVFb1dnU3RvdVRQVWx3SHgxbXNIenFuRVpZbmtqcmZHZEtIeXRncmd0OStLSXFFN2craWUxS016WVNleUJ1OXJQZyticVNnK3YzUDA2dTRkV0taYUN2YmFBT0ltOWJIOU9tblNsU1lyU3REWG5kSkFIeTRGU3R2QWEwM3dENzRXb08yMzFjVFdhaHhmUStCZUwxWFdSQzIxWUd6WkhTdUx0SGwyYjNacUt0eTdtUS9SdEVWMVIxRGNWQlIvUlAwT0FvaCtqUVVvQklxTE5rNVBERmJGa0NNZmhiZDc1VHh3cUJabjA2eVJ3bEgxSHhnOVJDdmFzK3liMDN2ODVFNTRYVklFQ3B5TnA4NytxdURMQllibG9rZU9wRkF6QmRDZFVEWmF0akJ2TjJFcWRaZEp6UFNwQ3gzV3Rpdys2US8yN1hJYVdNcFpuQzhJZnViYllnc09ucytHQitQWlFMMFN5dVcrNlRBdGh1L0FQNElsRk44ZHZwTGZhVGVNOGtBL1ZlWmovakprS0gxWkxyZGZwZ3RhaGZ4ODZGRkhIa2ZMYkkxWVRpYmtPWGpjZElKWDhDKzVoZW1vdGZxMDRiODZsaXc0L0grMHJlUkNVUUtDQXNLMEdlaUkiLCJtYWMiOiIxOGY0MWMxY2QwZGRhMGE0NDA1Y2U3ZTkzODkwY2QxZGRkNmNmNDg4MWVkNmIzNGU1MDY3YWQzNWM3YzkwNDQ0IiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 17:37:06 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InBxVk1sNHNGb0pKQnZhcXY5WTBSdHc9PSIsInZhbHVlIjoiV21VNEdzVGV3UWJGbjBmcG5vQ09DZ1BqY0x3QmRWYXl1R2RVdGlhT0J6T25QUUhOWjNJaEI0UU16VVBvdkxtWkpSOXdCWFhndVFHcFdkUi8zRnF5NkE4MU85eWpLVG9CN0tzUHlDZWxxYTBhNFpsZUE1MjlrbmZ6dVFEcURWTndqMTZFdGoydFBhQkc5L0dBTzNibjRBK052bzZ4MTk0U0VyWmlRY3lsT2lteTRncGZ2N1EvOUN2aTFJWTRQT05EbnZycFBESWFtb2UwOFJiVWcvaFIyYWJhdjBaZThEZ1pCb0RBa2xBcU4vN0xUMUkzZ1ViN1JvbmgrSXU5WGlyOUF3YVAxMER3TFZRY0VpQ0tZWEg3K2hFVW92d20yTUlNZHRJdDludVpmM25yUUIzMHFvVyttLzgycjZPbUNxeU9TcHhPSTVMeVdjZnhGUWhWdHpPQmxGMmowUnk4Q2RLWDdyTXNnNEZyMTJjUWZKZDNoTXpKRkx3bC9Obm5tRzAyc3dNdlNHMERUV2pKVDVvK3dFTHVmMjB2VDlLUldhWW5FTm8zdmlRbUtjaG5FOWc3anBjeFQzSTh5VnZkRzhWckNYRHRCQ3p5NUpLTEdBVU1MT1hyZ1FQOFE4KzZFZDNONHcrN25NNURXd3RzWmpjZHlZUkJtYndnazZRbzBPZmciLCJtYWMiOiI0YTQ2YmE0YWJjM2RjOTg2OGQ1MWY2MDA4NDc5NTQxNmRiNDdlYzJhZmM4MzE2ZTQzOGFhNGFkOTE4OWNkMDFmIiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 17:37:06 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlJiTEhzVk5wZVEyZ0k4SWcvcVZnTGc9PSIsInZhbHVlIjoiVk1GdlpDNzUzMlE5ZzdQa1RBVCtrYjFWeWN0QTJGVTd4Z3p2NWNoRWVFb1dnU3RvdVRQVWx3SHgxbXNIenFuRVpZbmtqcmZHZEtIeXRncmd0OStLSXFFN2craWUxS016WVNleUJ1OXJQZyticVNnK3YzUDA2dTRkV0taYUN2YmFBT0ltOWJIOU9tblNsU1lyU3REWG5kSkFIeTRGU3R2QWEwM3dENzRXb08yMzFjVFdhaHhmUStCZUwxWFdSQzIxWUd6WkhTdUx0SGwyYjNacUt0eTdtUS9SdEVWMVIxRGNWQlIvUlAwT0FvaCtqUVVvQklxTE5rNVBERmJGa0NNZmhiZDc1VHh3cUJabjA2eVJ3bEgxSHhnOVJDdmFzK3liMDN2ODVFNTRYVklFQ3B5TnA4NytxdURMQllibG9rZU9wRkF6QmRDZFVEWmF0akJ2TjJFcWRaZEp6UFNwQ3gzV3Rpdys2US8yN1hJYVdNcFpuQzhJZnViYllnc09ucytHQitQWlFMMFN5dVcrNlRBdGh1L0FQNElsRk44ZHZwTGZhVGVNOGtBL1ZlWmovakprS0gxWkxyZGZwZ3RhaGZ4ODZGRkhIa2ZMYkkxWVRpYmtPWGpjZElKWDhDKzVoZW1vdGZxMDRiODZsaXc0L0grMHJlUkNVUUtDQXNLMEdlaUkiLCJtYWMiOiIxOGY0MWMxY2QwZGRhMGE0NDA1Y2U3ZTkzODkwY2QxZGRkNmNmNDg4MWVkNmIzNGU1MDY3YWQzNWM3YzkwNDQ0IiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 17:37:06 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-791686330\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-196352186 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G4oUC4dvL91DvSdXtTSUa87JqfM4sfNNzjOiK6ns</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"227 characters\">http://localhost/bill/eyJpdiI6InNzWlhKbTYwWndnMXhFSnlrbWhnK0E9PSIsInZhbHVlIjoiaWhTMHpYT3kzU0JqQTdHK01oV1JvUT09IiwibWFjIjoiODg3MTAzOGJiNDk1MzcyODU5MWZmZTNkMTdhYTAzMDJkNmI1NjlmODFkYzk1MzA3NTljODFhMzc1MTkyMzg5YiIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-196352186\", {\"maxDepth\":0})</script>\n"}}