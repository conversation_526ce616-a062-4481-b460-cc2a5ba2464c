{"__meta": {"id": "X5e88a1d73a1ebcb8a576dff9ae203a0b", "datetime": "2025-06-26 15:59:30", "utime": 1750953570.074307, "method": "GET", "uri": "/login", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.33062, "end": 1750953570.074321, "duration": 0.7437009811401367, "duration_str": "744ms", "measures": [{"label": "Booting", "start": **********.33062, "relative_start": 0, "end": **********.693353, "relative_end": **********.693353, "duration": 0.3627328872680664, "duration_str": "363ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.693362, "relative_start": 0.36274194717407227, "end": 1750953570.074323, "relative_end": 1.9073486328125e-06, "duration": 0.38096094131469727, "duration_str": "381ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46557856, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 4, "templates": [{"name": "1x auth.login", "param_count": null, "params": [], "start": **********.753446, "type": "blade", "hash": "bladeC:\\laragon\\www\\ERPcopy\\public_html\\resources\\views/auth/login.blade.phpauth.login", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fresources%2Fviews%2Fauth%2Flogin.blade.php&line=1", "ajax": false, "filename": "login.blade.php", "line": "?"}, "render_count": 1, "name_original": "auth.login"}, {"name": "1x layouts.auth", "param_count": null, "params": [], "start": **********.76438, "type": "blade", "hash": "bladeC:\\laragon\\www\\ERPcopy\\public_html\\resources\\views/layouts/auth.blade.phplayouts.auth", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fresources%2Fviews%2Flayouts%2Fauth.blade.php&line=1", "ajax": false, "filename": "auth.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.auth"}, {"name": "1x landingpage::layouts.buttons", "param_count": null, "params": [], "start": 1750953570.011578, "type": "blade", "hash": "bladeC:\\laragon\\www\\ERPcopy\\public_html\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.phplandingpage::layouts.buttons", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2FModules%2FLandingPage%2FResources%2Fviews%2Flayouts%2Fbuttons.blade.php&line=1", "ajax": false, "filename": "buttons.blade.php", "line": "?"}, "render_count": 1, "name_original": "landingpage::layouts.buttons"}, {"name": "1x layouts.cookie_consent", "param_count": null, "params": [], "start": 1750953570.069537, "type": "blade", "hash": "bladeC:\\laragon\\www\\ERPcopy\\public_html\\resources\\views/layouts/cookie_consent.blade.phplayouts.cookie_consent", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fresources%2Fviews%2Flayouts%2Fcookie_consent.blade.php&line=1", "ajax": false, "filename": "cookie_consent.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.cookie_consent"}]}, "route": {"uri": "GET login/{lang?}", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@showLoginForm", "namespace": null, "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=344\" onclick=\"\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:344-359</a>"}, "queries": {"nb_statements": 9, "nb_failed_statements": 0, "accumulated_duration": 0.00834, "accumulated_duration_str": "8.34ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 555}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 348}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.737672, "duration": 0.00221, "duration_str": "2.21ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 26.499}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'kdmkjkqknb' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 537}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.741266, "duration": 0.00268, "duration_str": "2.68ms", "memory": 0, "memory_str": null, "filename": "Utility.php:537", "source": "app/Models/Utility.php:537", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUtility.php&line=537", "ajax": false, "filename": "Utility.php", "line": "537"}, "connection": "kdmkjkqknb", "start_percent": 26.499, "width_percent": 32.134}, {"sql": "select `full_name`, `code` from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 543}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.74565, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "Utility.php:543", "source": "app/Models/Utility.php:543", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUtility.php&line=543", "ajax": false, "filename": "Utility.php", "line": "543"}, "connection": "kdmkjkqknb", "start_percent": 58.633, "width_percent": 1.799}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 4748}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 4682}, {"index": 15, "namespace": "view", "name": "auth.login", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\resources\\views/auth/login.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.7546842, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4748", "source": "app/Models/Utility.php:4748", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUtility.php&line=4748", "ajax": false, "filename": "Utility.php", "line": "4748"}, "connection": "kdmkjkqknb", "start_percent": 60.432, "width_percent": 5.276}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 4748}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 4682}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\resources\\views/layouts/auth.blade.php", "line": 10}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.9614751, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4748", "source": "app/Models/Utility.php:4748", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUtility.php&line=4748", "ajax": false, "filename": "Utility.php", "line": "4748"}, "connection": "kdmkjkqknb", "start_percent": 65.707, "width_percent": 7.674}, {"sql": "select * from `users` where `type` = 'super admin' limit 1", "type": "query", "params": [], "bindings": ["super admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 4113}, {"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 4155}, {"index": 18, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.9725938, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4113", "source": "app/Models/Utility.php:4113", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUtility.php&line=4113", "ajax": false, "filename": "Utility.php", "line": "4113"}, "connection": "kdmkjkqknb", "start_percent": 73.381, "width_percent": 9.472}, {"sql": "select `value`, `name` from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 4114}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 4155}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.975701, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4114", "source": "app/Models/Utility.php:4114", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUtility.php&line=4114", "ajax": false, "filename": "Utility.php", "line": "4114"}, "connection": "kdmkjkqknb", "start_percent": 82.854, "width_percent": 3.597}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 4748}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 4682}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\resources\\views/layouts/auth.blade.php", "line": 39}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.9772708, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4748", "source": "app/Models/Utility.php:4748", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUtility.php&line=4748", "ajax": false, "filename": "Utility.php", "line": "4748"}, "connection": "kdmkjkqknb", "start_percent": 86.451, "width_percent": 3.118}, {"sql": "select * from `landing_page_settings`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "Modules/LandingPage/Entities/LandingPageSetting.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\Modules\\LandingPage\\Entities\\LandingPageSetting.php", "line": 27}, {"index": 19, "namespace": "view", "name": "landingpage::layouts.buttons", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": 1750953570.066835, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "LandingPageSetting.php:27", "source": "Modules/LandingPage/Entities/LandingPageSetting.php:27", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2FModules%2FLandingPage%2FEntities%2FLandingPageSetting.php&line=27", "ajax": false, "filename": "LandingPageSetting.php", "line": "27"}, "connection": "kdmkjkqknb", "start_percent": 89.568, "width_percent": 10.432}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "0ps0kU08Y9VObrS8MGH1isQjRNeK7Gn2ObkN9gvL", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-247246156 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-247246156\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1949731900 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1949731900\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2009724736 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2009724736\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-177461792 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">prefetch;prerender</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">prefetch</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1742 characters\">XSRF-TOKEN=eyJpdiI6ImhBYjBvMVdTTlJVaE5rZGVUYmlrS1E9PSIsInZhbHVlIjoiTDlQdEorMnp0ZlVvK1RYLzhlZXB1VExoblR6U3I3QUhob3NOOElOOVduODIzMGpDRVJwK3NxbEx0VTVCUXh6dno5akQxTlBDYXFLU2JsREdqdVNNRlJKYWhoc1pqYmNFRDU4clRXZisrREFkZGRKRHlaRlN2SzIzRWhRdXlZTmlWK21sYjhJUzhDTTIwS1JxV2IrUjZ1Z1FEUTNjWXVFU21lbGQ3UDlNNmlQRkpIS0VTRlN3UTlRNCs5Wm04SjZNQ0VET2xjZEMwZ3JVNUlwZk5rNG42Q2hkT2NQL1BVaFVWV0kxUDByVHdlTDVwaWpudnF0OWZPY1ArTXAvVUgwOFlNeXRhK0dPdCtIeWNSZE5ObjRTbnlEMXNySHVSQmRMQ1lDQXVtdnhZbTUzNkRpd2RiOGZIcmk0U0F3MERxRGhoc21XeDlwZXNCWnhnRFpuOWpKazFUVDhGTXpQcitKb1JGOUsrN0JRRjl3Uk5yM3ZZMGdwQmhMZlduWXFubGVzNmcyYys1Q1NEZm1URjJ2TTFpa2M5WUQxcDVYMTdDOWFRRzA1UmJnME4zdExxSVNQY3hPV3Z5emJDVzZta2pKMnhoNFdtaE4wZ2xrN1dxVzRhY09tSStXTEdRUWpsaGdhZk1vTmRySTRJaWhodUtvYVZEblhRaVRianMwYkxEeTkiLCJtYWMiOiI5ODdmMTZkYjUzODk3NTkyNzFkNDRkMmVkNWU5YTQ5Zjk0ZWFlZTI4ODhjOWI4Y2M4ZDZmODQ5OGIzNjI5MzJjIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InZwN0l6dFlKbWhKRDlCUmlWaWdtTXc9PSIsInZhbHVlIjoiU3NtTDI4Y3E2YVJUZXQ2TXorKzRXeVZKaDR6NG4waUVGTlZvb21ZVFE2QzhXVlJwV3ZoS2VzdEkyTElFSlJZdHlrSUdKcTRoUlJ3UG1GeXJ2QndodGJzRVJLdmd4M3hLbm51U3pVbm5kc041dlZWRzhjVHBPdTVTWEJDVEFxdUpnRDFEaGE5WFBUSjcySXBaUXhrem4wYnhiODl3b0g3ZGhhNVlnWjRuM25NNWVTWk0yaWh1b1cyVFNjam82Ty8wQWtBTVRtTUZwM2ExUFdoNGdtMDNsMUQ0UXhNenZEUjNIUGMzSml4Wm45NFBEU1NNM0pqR0hsMlIrOHFhRFB6U2FKcVcrS1pxaGdwUFFFOFQ4Ukt4S1FPL25zbm9WMzNEOGJWK2txOG5sajVsdHF1WUJKbmVDcTNkbUk3MFZQSUlBNEhTb2s2UnErSnlIWDFvWXR4ZXFmVjY1RFZRbzVndytrTmRrbVR3aEduNUMrWmtjc0s3WGtSYmRLRGJzYlFRNldBUSszQU5MV2YyT2hQSENjSXBQWUg3SS9EM2pRTGcwaUFQclNUbklhZXVYcHdrTHAraDZNbzhNbSt5MkVvekVvMW5TRmtnSERGNnZJWXlZbFVCYUh1Y3NZVndwdWxhN1QvUU1UNElSeUx5YUhvWm9NdzRZWGM4cDdOTDlkcHYiLCJtYWMiOiI5Y2U5OTkxODZhZmEzY2Q0MzZlMzdjOWU5OGVmNWRkNTZhYTQ3OGZkYTU3ZDhmYTg4MTFjYWY1ZWRhMDIyM2ViIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-177461792\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1003714173 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0ps0kU08Y9VObrS8MGH1isQjRNeK7Gn2ObkN9gvL</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xUs158fce27RRZxBwxL6eongJCrFGAQz1wiETmFD</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1003714173\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1283934012 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 15:59:29 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImpjdTBXSHhXQVFiZDU4bzZSS01LNXc9PSIsInZhbHVlIjoiWERuQkZ0aWlZUnFHSThWSVVaSFpLZ21zbE94dU9hZ3NMUVJ0Z1JRL2FhbFI0YnVGRVY3c1laa3dHZUlEaFRVTVlscDJXUmwrVGZ4M01FM3QxSVBpYnhoRFRrbGdYeDc2Y1krQ3VHbHRpd3BuOFJmN0VnT1dkamhrSXJKUlpFeFRYUEtuYm1XV2xwVHEyVnZpU0p5VWZ4SGJYME9jVDYydjVjMW0vWVZxZEc1dllDUjBmM0FNejlrck1IcG5WNFMzSEZwS1J4dXJTL2Qra2w1c1hGazArUDZmN2pXQmVqeVM1b0ZjNUlhU3NjMjhRaHFLc3lsd3FWSnlVZHR5SmFWZkVwQ0hMeUJqUU1xYTJ0NEc3UE90bDdmZkp0NWIzZG1EYUxXRjFhUzVtTWZqREhZTUY3LzB0Mks3VHoxcWJlZTJjdDk2ZUdOWTdZYnluYlpMTE1zT1l1VmhFdGE5OElldmFRSmN2aWRBZnZsQ2E2cDBqWFRZa1Q5bUErTUlkNHY2T2ppR3dPL1haSFYrbW1ZcjJ2bk5JVjZPR0k4YWdpdkM0R1JEQ245YWNsa3BqWUxVNEs3UTdSa3pwSEpOc0wwZm5XRWZydEhnVDlFR3JBUU5kelNuZUN5OEhWZUhnd3hrWnJHcUx6NXBhckZFNm9SQ2NpZEtkM0h3dVBVQnA0dnEiLCJtYWMiOiJiMjM2MTdiZDcxYmJkYjJhOThjNGE5NTljYWU2MTFiMDNkZGVlMzlkMmFkZmEwYWJjYjg1ZmRkYTFiZTE2NzI3IiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 17:59:30 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InVmWFZoeHg3Mld2bzR1Z0JPWDdKSWc9PSIsInZhbHVlIjoiOEhzOXdhdytWcUJva0VjSmdTRXNMakI2b283YStoaWtyUDJ3clgwcFhLNlBFRkdXcUhyL1dRdUZ4VFhIN0N6WXVTWWttckxVK3hsa1lLZjVMT21lU1pYQlBkUnYvdEFQWFZzY1M4K1BXUFVXQjVDamlzQWh3MXcrdXZNNUhLUTdSeGQxekNFY1JWa3JnZnZOT0w0K3YyNGxjc1Q4aEFCTnl0cW95cWNDcUVWbERkcSt2d2pHNHVlNkJLRjJZUzdKK1JHSmlMMW9qRk9EZ20xZzhNSDJlbzJldE1FWjAwQlFBOXVYNWNxRU5FQ25iZ1k5VkNJSXhRVEtKczZWU0Z5UlJ4U0lwUVlXRmM0UVVOc1ppMGlCMGoxWEdDeUp4Z3YrYVl1TmN2T0k3YU1ZdHN2NWpsc0k0a3JXMDBqRWIzODlMQk1jczlMbU93MWd0ejRKZDdMUmJjNTViTjQzdWdtdzJWODNDVXlkd0NPVmxaaXBTSW9qOVJPbjR3SjY0TEFwTUhuZnNHa0d6VzVSSFdkcjhnbXFmcHYyNkdnZ3dJTmkwT0YvWnZwUmtyUWFiY2RpeGk3bUV3MlcyTVE0OWM0L1JsMUVndEtGWk1IQzBuNGxISFZoelZwZmRtaVBpUk9CdnNxaUpNZCtab1lYT1JFeVRGREk2ZXZWcDEyenl4OFUiLCJtYWMiOiJiNzVjODdlNzNiNzZkOTkzYzI4OGQ5N2E2ZDI4ZjkwMzRlNjlhYmM5ZTU3OTdlY2IyMmQ0MDU2YTM2MzZmNjU1IiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 17:59:30 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImpjdTBXSHhXQVFiZDU4bzZSS01LNXc9PSIsInZhbHVlIjoiWERuQkZ0aWlZUnFHSThWSVVaSFpLZ21zbE94dU9hZ3NMUVJ0Z1JRL2FhbFI0YnVGRVY3c1laa3dHZUlEaFRVTVlscDJXUmwrVGZ4M01FM3QxSVBpYnhoRFRrbGdYeDc2Y1krQ3VHbHRpd3BuOFJmN0VnT1dkamhrSXJKUlpFeFRYUEtuYm1XV2xwVHEyVnZpU0p5VWZ4SGJYME9jVDYydjVjMW0vWVZxZEc1dllDUjBmM0FNejlrck1IcG5WNFMzSEZwS1J4dXJTL2Qra2w1c1hGazArUDZmN2pXQmVqeVM1b0ZjNUlhU3NjMjhRaHFLc3lsd3FWSnlVZHR5SmFWZkVwQ0hMeUJqUU1xYTJ0NEc3UE90bDdmZkp0NWIzZG1EYUxXRjFhUzVtTWZqREhZTUY3LzB0Mks3VHoxcWJlZTJjdDk2ZUdOWTdZYnluYlpMTE1zT1l1VmhFdGE5OElldmFRSmN2aWRBZnZsQ2E2cDBqWFRZa1Q5bUErTUlkNHY2T2ppR3dPL1haSFYrbW1ZcjJ2bk5JVjZPR0k4YWdpdkM0R1JEQ245YWNsa3BqWUxVNEs3UTdSa3pwSEpOc0wwZm5XRWZydEhnVDlFR3JBUU5kelNuZUN5OEhWZUhnd3hrWnJHcUx6NXBhckZFNm9SQ2NpZEtkM0h3dVBVQnA0dnEiLCJtYWMiOiJiMjM2MTdiZDcxYmJkYjJhOThjNGE5NTljYWU2MTFiMDNkZGVlMzlkMmFkZmEwYWJjYjg1ZmRkYTFiZTE2NzI3IiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 17:59:30 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InVmWFZoeHg3Mld2bzR1Z0JPWDdKSWc9PSIsInZhbHVlIjoiOEhzOXdhdytWcUJva0VjSmdTRXNMakI2b283YStoaWtyUDJ3clgwcFhLNlBFRkdXcUhyL1dRdUZ4VFhIN0N6WXVTWWttckxVK3hsa1lLZjVMT21lU1pYQlBkUnYvdEFQWFZzY1M4K1BXUFVXQjVDamlzQWh3MXcrdXZNNUhLUTdSeGQxekNFY1JWa3JnZnZOT0w0K3YyNGxjc1Q4aEFCTnl0cW95cWNDcUVWbERkcSt2d2pHNHVlNkJLRjJZUzdKK1JHSmlMMW9qRk9EZ20xZzhNSDJlbzJldE1FWjAwQlFBOXVYNWNxRU5FQ25iZ1k5VkNJSXhRVEtKczZWU0Z5UlJ4U0lwUVlXRmM0UVVOc1ppMGlCMGoxWEdDeUp4Z3YrYVl1TmN2T0k3YU1ZdHN2NWpsc0k0a3JXMDBqRWIzODlMQk1jczlMbU93MWd0ejRKZDdMUmJjNTViTjQzdWdtdzJWODNDVXlkd0NPVmxaaXBTSW9qOVJPbjR3SjY0TEFwTUhuZnNHa0d6VzVSSFdkcjhnbXFmcHYyNkdnZ3dJTmkwT0YvWnZwUmtyUWFiY2RpeGk3bUV3MlcyTVE0OWM0L1JsMUVndEtGWk1IQzBuNGxISFZoelZwZmRtaVBpUk9CdnNxaUpNZCtab1lYT1JFeVRGREk2ZXZWcDEyenl4OFUiLCJtYWMiOiJiNzVjODdlNzNiNzZkOTkzYzI4OGQ5N2E2ZDI4ZjkwMzRlNjlhYmM5ZTU3OTdlY2IyMmQ0MDU2YTM2MzZmNjU1IiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 17:59:30 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1283934012\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-776050726 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0ps0kU08Y9VObrS8MGH1isQjRNeK7Gn2ObkN9gvL</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-776050726\", {\"maxDepth\":0})</script>\n"}}