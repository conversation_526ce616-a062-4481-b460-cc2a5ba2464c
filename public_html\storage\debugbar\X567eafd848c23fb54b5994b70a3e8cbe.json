{"__meta": {"id": "X567eafd848c23fb54b5994b70a3e8cbe", "datetime": "2025-06-26 15:58:25", "utime": **********.790083, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.376528, "end": **********.790098, "duration": 0.41356992721557617, "duration_str": "414ms", "measures": [{"label": "Booting", "start": **********.376528, "relative_start": 0, "end": **********.740443, "relative_end": **********.740443, "duration": 0.36391496658325195, "duration_str": "364ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.740453, "relative_start": 0.3639249801635742, "end": **********.790099, "relative_end": 9.5367431640625e-07, "duration": 0.04964590072631836, "duration_str": "49.65ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45646608, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00318, "accumulated_duration_str": "3.18ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.766165, "duration": 0.00183, "duration_str": "1.83ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 57.547}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.776176, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 57.547, "width_percent": 17.296}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.782871, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 74.843, "width_percent": 25.157}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "G4oUC4dvL91DvSdXtTSUa87JqfM4sfNNzjOiK6ns", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/bill\"\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-645799426 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-645799426\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1714464609 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1714464609\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2014991956 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G4oUC4dvL91DvSdXtTSUa87JqfM4sfNNzjOiK6ns</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2014991956\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost/bill</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1ap6d1q%7C2%7Cfx3%7C0%7C1998; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clsk=p74v85%7C1750953495204%7C79%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ims4cXg3eVVKUElsWXZkclhhZFZ6aEE9PSIsInZhbHVlIjoiQVQ5NHVFV2wwR29QUEpFUkM5VTRuRE1nYTRyWDFrRFJOMHhmdk1WMVNncWtnSVptRlE3NWNFb1k5bUh1dTkxQWhPZGIzNjE3Z21qWk1TRjF6bGVLeCttN054SWFLaXh3bWJzM21SUUl0Wmw1SWRKSHF3aHNET25MYzlQd1plbk45UllhWm1iU2hSWWhxOHozcXpZYjgvaVpOWVlZc2Q0N2grMFB0OUUzV3dFR09lVStaS1NOOFRJTm9jamFtY09NVHhZTHhySUdCaGs3dHBOV0NWNlZ5T1dFM1RCWk53VzJBcUhRVmNNOEE4UWU4bjF3NG5qV3gxL2o4SlBsZWNvWk45UWdDTU9RS3hpVGYxdjNyMVlJemZDL2Jqc1NOT1djVHJMc0hSMWpzR3N1YU9LaDlHN1g3a056d1o0SnQ0OXZkeW1RMk5YZjdua3hCc2piZ1o2aHNHY3NwSjgxWnoyV1d4R3RxWDNqK1FWTzdQQmtLbVdEdko1eUhXL2VMaW9naWg4TXgrZEtOSDVNU0ppdkJXZG53Z2UrNXFCbGZkMkw3dWd1TEtRbmVxeCtUS1JIYTR3QXAxYm5Yam14dXgzM0tEdmMrWDBLOEF2VXBNaSsvd3RrTll4Tkx2TG5tbzJhcDdYaEFTR2picXNZZlNyc1NxNkdxdnVtUERxOTlnVjMiLCJtYWMiOiIwZTI5ZWIyNDQ2ZDVlNTEwODkyMGYxNDEzM2M5MDRlMDc0ZjA1ZjU4YjUyYWQyMDYwMTIwODYxY2YxODdlOTQzIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InlqUFFMU1c4RVpqaldENCtqaHplMXc9PSIsInZhbHVlIjoicGV5dzZ0bk5obWRhQ3ZrZWU5Z0laYndyQ2JpOGNRekJQdW8zbG42akQyZWV4bnhWM0x3VGJLNU9EbzFzZ0FmdkJnT1k2TytTK1pJRmE4TUFBWWE4cVVpaWVjNmZFMndtSTliTks0b21JNk05cDY1SnpIeCs1a2lsYTlxdVRJQmU1ZnZWYUI0N0lvRzBPdy9UYm9mb2R5bjh4TDZKNHFFTHptWGNXTDV0RlFSclJITGFmN1B6cWJlS3B5ZzkyWXhHVjBta1RnYmp2dTAxTktNZGEyUE5wR1RINSsxTXV1UGtPZ0s3a2FHNlF4VDV6NjF5SUcrNGdJZ2VTdi9xejh6MlZZQVI2WmI0SUpwZDRGN05vOW4zMzArcGR4SzNmY3VoSFRHSG9VS1ZzYjdlMVFocm1qb3IzTkV1TUR6WDFpTVFVRHFITkRxTUZnVncwcTRKUit0RXRDbzY2NmhuVVVMem00YVNzOXB1ZEhBRDZxQVdlMHV4b2w1VlVENjJEaU5kMHlCbDJBRUsxZW1RYnhlYnlwaGVhQWU0cXhSZ1QxclJpMk1wc3JibjQyMlhPVHFwM2Z5Wmd2a2crU3JxVXllVWtLcjFJT09vNFowb1A5clZnKzArRjM5ZVUybVVXa0pXOGZ2THNSbGY4L2tZZUw5QnJORVl0eGtwUnZaZCtkWVQiLCJtYWMiOiI1ZGVmNThhYmU2N2NjMWY5NTkxODZiN2U2OGM2MTU0Y2NiMzFlMDViNDNhYjVmYmNmZTk2MDc3M2YwNWRlNzNhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-28500416 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G4oUC4dvL91DvSdXtTSUa87JqfM4sfNNzjOiK6ns</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">16z2m35FIhU8zNuOqkDvqLO3UyIjTT7e4c1Y0Zqf</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-28500416\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-218667506 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 15:58:25 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjZBeVV6eDdHSGhDbHRSY095RmUwMFE9PSIsInZhbHVlIjoiUndiRDV0enlQVU4xTVpIelNwTk1iTXRzM3V0VlBycnJyRkpaSGhtTEgrTVlmZkdCYkdkeTVDc0sxSS9ySG5xMTVzSFo1QUpwS2xQRVZLS3FpWUxIVGkyYW5uV2hDd09jQmQyenV1emRLZGpZemRTNXlXOEJuVDNVY3FzNW1PTE5oZktveWVIdnI0Q0ZTMjhRR0JvUHVEazFqQ2xIVXBoR296TVN1bWZPQyt6Y0R5RGpPclgwVnN6eU0vVHhEV2pEZm9Jd2JaTDdSb1dqdzhoMnp1VHhPeGlvUldHNFlkY0VUZDdBaTVRZmN2MkU2Rmd5OXhLMWFYSmFhZmVVMGIwZWVuWnJyQU9HS2djenV0NUVXTjRqTHhoR0daLzZUaTN0L29zcXAvV0dxQVN2NWFTMGtWR1RXWGFxY3c0MHpTeUhDWDl3dDQ0NW1iRTJrd0MrN1hLOGhPdDV1VmtYOFM1ZHEvazhRNXl3M3c2R3dqYWNQUEVNdFlHR1lKVHpjQXNkNnA2ZUpUOEJBWGxsOElsOWJnOW91MDhCQWZSK1F5SWhQRFR4SW1US2RhOFg5c0RpNSs4MTBJaFdvSWtSc2ZRZzVXTVR3cXpMWTkvN1V5aEplRkFWdzR5VU5LS2ppS2duV283dHVjLzJSdzd4N29BNzRSaU5OZk8zQTIrS25PYlkiLCJtYWMiOiI2OGNhZWU0NjljYTM0NTBiZjRjZGI3NzFlNDViMjEyMGNlODlmYjEwNWVlMjIzMTdhNjQ0YmY0MjE4MzRhMTg4IiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 17:58:25 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjVxVGh2cHJhcXhtZ0xiSFBscXMzOFE9PSIsInZhbHVlIjoiSTFtYThGNWgwZkt5R05tOU5DWDlpN2tacXI2UVRsd1ZGTmdKRmd1b2o1Zzh2bkhPQmxPZnpsbVZEQW1GbUd6TGhIUjN2bVJ6TGZwVXN3dVNpUCsrVGc1RWVTYTlWNllJSTkxbVFUeUp5UzBrVkJUcThMeEk0b09RTDJIeUF4TS9TVDBFKzU0UURTSDZTUFoyaDd1dit1U0RHc2QyZ3ZXRml5di9KSURVMlRDUmlIbVg3UkVxWnpzY2hmTTYxL0UzQW5uaFFFcEhyazZBTGM1NWswUFFCOGlicENYRVprczIrcjdoOUZQSHlQd05MbWl1cE9uZURNd1BHOFFtMDkxajJPVUlEb2RoT09kNG1waTBTRjdUNXNZUzBCWTFFdWJ5OGJCT1Q0WmprQlFydDlNaW5YNDBBN2dvVDQveHZOcWNrbG1hNFRZcTlJbGZtdnlrVkdqOEp2RU85aWcvdVFhUmtmME5keDVBUFd0RTVuTkJOVHJCdGdYKzVkcys5UkNIQmhYYm9LK3ZISlFyWjJaOFE3cVB3eTU2dHhWYVArRlJmWWxMclBEMVRNNFNFcFZPYWZWNk43NjFIcjBhWm96Q3dQVWtaaG9rdDd2K01wRklyZDFTMWRhR2dYaFpOTnp6RGdzWE1vY2hUcFRXSWpXcG5wQkl5ZVlkU3hSTTJnRHciLCJtYWMiOiIzZmZjZDEwZmQwY2Y0NGM2NDM1ZDU3MGVlMWQ2Mzg0YzIyNmYzOGNiN2Y3MmMxOTFlYTVhMjNmYmY2NDk0ZGYyIiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 17:58:25 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjZBeVV6eDdHSGhDbHRSY095RmUwMFE9PSIsInZhbHVlIjoiUndiRDV0enlQVU4xTVpIelNwTk1iTXRzM3V0VlBycnJyRkpaSGhtTEgrTVlmZkdCYkdkeTVDc0sxSS9ySG5xMTVzSFo1QUpwS2xQRVZLS3FpWUxIVGkyYW5uV2hDd09jQmQyenV1emRLZGpZemRTNXlXOEJuVDNVY3FzNW1PTE5oZktveWVIdnI0Q0ZTMjhRR0JvUHVEazFqQ2xIVXBoR296TVN1bWZPQyt6Y0R5RGpPclgwVnN6eU0vVHhEV2pEZm9Jd2JaTDdSb1dqdzhoMnp1VHhPeGlvUldHNFlkY0VUZDdBaTVRZmN2MkU2Rmd5OXhLMWFYSmFhZmVVMGIwZWVuWnJyQU9HS2djenV0NUVXTjRqTHhoR0daLzZUaTN0L29zcXAvV0dxQVN2NWFTMGtWR1RXWGFxY3c0MHpTeUhDWDl3dDQ0NW1iRTJrd0MrN1hLOGhPdDV1VmtYOFM1ZHEvazhRNXl3M3c2R3dqYWNQUEVNdFlHR1lKVHpjQXNkNnA2ZUpUOEJBWGxsOElsOWJnOW91MDhCQWZSK1F5SWhQRFR4SW1US2RhOFg5c0RpNSs4MTBJaFdvSWtSc2ZRZzVXTVR3cXpMWTkvN1V5aEplRkFWdzR5VU5LS2ppS2duV283dHVjLzJSdzd4N29BNzRSaU5OZk8zQTIrS25PYlkiLCJtYWMiOiI2OGNhZWU0NjljYTM0NTBiZjRjZGI3NzFlNDViMjEyMGNlODlmYjEwNWVlMjIzMTdhNjQ0YmY0MjE4MzRhMTg4IiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 17:58:25 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjVxVGh2cHJhcXhtZ0xiSFBscXMzOFE9PSIsInZhbHVlIjoiSTFtYThGNWgwZkt5R05tOU5DWDlpN2tacXI2UVRsd1ZGTmdKRmd1b2o1Zzh2bkhPQmxPZnpsbVZEQW1GbUd6TGhIUjN2bVJ6TGZwVXN3dVNpUCsrVGc1RWVTYTlWNllJSTkxbVFUeUp5UzBrVkJUcThMeEk0b09RTDJIeUF4TS9TVDBFKzU0UURTSDZTUFoyaDd1dit1U0RHc2QyZ3ZXRml5di9KSURVMlRDUmlIbVg3UkVxWnpzY2hmTTYxL0UzQW5uaFFFcEhyazZBTGM1NWswUFFCOGlicENYRVprczIrcjdoOUZQSHlQd05MbWl1cE9uZURNd1BHOFFtMDkxajJPVUlEb2RoT09kNG1waTBTRjdUNXNZUzBCWTFFdWJ5OGJCT1Q0WmprQlFydDlNaW5YNDBBN2dvVDQveHZOcWNrbG1hNFRZcTlJbGZtdnlrVkdqOEp2RU85aWcvdVFhUmtmME5keDVBUFd0RTVuTkJOVHJCdGdYKzVkcys5UkNIQmhYYm9LK3ZISlFyWjJaOFE3cVB3eTU2dHhWYVArRlJmWWxMclBEMVRNNFNFcFZPYWZWNk43NjFIcjBhWm96Q3dQVWtaaG9rdDd2K01wRklyZDFTMWRhR2dYaFpOTnp6RGdzWE1vY2hUcFRXSWpXcG5wQkl5ZVlkU3hSTTJnRHciLCJtYWMiOiIzZmZjZDEwZmQwY2Y0NGM2NDM1ZDU3MGVlMWQ2Mzg0YzIyNmYzOGNiN2Y3MmMxOTFlYTVhMjNmYmY2NDk0ZGYyIiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 17:58:25 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-218667506\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1039961931 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G4oUC4dvL91DvSdXtTSUa87JqfM4sfNNzjOiK6ns</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://localhost/bill</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1039961931\", {\"maxDepth\":0})</script>\n"}}