{"__meta": {"id": "X3f3f2ba5fa51c7f15d8f557ccfc07239", "datetime": "2025-06-26 15:59:47", "utime": **********.544568, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.064825, "end": **********.54458, "duration": 0.4797549247741699, "duration_str": "480ms", "measures": [{"label": "Booting", "start": **********.064825, "relative_start": 0, "end": **********.463436, "relative_end": **********.463436, "duration": 0.39861083030700684, "duration_str": "399ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.463445, "relative_start": 0.3986198902130127, "end": **********.544582, "relative_end": 1.9073486328125e-06, "duration": 0.08113694190979004, "duration_str": "81.14ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45671280, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.014899999999999998, "accumulated_duration_str": "14.9ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.497889, "duration": 0.013529999999999999, "duration_str": "13.53ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 90.805}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.5214648, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 90.805, "width_percent": 2.752}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.530717, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "kdmkjkqknb", "start_percent": 93.557, "width_percent": 3.758}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.5370429, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.315, "width_percent": 2.685}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "ET1WIQmDwH5DmIalEePkmJkdqregxR405rAMGD3B", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/dashboard\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-137117798 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-137117798\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1673297064 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1673297064\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-752427235 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ET1WIQmDwH5DmIalEePkmJkdqregxR405rAMGD3B</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-752427235\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1608887621 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1842 characters\">_clck=1lqc4d8%7C2%7Cfx3%7C0%7C2003; _clsk=1h4itwx%7C1750953578911%7C2%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Imd6Yk9hMjNvN2NuaWIrN2VDRjZUU0E9PSIsInZhbHVlIjoiVURaRGlQdWxTc3pqWVlPb3NTeC80YlRGZmpUbWZWM0hVaXljNnN3RmtRa3pTVUNzTWFLOW1kdnBUR1d0V0JYeURNdm9YL2hSNnVaQTFHQkU2UFRSdFp5NUZGR1FjcnNrWTViRXozVzJJQS9NQjg0R0dhemR2ZmFFdjh4UncxVzFsU1ZFN0RoRmNQOEI4d1VMdk5FOGpOa1RKVy82S2JidnVqS0hycXZhamI0cG5JendibXluOUk1bWtzL3VqYURqV2pkQ2hKc1R1RXQ0QWl1d2RXejdFazg4allpL3g4d0kycUNGdmxTZHRtY2QwTmtrc285YlhyN09FeXBFYXpPaXZwK1R3T0NHdFdBWHVCVnE0M3R2aGRqR1hhb0ltL1lJWUI5MHNlUkl2TUc0eW9Dd0NTLzFQQ01LQnM0OTFrMUNoMklCd2s3eDNwQVVVSDhiK0tpYmUxL1RMWGFhVG8rOXc2MmkwOE5HTmN1NzVseHZLdnFSaFZuNllyT1ZZL3VFVk1tZEJLNm9vQ2pWdzh3Q0FlME5OaDU4RDVLOWN1QmhtM0EyRjhZOUVGelZDSkZwbXpWc2ZPQlFjNzVjdnpYRlRwRWVKWXBLdkJDNzhIcCtIZU5FN01OTzhkK2pXeXB6dEpNOFZsRkQxWGpJa0hrbzdneDNjTG82SVhPVktYWlQiLCJtYWMiOiI4MjAwM2Y3OGNhYWExM2FjOGNlNDM1MmRjZGNmNDlkZTBhNWQ4MDVjMjhhNDdiNGJmMjUzZjMzMzk4YTliM2YyIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InRZSmZabVFQQWFodTBxTEEyQW4vRXc9PSIsInZhbHVlIjoibWRsc1NmWnZWenYxTGJ6Mm81bUJBbkxERndzbFJlQWFZWll1K1l0ZDZSY3pPOHpCTC9rNmlldEl5cUNneWJDdzlEeTBtbS9adyt1MGhzNU02YXhBVFBRT3A5Ukd5ZXFXT3V6SURYRVNEQjRDSnFOU2sxRktwWkVrTlZGalVjT1NEQVEyQjA0blpjVGd2OGpRTGlpaEwvRGl1MUMyN2ZpUVE5L25lQnhqQmgveVEwTTFaTmE0ZmViQUw1T0N4bGpYeDRzSXdtQVVvdVVVb2QydlkxUXRoUlBubXNqSUFPYjREL0VlSm82RUtzYzlRd1Jka2w0RzNvTVVNRytmNTkrRHdtRjByOHlNaE1laUplUUN5U3NjQWlqTXNGU3JsekFGa0Y4SC9laUdmVUVLajhQaFlCZFpRbFpVNVlMK2NkTmZnK2Y2RTdPVXVTN3JhS2Fsd0JLQ2FMY0MyeVhTRmhlS2FJVlBWaTN0N3FwSStDV3J4T3MwM0NRSGZFNk91VFZQQytGQzhVblRxOTIxbTMrODk0SEplUUEvT3FGSnBhbEVxNmw4NmxYR2phVld6SFRIUk9NSlRaRVFvWGNhTWg0ako4MUduRFpuTzJ5T3QzamxEekJYTUVZelVYQ1FBazZlY3ZlcGkxNm9DQ1A2bnQrUC9mQUl1eDlTaTZTN1JMRlAiLCJtYWMiOiIyNjQ2YWI1OGRmMGQ4YTQ2NzJiZWNhN2IwMTc4ZWQ5OTljZWI2NGNhMmQzMGExMjI3ZjZmZGVhZjhiMWVhYTlhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1608887621\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1099959105 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ET1WIQmDwH5DmIalEePkmJkdqregxR405rAMGD3B</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OKKbyGk1l1oGFNUe7gxioQDH0z4ATppFVbVTtLek</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1099959105\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-272386784 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 15:59:47 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlR1QWZxVkcvcTBSMVp0NTlXckRWYVE9PSIsInZhbHVlIjoiV1oyODE3L0ozNS9Tdno1cWtReEFsWEQxQ1pLY1g2T0p3alpaKy9BNGxlWFMwOU9aeHA0MTBmY3Bmd1N4QjlhcWhDRVVNRStQb1BsNzRIUWh3bW1ib0gxTzJCbXo1RnJab2NOSGFzTEVZTG1FemJYRVVGamVaUU52Q1c2NHBGc3RmNHRRZFBHYVVBaWRCTnpOL3lCUGRFRHo4N2lNb1E4WGZFMk5ZTFlUMElyRXVlWkNqK3BxNTJ2VnpLVkdtSy9nWEtod21IK1JaZzFKQmpzQTd2eXlkaVZUREpkclZ4aDBtRHZMVFhtZ2dnMW0vUzdjeFhCc1lQWXk0MGZKQnZxS2xHVVFFQ0JuazFTTnFwL3A5V0lrenZaL1JtdXAvUkpaaU5UdG80clQwMC9JNGphRjZ3V1hQZTRKcUM5UmFCMTh6NUdGVDdzaUdxWWp4KzFNNE1XVTI3NTAvZUtyNnBQU3JmZHhTRFcwUzlHcWYxV3RiVXpRNTV4N3YwZVd2WnAzcVdOTkpmM2E0MmM2SXlJQ2lPZENpS2t1UXlqOXhOdkdWM25vbXhGTGZkUTVRYyt4TzhVWTlBR2pqL25TeUxvU3BIRU1sZCszMjhNeWZuOWc0TmNrTEhkcEsrcVNHL0xuZVN4WTVUTDVXSGtROUZJZXJBOGc0WU9sOG5aR3hVT1AiLCJtYWMiOiIwZDUwOWIxOTNhMDA0MTM1MmNlYTJhYTBiNzU3YzI1NWU4MTE5MDk2MDJlZmRlYWZjMmIzMzZkNTlhMDFiOGUzIiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 17:59:47 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlFyQUd4bUlvN3VIWlV1MEVMbVAxZUE9PSIsInZhbHVlIjoiRnFQRGxCRjRGUEdiQWRVVFRnZDA0eE8wa095b1NrS0VuZVJCbENpenRTSEdjSUFyZnptb3R5Q3RIVmVyUkpwVWNvQmtrNXlvWEVCenUwT1grWUx2RW1RQXc2TVhvRDNkWWVBRDA0WllzK2JaV3NYVTBSNms4c251dFBDYXNZVkVUR3czdHBwVnBJVVhYUE04cFdiekdFMlkrbS9wZXY5dDJuamlxeHNuUEYzalQ2SFNpbGNncUdWWSt0MnlKWEhIV0VhbjJpOEZmNStQaTdIbTkyZVFVMlhHL1VnT0JiMmdtTXJRandJMlh6WFJSS3NNNmVvK0JLSmhzQUJPM3pPaU13M21JK05HcUVrbTBndWk5TjY1VEJEU3lmemlXdFVGRGtvV0ovU1BaMUcxZE9wSWNJSFVSbDdDOERwRjl1Y3lITWh0VGZ2OERMQ1h1ZmxaMWxRbDVYODVNSERQRDVJUXpPZXNMK0h0RWRaTjRkUUpmckNIbHdueVlrUWczV05wVmlISjc2SnFYSllsejZVS2d1dmdiRE1Uc0EwOXpsTHJiTDRsd0xQbE1WRlVzaUw0T1E0VEc4akk0ZzFXU0wwRHpqRnZsaXpIeE5EcDZXVWFSNnhGdFpGTjh4azE3K2FtUjVUUEl5S2p2Z0lZK3BJb0RNVjZOdUtsZHdXbUN6ODgiLCJtYWMiOiIyNDIyYjVlZmY0OWNlNzMzZjhjMmRmYzEyMzU5NTVkYTdmNTM1NmViYjJjNzM2ZDZhODY2N2ZjZmNjZmM4N2IxIiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 17:59:47 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlR1QWZxVkcvcTBSMVp0NTlXckRWYVE9PSIsInZhbHVlIjoiV1oyODE3L0ozNS9Tdno1cWtReEFsWEQxQ1pLY1g2T0p3alpaKy9BNGxlWFMwOU9aeHA0MTBmY3Bmd1N4QjlhcWhDRVVNRStQb1BsNzRIUWh3bW1ib0gxTzJCbXo1RnJab2NOSGFzTEVZTG1FemJYRVVGamVaUU52Q1c2NHBGc3RmNHRRZFBHYVVBaWRCTnpOL3lCUGRFRHo4N2lNb1E4WGZFMk5ZTFlUMElyRXVlWkNqK3BxNTJ2VnpLVkdtSy9nWEtod21IK1JaZzFKQmpzQTd2eXlkaVZUREpkclZ4aDBtRHZMVFhtZ2dnMW0vUzdjeFhCc1lQWXk0MGZKQnZxS2xHVVFFQ0JuazFTTnFwL3A5V0lrenZaL1JtdXAvUkpaaU5UdG80clQwMC9JNGphRjZ3V1hQZTRKcUM5UmFCMTh6NUdGVDdzaUdxWWp4KzFNNE1XVTI3NTAvZUtyNnBQU3JmZHhTRFcwUzlHcWYxV3RiVXpRNTV4N3YwZVd2WnAzcVdOTkpmM2E0MmM2SXlJQ2lPZENpS2t1UXlqOXhOdkdWM25vbXhGTGZkUTVRYyt4TzhVWTlBR2pqL25TeUxvU3BIRU1sZCszMjhNeWZuOWc0TmNrTEhkcEsrcVNHL0xuZVN4WTVUTDVXSGtROUZJZXJBOGc0WU9sOG5aR3hVT1AiLCJtYWMiOiIwZDUwOWIxOTNhMDA0MTM1MmNlYTJhYTBiNzU3YzI1NWU4MTE5MDk2MDJlZmRlYWZjMmIzMzZkNTlhMDFiOGUzIiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 17:59:47 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlFyQUd4bUlvN3VIWlV1MEVMbVAxZUE9PSIsInZhbHVlIjoiRnFQRGxCRjRGUEdiQWRVVFRnZDA0eE8wa095b1NrS0VuZVJCbENpenRTSEdjSUFyZnptb3R5Q3RIVmVyUkpwVWNvQmtrNXlvWEVCenUwT1grWUx2RW1RQXc2TVhvRDNkWWVBRDA0WllzK2JaV3NYVTBSNms4c251dFBDYXNZVkVUR3czdHBwVnBJVVhYUE04cFdiekdFMlkrbS9wZXY5dDJuamlxeHNuUEYzalQ2SFNpbGNncUdWWSt0MnlKWEhIV0VhbjJpOEZmNStQaTdIbTkyZVFVMlhHL1VnT0JiMmdtTXJRandJMlh6WFJSS3NNNmVvK0JLSmhzQUJPM3pPaU13M21JK05HcUVrbTBndWk5TjY1VEJEU3lmemlXdFVGRGtvV0ovU1BaMUcxZE9wSWNJSFVSbDdDOERwRjl1Y3lITWh0VGZ2OERMQ1h1ZmxaMWxRbDVYODVNSERQRDVJUXpPZXNMK0h0RWRaTjRkUUpmckNIbHdueVlrUWczV05wVmlISjc2SnFYSllsejZVS2d1dmdiRE1Uc0EwOXpsTHJiTDRsd0xQbE1WRlVzaUw0T1E0VEc4akk0ZzFXU0wwRHpqRnZsaXpIeE5EcDZXVWFSNnhGdFpGTjh4azE3K2FtUjVUUEl5S2p2Z0lZK3BJb0RNVjZOdUtsZHdXbUN6ODgiLCJtYWMiOiIyNDIyYjVlZmY0OWNlNzMzZjhjMmRmYzEyMzU5NTVkYTdmNTM1NmViYjJjNzM2ZDZhODY2N2ZjZmNjZmM4N2IxIiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 17:59:47 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-272386784\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-77715709 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ET1WIQmDwH5DmIalEePkmJkdqregxR405rAMGD3B</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-77715709\", {\"maxDepth\":0})</script>\n"}}