{"__meta": {"id": "X2541fadc2f721f4ff78f3b58629d0efc", "datetime": "2025-06-26 16:00:18", "utime": 1750953618.028252, "method": "GET", "uri": "/login", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.579284, "end": 1750953618.028268, "duration": 0.44898414611816406, "duration_str": "449ms", "measures": [{"label": "Booting", "start": **********.579284, "relative_start": 0, "end": **********.952748, "relative_end": **********.952748, "duration": 0.37346410751342773, "duration_str": "373ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.952756, "relative_start": 0.3734719753265381, "end": 1750953618.028269, "relative_end": 9.5367431640625e-07, "duration": 0.07551312446594238, "duration_str": "75.51ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46560784, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 4, "templates": [{"name": "1x auth.login", "param_count": null, "params": [], "start": **********.996636, "type": "blade", "hash": "bladeC:\\laragon\\www\\ERPcopy\\public_html\\resources\\views/auth/login.blade.phpauth.login", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fresources%2Fviews%2Fauth%2Flogin.blade.php&line=1", "ajax": false, "filename": "login.blade.php", "line": "?"}, "render_count": 1, "name_original": "auth.login"}, {"name": "1x layouts.auth", "param_count": null, "params": [], "start": 1750953618.002305, "type": "blade", "hash": "bladeC:\\laragon\\www\\ERPcopy\\public_html\\resources\\views/layouts/auth.blade.phplayouts.auth", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fresources%2Fviews%2Flayouts%2Fauth.blade.php&line=1", "ajax": false, "filename": "auth.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.auth"}, {"name": "1x landingpage::layouts.buttons", "param_count": null, "params": [], "start": 1750953618.018935, "type": "blade", "hash": "bladeC:\\laragon\\www\\ERPcopy\\public_html\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.phplandingpage::layouts.buttons", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2FModules%2FLandingPage%2FResources%2Fviews%2Flayouts%2Fbuttons.blade.php&line=1", "ajax": false, "filename": "buttons.blade.php", "line": "?"}, "render_count": 1, "name_original": "landingpage::layouts.buttons"}, {"name": "1x layouts.cookie_consent", "param_count": null, "params": [], "start": 1750953618.021229, "type": "blade", "hash": "bladeC:\\laragon\\www\\ERPcopy\\public_html\\resources\\views/layouts/cookie_consent.blade.phplayouts.cookie_consent", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fresources%2Fviews%2Flayouts%2Fcookie_consent.blade.php&line=1", "ajax": false, "filename": "cookie_consent.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.cookie_consent"}]}, "route": {"uri": "GET login/{lang?}", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@showLoginForm", "namespace": null, "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=344\" onclick=\"\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:344-359</a>"}, "queries": {"nb_statements": 9, "nb_failed_statements": 0, "accumulated_duration": 0.00632, "accumulated_duration_str": "6.32ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 555}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 348}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.980259, "duration": 0.0016200000000000001, "duration_str": "1.62ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 25.633}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'kdmkjkqknb' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 537}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.9840791, "duration": 0.00259, "duration_str": "2.59ms", "memory": 0, "memory_str": null, "filename": "Utility.php:537", "source": "app/Models/Utility.php:537", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUtility.php&line=537", "ajax": false, "filename": "Utility.php", "line": "537"}, "connection": "kdmkjkqknb", "start_percent": 25.633, "width_percent": 40.981}, {"sql": "select `full_name`, `code` from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 543}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.988519, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "Utility.php:543", "source": "app/Models/Utility.php:543", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUtility.php&line=543", "ajax": false, "filename": "Utility.php", "line": "543"}, "connection": "kdmkjkqknb", "start_percent": 66.614, "width_percent": 2.69}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 4748}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 4682}, {"index": 15, "namespace": "view", "name": "auth.login", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\resources\\views/auth/login.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.9971101, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4748", "source": "app/Models/Utility.php:4748", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUtility.php&line=4748", "ajax": false, "filename": "Utility.php", "line": "4748"}, "connection": "kdmkjkqknb", "start_percent": 69.304, "width_percent": 5.222}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 4748}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 4682}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\resources\\views/layouts/auth.blade.php", "line": 10}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1750953618.0029962, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4748", "source": "app/Models/Utility.php:4748", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUtility.php&line=4748", "ajax": false, "filename": "Utility.php", "line": "4748"}, "connection": "kdmkjkqknb", "start_percent": 74.525, "width_percent": 7.12}, {"sql": "select * from `users` where `type` = 'super admin' limit 1", "type": "query", "params": [], "bindings": ["super admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 4113}, {"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 4155}, {"index": 18, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1750953618.0117471, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4113", "source": "app/Models/Utility.php:4113", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUtility.php&line=4113", "ajax": false, "filename": "Utility.php", "line": "4113"}, "connection": "kdmkjkqknb", "start_percent": 81.646, "width_percent": 5.063}, {"sql": "select `value`, `name` from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 4114}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 4155}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1750953618.014207, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4114", "source": "app/Models/Utility.php:4114", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUtility.php&line=4114", "ajax": false, "filename": "Utility.php", "line": "4114"}, "connection": "kdmkjkqknb", "start_percent": 86.709, "width_percent": 3.639}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 4748}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 4682}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\resources\\views/layouts/auth.blade.php", "line": 39}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1750953618.015664, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4748", "source": "app/Models/Utility.php:4748", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUtility.php&line=4748", "ajax": false, "filename": "Utility.php", "line": "4748"}, "connection": "kdmkjkqknb", "start_percent": 90.348, "width_percent": 5.538}, {"sql": "select * from `landing_page_settings`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "Modules/LandingPage/Entities/LandingPageSetting.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\Modules\\LandingPage\\Entities\\LandingPageSetting.php", "line": 27}, {"index": 19, "namespace": "view", "name": "landingpage::layouts.buttons", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": 1750953618.0198, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "LandingPageSetting.php:27", "source": "Modules/LandingPage/Entities/LandingPageSetting.php:27", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2FModules%2FLandingPage%2FEntities%2FLandingPageSetting.php&line=27", "ajax": false, "filename": "LandingPageSetting.php", "line": "27"}, "connection": "kdmkjkqknb", "start_percent": 95.886, "width_percent": 4.114}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "c7Olbm9j3VGWU97WwGbmhGgptmjEatJHEweNGUI4", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/login\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-656062744 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-656062744\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-333714319 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-333714319\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1272192073 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1272192073\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-122169660 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1842 characters\">_clck=1lqc4d8%7C2%7Cfx3%7C0%7C2003; _clsk=1h4itwx%7C1750953610407%7C6%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkNybnFmUjNDODVDS1RlWmZyQmcyWkE9PSIsInZhbHVlIjoiRm51YjZodE12MkowUWlaSlo5V3ZyZkFUZXhjVXZTYzVLbjRLR1dkU1phc1dCSERDaTN3ZzlqSm1idDh4OTZGTEJRWTA1c1JudGpld2lqYnY4RXk1QktBelQ4V09EdUJWaVY3eVc0Ukp0QnFNcWluS2VDMjlqRVphVjRMVHl5ZVBrY0E2ZW05NkhWTzZ5NlAySWl4M3hsK0x4MFU5bzNXZ3BLM2hmaXVabzNnSHJlc0M1MWdRWDUwSnBGa21JUjBLUnNlZzNSaWtxRnV3VkwzWVBacDRPYW01QlFBbUsvL0J0MGgxeUtWQnRYeEFxVEdwRHYxK3kyVUdzUzc0TVY0WGM5a0VRQjJJOGl0LzJ4ejBFdzFrU3Y2Q0RRUWk4SVljRSthUU5VT2ZjTDhsSWQrbVN2bVl3V3ZnMitaUFd6TnZ5UXJKL2xhenFIekhsakhqSy80bHFuTFNueXlJZDlWNnJLOFRHRGFmT1hYdDNLODYwWjZGc0IydTBLWWxvdG4rUW1QcDFoYm5RZHM2b3RXdzdnTDBrZ1o5RC9Za1lGUnYyNk03MUZ3VTFSZnZBM2lEcFNvUktUUTNkUVpzQ0tQb094UlZQTm4rT043VEdRM1ZBejBWOTFKQ1lNMWFzOEJXQnpEdEIxeElZT0pmQUh0ZGhpVE5lclZUcEVjUEpIaVciLCJtYWMiOiJkNjUxODM3ZjNkYzA4MTVkNDU5OTM4MjQxNGQwNTI2Zjk4M2VmZTVmNDg1YzA4MTM0YTViNGExMmIyYTdlODczIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjNUMm1IYnh3cUw2cWtwSkY5YzFGSEE9PSIsInZhbHVlIjoiZ09wZ3NYb0x6K2NXMFR5S0xZNjJpSWkxMUN3MkZobXRKa0hEVHFJODgrUmZpVmIxZi9hM0hpSkJYbUJqdGtxc1dWdGVJM1UyeTNydVZKNXRrakZIVEtTWmpiYURCMGRMaUlLZ1FhQy9uellUdk1scXk4NUNlQnF6OE9jbGs4R2pOZkJyVVBsVkI4NXlYaFczdGtON3hsNXVXVmFQanBoSmZQQ1pKODcvR014V1h0ckd4VWxseVdpcm1lRWwvTmFlY1BHRWRBTXo2VWhYSDNjYXpDb2ptT1RzVGtZSU9lckh6NEJMQVZBVjk0U1Zoa1YzbVk5NmpBL3JPZ050bnRLU1RGRkJnVjUydmtlNFVhdERqVVpzNW5ud2k2L3oxa2FWWFlmeEJVRTJwT2RjQVkzd21uelFDYW96ajBDNk1hV3RrZWdlak95ZCsvTERabFIyR3F5NVExZnBHUnNCL05uS0ljYWZWZWs0UFFQYkdVckZXVHRhMzZjbm9QZ1VUQ2cvWXBmL3UwYVRjcWVWVThpWThSS1BPZ1V6YitnWUtnd3orZUNZekY4Qmd2VXR1aHhjOEdpdW1IMVhuSkx5Z2xpaUpmcXVNSzJscWhvNXJTVTV6L1ZyWjJaWldWMFRUSGF6V2Nwem9KRWxpSnEwcG5iRVUvMzREcWRMSFNYdlEvTEciLCJtYWMiOiIzZmFiYjgzYTFkMWY4YWU2NDczOTg5NTg2MGU0MjY5ZDRlNDcwY2FmODk5MGM3MjE1YmZhMjg2NjIyMzU1MDlmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-122169660\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-980028588 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c7Olbm9j3VGWU97WwGbmhGgptmjEatJHEweNGUI4</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ISP8BnnxcALCnxfnm8xYKkDYjUF7hhDCxrzDRCoE</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-980028588\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1501349329 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 16:00:17 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlVjaXd3SkRlczZBSEVnNnFMREsxMWc9PSIsInZhbHVlIjoiOUtwVWowSlA0MW13RE92NFFCS3VxQzRRTnRTRHB4bi9lQWFKUEJ4enp3dUlPd0hjeGVoZ3pZeGFzVk5LVzhTamNCRUxsRnJZaEZ0ZmpDM2dhanRxbFByc3JjN1dHQVE5c2cyYU9QU3E5NlZGNEduSjZ0cEpBQjFZaGc1WDJMck94eWgybzFUbmhKQW5nOHZGRTNzenMyK1c4RGlseU9BUUIwOUJrb1BJT1RIMjJiOXNWZEF0aWI1cmF0WTNBTlVkSkN6VjFneVhCWkpGVkxwenhQVWdCV0FGQUhZTGh1YkJMRnVxdFMwRHdrL0VwbDVNZkVhZUlGTmRLZXkxTGQ5cHdvZW9IaVRRbGprWnhWY0xpb3VpSU5oajRrcEFqUjJzL3ZpWXNEQ3daZ0Z1NHM4SEprSDNSdUdMbE9NT1Jab29IYWQ2K3ZVcWNVc0REV0ZFQlNWeWJaQ3ZSc2h3TFhSa3p1L2M5NTd1SzVUYWpBSElxZDVpYWI5VHBSVnZobXJCU3BvMncvVkp2aVI1cnIybHNESEdFemNwNHRlTDhxcGsyL3EzV1I4RWIrR2h1cytVZTUyOVJ0RXg1Q0NlRTRnaFRXKzBkWElkQ3h2VUNaNXVubHRMdEhvQTNpRUJxTVdlSGVzbjI0NXlFcHd3OFRTbXp5RmlLVWZlYURDSGVrN2YiLCJtYWMiOiIzNjYyMzkyZjhhYzhiZWYwZjM2MzI4MmM3MmFhMjA2MDMxNzNmODkyYmM1MTZjYTEwOTk1YjFjM2M1MzE0NDkwIiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 18:00:18 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkRIdWdLUHpzSDEyUVg5RFc1TGNaenc9PSIsInZhbHVlIjoib1lxZmRzQlhMNXFFbG5DZnJYcWpZa3RUNzlweFZ4ekhaWEJRVWo3TnZHWUcyQVNkSXM5SDI5YnhtakFtL08vS1lCVk9xa3psZ053Q2VyZzYvZE9ISm5ka292bTFhUHEwRUlPajdMdkhGcms5dWxJYXV1N0w2R2VpbG55MlVHV1BBemhGTDNrdzhiMVZLQUJxeHVRK3RubnBzNnU5WU5iV3dncTdDSXdybzNSZm9CRTBmSWh4YjF6WElObzRGSkxzREZ5eDkzYlp6eHczQzhtNlR4bzlaakh4V3dKSmxTekY4c0xxTnkxcVJkMlh6cmFBNk1DZ1lmSFZoYU1ZYTZMdmFXMDZtc3VNUE5qWHhvY1pyQ1ZpVnhUSXowMldwUnpvc2p6dHhwWDM0ZFcrT2xxRnphRmhvaEtIQlUzY3U1RVg1Z05WdTBINzljVVF1SmdBbU5aTU5qTVB2VUs4REQ1MUEyZVlMZ0Ixb2xEeVE0UHNiME10MDJ3K1NpYjEzNXBNZlhYZ2pHbEJJZ2lGdWdpakFjSU1abTBnTWF0RW9uOVVhWjJZSzJtNmV0UFhEcWNlRVJ3RlU0RVJFMlJzNDhOVHdFb013ZHdGYTZoeEl6eWdvblVadTMwVFBnMVlTcURpcUh2SWs4RlN4MFI1cjZnZmNBamNjc2lFd3B6U29LMXMiLCJtYWMiOiI5ZjQzMzUyOGQ2MzA2NGM5Yzc3MjkzYjZlN2YxMTJlYzI5N2ZhYzlkNDk0M2UwNDc2MDE2MDZhZmY5NGM1ZDRkIiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 18:00:18 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlVjaXd3SkRlczZBSEVnNnFMREsxMWc9PSIsInZhbHVlIjoiOUtwVWowSlA0MW13RE92NFFCS3VxQzRRTnRTRHB4bi9lQWFKUEJ4enp3dUlPd0hjeGVoZ3pZeGFzVk5LVzhTamNCRUxsRnJZaEZ0ZmpDM2dhanRxbFByc3JjN1dHQVE5c2cyYU9QU3E5NlZGNEduSjZ0cEpBQjFZaGc1WDJMck94eWgybzFUbmhKQW5nOHZGRTNzenMyK1c4RGlseU9BUUIwOUJrb1BJT1RIMjJiOXNWZEF0aWI1cmF0WTNBTlVkSkN6VjFneVhCWkpGVkxwenhQVWdCV0FGQUhZTGh1YkJMRnVxdFMwRHdrL0VwbDVNZkVhZUlGTmRLZXkxTGQ5cHdvZW9IaVRRbGprWnhWY0xpb3VpSU5oajRrcEFqUjJzL3ZpWXNEQ3daZ0Z1NHM4SEprSDNSdUdMbE9NT1Jab29IYWQ2K3ZVcWNVc0REV0ZFQlNWeWJaQ3ZSc2h3TFhSa3p1L2M5NTd1SzVUYWpBSElxZDVpYWI5VHBSVnZobXJCU3BvMncvVkp2aVI1cnIybHNESEdFemNwNHRlTDhxcGsyL3EzV1I4RWIrR2h1cytVZTUyOVJ0RXg1Q0NlRTRnaFRXKzBkWElkQ3h2VUNaNXVubHRMdEhvQTNpRUJxTVdlSGVzbjI0NXlFcHd3OFRTbXp5RmlLVWZlYURDSGVrN2YiLCJtYWMiOiIzNjYyMzkyZjhhYzhiZWYwZjM2MzI4MmM3MmFhMjA2MDMxNzNmODkyYmM1MTZjYTEwOTk1YjFjM2M1MzE0NDkwIiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 18:00:18 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkRIdWdLUHpzSDEyUVg5RFc1TGNaenc9PSIsInZhbHVlIjoib1lxZmRzQlhMNXFFbG5DZnJYcWpZa3RUNzlweFZ4ekhaWEJRVWo3TnZHWUcyQVNkSXM5SDI5YnhtakFtL08vS1lCVk9xa3psZ053Q2VyZzYvZE9ISm5ka292bTFhUHEwRUlPajdMdkhGcms5dWxJYXV1N0w2R2VpbG55MlVHV1BBemhGTDNrdzhiMVZLQUJxeHVRK3RubnBzNnU5WU5iV3dncTdDSXdybzNSZm9CRTBmSWh4YjF6WElObzRGSkxzREZ5eDkzYlp6eHczQzhtNlR4bzlaakh4V3dKSmxTekY4c0xxTnkxcVJkMlh6cmFBNk1DZ1lmSFZoYU1ZYTZMdmFXMDZtc3VNUE5qWHhvY1pyQ1ZpVnhUSXowMldwUnpvc2p6dHhwWDM0ZFcrT2xxRnphRmhvaEtIQlUzY3U1RVg1Z05WdTBINzljVVF1SmdBbU5aTU5qTVB2VUs4REQ1MUEyZVlMZ0Ixb2xEeVE0UHNiME10MDJ3K1NpYjEzNXBNZlhYZ2pHbEJJZ2lGdWdpakFjSU1abTBnTWF0RW9uOVVhWjJZSzJtNmV0UFhEcWNlRVJ3RlU0RVJFMlJzNDhOVHdFb013ZHdGYTZoeEl6eWdvblVadTMwVFBnMVlTcURpcUh2SWs4RlN4MFI1cjZnZmNBamNjc2lFd3B6U29LMXMiLCJtYWMiOiI5ZjQzMzUyOGQ2MzA2NGM5Yzc3MjkzYjZlN2YxMTJlYzI5N2ZhYzlkNDk0M2UwNDc2MDE2MDZhZmY5NGM1ZDRkIiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 18:00:18 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1501349329\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-949438007 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c7Olbm9j3VGWU97WwGbmhGgptmjEatJHEweNGUI4</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-949438007\", {\"maxDepth\":0})</script>\n"}}