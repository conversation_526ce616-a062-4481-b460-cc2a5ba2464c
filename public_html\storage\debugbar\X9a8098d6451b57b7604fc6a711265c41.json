{"__meta": {"id": "X9a8098d6451b57b7604fc6a711265c41", "datetime": "2025-06-26 16:02:09", "utime": **********.920846, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.39939, "end": **********.920859, "duration": 0.5214691162109375, "duration_str": "521ms", "measures": [{"label": "Booting", "start": **********.39939, "relative_start": 0, "end": **********.745518, "relative_end": **********.745518, "duration": 0.346127986907959, "duration_str": "346ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.745528, "relative_start": 0.34613800048828125, "end": **********.92086, "relative_end": 9.5367431640625e-07, "duration": 0.17533206939697266, "duration_str": "175ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48717208, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.00775, "accumulated_duration_str": "7.75ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 17 limit 1", "type": "query", "params": [], "bindings": ["17"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.852486, "duration": 0.0016, "duration_str": "1.6ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 20.645}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.862169, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 20.645, "width_percent": 5.935}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 17 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["17", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.8748498, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 26.581, "width_percent": 5.419}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (17) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.8765, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 32, "width_percent": 7.742}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.881239, "duration": 0.00299, "duration_str": "2.99ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "kdmkjkqknb", "start_percent": 39.742, "width_percent": 38.581}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.8863661, "duration": 0.0016799999999999999, "duration_str": "1.68ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "kdmkjkqknb", "start_percent": 78.323, "width_percent": 21.677}]}, "models": {"data": {"App\\Models\\ProductServiceCategory": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 28, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 17, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1699446297 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>17</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1699446297\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.880403, "xdebug_link": null}]}, "session": {"_token": "jEiKRXRaeQw7kpqtZmXNWu3bpmEnaxKAnNI8mVhc", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "17"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-1027956939 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1027956939\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-876713752 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-876713752\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1236272232 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1236272232\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-522050016 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">jEiKRXRaeQw7kpqtZmXNWu3bpmEnaxKAnNI8mVhc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1843 characters\">_clck=1lqc4d8%7C2%7Cfx3%7C0%7C2003; _clsk=1h4itwx%7C1750953725629%7C12%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjNYVmlZQTliUjB0Q1dBZlU5YWhjM3c9PSIsInZhbHVlIjoiQm9YNnZpUHpDQVNiSnBUVStXRlg2WkxtN2VZanVYVVAzQVVCQmt3V2t2a1A0ZFJNNW40V3JBMjlIUVVicW5jekhPbWpBblk4K0JhUkZiUitPZ2pTUjlXQXAxeE1MbFlnbkJkMlBLYmVheG85TG13TGljcDhQdWhwYkhCVGdwZzRGQmt6TitSMzFSdU5YaWt3c0dRWXVCZ0YzMjdEZ2YwaWJwbGZ5SG1adGhzb1o2dXBDQ1JMMnV1NkI1UVVHS2N1Mjh3TG1rUG54dWRCdkxnMzJyZTlkVW1RckIxak02R2k2V0Z0ZVlYSWYwVk03dHBLWHhqdHZQaHk0N0Z3RllOSlFzeDkyQ0JxTVI1b3BFTnd5N0xXMi9jTTRCVzZKVndRN1o4cHNRZXJEcUVnL2RKTzczOGx1RjhWZ0dyRmMyaHl0R2VTa28yOElPdVoyaWszRTNLampyc2VYSmtSaHJrckxTbDh0anhxVEhPNmtLejYvdzl2MFpJVlYxMEtKTTNWWXZNamRKWW1XdWV3WmYrdEZybEVqNGdTMW8vcE5tM3hrUmtmUmp5b0s3WTNtVGJ3R2U1VHEzQVp3WjczRFlzT1MzakZyc3gzVTg0WUhMaVpIQ3BIZ0I5MHlmekUrUll0THRmZjNlenY0eS9QMVhMQ0NPTS9KQlJZckZIaUJuckIiLCJtYWMiOiJhZjNlMGE3OTNiYjE3ODM2MDJlMDY1ODYwZjYxOWE2NGY0Y2ZlMGNkMjZiY2VkZmNiZjMwOTFjYzg4Y2NlNWY4IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ik84VkZMT3htMEF0VnMzcWp5TFRmZWc9PSIsInZhbHVlIjoiL25NSHlGbGkwSjVhdUFFNWVHeDRkUmgwMzV0bUpsczRUeHM1aDFsSU5FZ1J3MWZSZjNOOGkvVEE0eWNkd3NCNFZPZ0xqbFVUOEI4THRDdWJiTjZUVWF2THNkM3JSTDdERzNGdVFYaFl5Z2dybUdYR2Y0YlRXU3JrcFdlZGUvZ29JbUt1SnFJeGtBTWRtMmNJdXZab2YzMnltUzZ6K0VWRnZndFRlamhMMWlBTXhaMWhxWFMwQ2xoRVRCL3FRbGx0VS9kYnI2WkZZMjN0WHVEeGZHb3BHelk4YU9mck1OblkxUm41elU2d0QxN2tPckhpd3c4YVdJQ3ZZNjNJNGt0NThqWHlCUFpRVzVlSHFBZWVvc0UxbHhNMXVscGdvNG9KS3JmbExWNll2aThVMjZaOUM3dnlvU090dlBZck0xS1lTZjZiVXRFV05NWFFIanlnSXF2NE44aVg3V0NtK0Y2QmhNMnNsQWtCM3J5a3ArS3l2MUdTYnZjaFRSWE9FdnRnOXRHUmRMeEhtU2hxY1J2WUIxV1pOTjUyQTkveHlrK2lmT0JmN2VDcUtKazNaaHRNa3JhN2ZWTmRPTjJwamZkVnhKT3JsN25uSjg0eDV3S2Mya3VkbElCME9SSmRpbkdZYnNwdFIveGx1ZWIza2dBNjNRNWthWFhLU2VYdFFrb0MiLCJtYWMiOiJmZTdkOWQxNTIxOGU1ZmVhYWVlZTQ1YmQ5MjY5ZjRmOGY1YWRhMTNkMTgxYTgzMDMyMGU1YzUzNWE5YTI1MWMzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-522050016\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-816896115 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">jEiKRXRaeQw7kpqtZmXNWu3bpmEnaxKAnNI8mVhc</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PSbLK6QAWuUzu3JpIF2TDtYQ1L7TOqCcejFUVX3V</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-816896115\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-49605287 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 16:02:09 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im9TVDlEOHZOT2JOQmMwUS9ZMHVjV3c9PSIsInZhbHVlIjoiK3lyeUg4dXByMDlYR2dsWXJXSzRUenA3dmZaaWdaVjNIRmNKSy8xYUpoZ01Eai9oWXg5VDg4cC90aC9kNVJMM29zNkpiTjl2WGc0WnppQVlQMHh5L2xsUEU4aDVxS254NERVM1lGM09BUFNncW4yQjBnMnVpSFMyZ00yakdHQjdHSFIrd1JNNDV4d1lBUVRISERDWkx4Mm5qbGVJZDJZWFBZS1I3S2tlSXU5bmVmeFJhVmlHSmIyNVBwNURkLzh6WExqbDlrWEFyYk9FdjdUSG1BeHgxZTg3bUJ5WEtZQkpHZW0yb3NiOTl1SGxUaVFEK3V0V3REWmV2MEhyRHZ0RmNuMWhuOGZuQWV3STkyZlVDMG11VEF0OHROUzRsSmdoRnRHckZxMERmYXNLYzFrcWhXM3pwdjZDQzM3dkltNXQ3eC9JQVVPcEhXVkhVWEp1ejNtK3pmQWRNdXVxdGxxUzlGMDBUcWkrVmgvaSsvVTNCSFc1WFhieC9WZnVIV291RkU2bmhweEV1SWJwS1lZTnZHYk1SZFZzWWgvaklBdXluS0NBRDhvT3F4SW1aN24yb21jZjlLQ0JaT0dudDBFenNaUHZNU1JqdUFISDZHSkxRRkJVbzVqYkt1SkRMb0FQT1Q4M3prWnp0ZUc3dVhmamxoNWVvUldzNU42dyt3dHAiLCJtYWMiOiJhM2I2ZDEwODVhMjAxMTQ1ZmQ3N2YwOGMyMTQ2MDVkOWZkYTE1ZTkwYTVmYWQ3OWUwZjdhYTcwMWFhN2IzYTg0IiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 18:02:09 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkYxL2d3bUdTcWVVU3BEMVFVZ3AxYXc9PSIsInZhbHVlIjoicEM2T2FteTNieWp6NXlHY2JjbGUzbTl6aUY1UU1uRzhFMEtyc2wxRklvNlVGdEZLaEwwdU5sZVhlZU1ORHFuZUxTUEhlSmZEODdoYkd1K0lHOFBCeUtCa29QdEExbWhXWVBNUXY3Y0lETDNzU2tMT1dhbXgrakJKV2h6bzhnOGRkeDV2NWd3QllrKy8xREdOQ3dlZ2V4NzhUbEcxK3E2Q0JzTGFqN3I0UEprUXFUNDQ3eVpOSlVacXJRcGNiU21Vem1uVFBzUWhOSnUxN09qaTBKMDBiZTlNTW03c1orMFJSajlqbDFLZUppK254bGF6QWtkWG5WYmFpaXFudENmUGxVbXVONkRJWWxkZmpJMUEzMjRUNlVWcVhqTkV0MjNLOGpZWU5SZmhyaFNtbWhkejk1NXYrYlRVOTVNQnJmZVMvcUZIa29mQy9ZMkFMVHZlS3VobmZObDREeGFwNVlPc09UNU5RRDlHSS9Ua21meVVWU2E1NVc1cFVCSWpweFdMSmswZTJIa1Y2Z3YwUnV0WjRYL2xTcE9TanRHcHpUa1E0RUtvUVRLYWE4YXNZa2wrN0JyMWh0aTh1SGpGWHBBcUVZaHBab3BDb1NYK2JYYUVhSlNrVUZMRkRPZkJNSlhpSXQ3alNBNDVoSy9kMmhwOWM0ckM0L0poR01mQTZWQ2siLCJtYWMiOiIwMzkxODdmNGY4OThkOWYyYjQzODdkYTQ3YzMxNDM0YzRkZDQ4ZDAxYzE2YTc2MTRiYjdlOTc2NWYxMDhiMDU0IiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 18:02:09 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im9TVDlEOHZOT2JOQmMwUS9ZMHVjV3c9PSIsInZhbHVlIjoiK3lyeUg4dXByMDlYR2dsWXJXSzRUenA3dmZaaWdaVjNIRmNKSy8xYUpoZ01Eai9oWXg5VDg4cC90aC9kNVJMM29zNkpiTjl2WGc0WnppQVlQMHh5L2xsUEU4aDVxS254NERVM1lGM09BUFNncW4yQjBnMnVpSFMyZ00yakdHQjdHSFIrd1JNNDV4d1lBUVRISERDWkx4Mm5qbGVJZDJZWFBZS1I3S2tlSXU5bmVmeFJhVmlHSmIyNVBwNURkLzh6WExqbDlrWEFyYk9FdjdUSG1BeHgxZTg3bUJ5WEtZQkpHZW0yb3NiOTl1SGxUaVFEK3V0V3REWmV2MEhyRHZ0RmNuMWhuOGZuQWV3STkyZlVDMG11VEF0OHROUzRsSmdoRnRHckZxMERmYXNLYzFrcWhXM3pwdjZDQzM3dkltNXQ3eC9JQVVPcEhXVkhVWEp1ejNtK3pmQWRNdXVxdGxxUzlGMDBUcWkrVmgvaSsvVTNCSFc1WFhieC9WZnVIV291RkU2bmhweEV1SWJwS1lZTnZHYk1SZFZzWWgvaklBdXluS0NBRDhvT3F4SW1aN24yb21jZjlLQ0JaT0dudDBFenNaUHZNU1JqdUFISDZHSkxRRkJVbzVqYkt1SkRMb0FQT1Q4M3prWnp0ZUc3dVhmamxoNWVvUldzNU42dyt3dHAiLCJtYWMiOiJhM2I2ZDEwODVhMjAxMTQ1ZmQ3N2YwOGMyMTQ2MDVkOWZkYTE1ZTkwYTVmYWQ3OWUwZjdhYTcwMWFhN2IzYTg0IiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 18:02:09 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkYxL2d3bUdTcWVVU3BEMVFVZ3AxYXc9PSIsInZhbHVlIjoicEM2T2FteTNieWp6NXlHY2JjbGUzbTl6aUY1UU1uRzhFMEtyc2wxRklvNlVGdEZLaEwwdU5sZVhlZU1ORHFuZUxTUEhlSmZEODdoYkd1K0lHOFBCeUtCa29QdEExbWhXWVBNUXY3Y0lETDNzU2tMT1dhbXgrakJKV2h6bzhnOGRkeDV2NWd3QllrKy8xREdOQ3dlZ2V4NzhUbEcxK3E2Q0JzTGFqN3I0UEprUXFUNDQ3eVpOSlVacXJRcGNiU21Vem1uVFBzUWhOSnUxN09qaTBKMDBiZTlNTW03c1orMFJSajlqbDFLZUppK254bGF6QWtkWG5WYmFpaXFudENmUGxVbXVONkRJWWxkZmpJMUEzMjRUNlVWcVhqTkV0MjNLOGpZWU5SZmhyaFNtbWhkejk1NXYrYlRVOTVNQnJmZVMvcUZIa29mQy9ZMkFMVHZlS3VobmZObDREeGFwNVlPc09UNU5RRDlHSS9Ua21meVVWU2E1NVc1cFVCSWpweFdMSmswZTJIa1Y2Z3YwUnV0WjRYL2xTcE9TanRHcHpUa1E0RUtvUVRLYWE4YXNZa2wrN0JyMWh0aTh1SGpGWHBBcUVZaHBab3BDb1NYK2JYYUVhSlNrVUZMRkRPZkJNSlhpSXQ3alNBNDVoSy9kMmhwOWM0ckM0L0poR01mQTZWQ2siLCJtYWMiOiIwMzkxODdmNGY4OThkOWYyYjQzODdkYTQ3YzMxNDM0YzRkZDQ4ZDAxYzE2YTc2MTRiYjdlOTc2NWYxMDhiMDU0IiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 18:02:09 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-49605287\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1406296495 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">jEiKRXRaeQw7kpqtZmXNWu3bpmEnaxKAnNI8mVhc</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>17</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1406296495\", {\"maxDepth\":0})</script>\n"}}