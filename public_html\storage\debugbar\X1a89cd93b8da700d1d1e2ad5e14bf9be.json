{"__meta": {"id": "X1a89cd93b8da700d1d1e2ad5e14bf9be", "datetime": "2025-06-26 15:59:38", "utime": **********.376825, "method": "GET", "uri": "/login", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750953577.892996, "end": **********.376843, "duration": 0.48384690284729004, "duration_str": "484ms", "measures": [{"label": "Booting", "start": 1750953577.892996, "relative_start": 0, "end": **********.229566, "relative_end": **********.229566, "duration": 0.33657002449035645, "duration_str": "337ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.229575, "relative_start": 0.3365788459777832, "end": **********.376845, "relative_end": 1.9073486328125e-06, "duration": 0.14726996421813965, "duration_str": "147ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46563624, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 4, "templates": [{"name": "1x auth.login", "param_count": null, "params": [], "start": **********.297704, "type": "blade", "hash": "bladeC:\\laragon\\www\\ERPcopy\\public_html\\resources\\views/auth/login.blade.phpauth.login", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fresources%2Fviews%2Fauth%2Flogin.blade.php&line=1", "ajax": false, "filename": "login.blade.php", "line": "?"}, "render_count": 1, "name_original": "auth.login"}, {"name": "1x layouts.auth", "param_count": null, "params": [], "start": **********.306013, "type": "blade", "hash": "bladeC:\\laragon\\www\\ERPcopy\\public_html\\resources\\views/layouts/auth.blade.phplayouts.auth", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fresources%2Fviews%2Flayouts%2Fauth.blade.php&line=1", "ajax": false, "filename": "auth.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.auth"}, {"name": "1x landingpage::layouts.buttons", "param_count": null, "params": [], "start": **********.328926, "type": "blade", "hash": "bladeC:\\laragon\\www\\ERPcopy\\public_html\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.phplandingpage::layouts.buttons", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2FModules%2FLandingPage%2FResources%2Fviews%2Flayouts%2Fbuttons.blade.php&line=1", "ajax": false, "filename": "buttons.blade.php", "line": "?"}, "render_count": 1, "name_original": "landingpage::layouts.buttons"}, {"name": "1x layouts.cookie_consent", "param_count": null, "params": [], "start": **********.331242, "type": "blade", "hash": "bladeC:\\laragon\\www\\ERPcopy\\public_html\\resources\\views/layouts/cookie_consent.blade.phplayouts.cookie_consent", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fresources%2Fviews%2Flayouts%2Fcookie_consent.blade.php&line=1", "ajax": false, "filename": "cookie_consent.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.cookie_consent"}]}, "route": {"uri": "GET login/{lang?}", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@showLoginForm", "namespace": null, "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=344\" onclick=\"\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:344-359</a>"}, "queries": {"nb_statements": 9, "nb_failed_statements": 0, "accumulated_duration": 0.03421, "accumulated_duration_str": "34.21ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 555}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 348}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.254717, "duration": 0.0275, "duration_str": "27.5ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 80.386}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'kdmkjkqknb' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 537}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.284269, "duration": 0.0029100000000000003, "duration_str": "2.91ms", "memory": 0, "memory_str": null, "filename": "Utility.php:537", "source": "app/Models/Utility.php:537", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUtility.php&line=537", "ajax": false, "filename": "Utility.php", "line": "537"}, "connection": "kdmkjkqknb", "start_percent": 80.386, "width_percent": 8.506}, {"sql": "select `full_name`, `code` from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 543}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.289297, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "Utility.php:543", "source": "app/Models/Utility.php:543", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUtility.php&line=543", "ajax": false, "filename": "Utility.php", "line": "543"}, "connection": "kdmkjkqknb", "start_percent": 88.892, "width_percent": 0.877}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 4748}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 4682}, {"index": 15, "namespace": "view", "name": "auth.login", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\resources\\views/auth/login.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.298949, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4748", "source": "app/Models/Utility.php:4748", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUtility.php&line=4748", "ajax": false, "filename": "Utility.php", "line": "4748"}, "connection": "kdmkjkqknb", "start_percent": 89.769, "width_percent": 2.777}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 4748}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 4682}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\resources\\views/layouts/auth.blade.php", "line": 10}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.307163, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4748", "source": "app/Models/Utility.php:4748", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUtility.php&line=4748", "ajax": false, "filename": "Utility.php", "line": "4748"}, "connection": "kdmkjkqknb", "start_percent": 92.546, "width_percent": 1.929}, {"sql": "select * from `users` where `type` = 'super admin' limit 1", "type": "query", "params": [], "bindings": ["super admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 4113}, {"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 4155}, {"index": 18, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.320571, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4113", "source": "app/Models/Utility.php:4113", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUtility.php&line=4113", "ajax": false, "filename": "Utility.php", "line": "4113"}, "connection": "kdmkjkqknb", "start_percent": 94.475, "width_percent": 2.222}, {"sql": "select `value`, `name` from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 4114}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 4155}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.323862, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4114", "source": "app/Models/Utility.php:4114", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUtility.php&line=4114", "ajax": false, "filename": "Utility.php", "line": "4114"}, "connection": "kdmkjkqknb", "start_percent": 96.697, "width_percent": 1.403}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 4748}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 4682}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\resources\\views/layouts/auth.blade.php", "line": 39}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.32584, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4748", "source": "app/Models/Utility.php:4748", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUtility.php&line=4748", "ajax": false, "filename": "Utility.php", "line": "4748"}, "connection": "kdmkjkqknb", "start_percent": 98.1, "width_percent": 1.052}, {"sql": "select * from `landing_page_settings`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "Modules/LandingPage/Entities/LandingPageSetting.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\Modules\\LandingPage\\Entities\\LandingPageSetting.php", "line": 27}, {"index": 19, "namespace": "view", "name": "landingpage::layouts.buttons", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.329808, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "LandingPageSetting.php:27", "source": "Modules/LandingPage/Entities/LandingPageSetting.php:27", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2FModules%2FLandingPage%2FEntities%2FLandingPageSetting.php&line=27", "ajax": false, "filename": "LandingPageSetting.php", "line": "27"}, "connection": "kdmkjkqknb", "start_percent": 99.152, "width_percent": 0.848}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "0ps0kU08Y9VObrS8MGH1isQjRNeK7Gn2ObkN9gvL", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/login\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-80736125 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-80736125\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1888517404 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1888517404\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1124548435 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1124548435\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1842 characters\">_clck=1lqc4d8%7C2%7Cfx3%7C0%7C2003; _clsk=1h4itwx%7C1750953572032%7C1%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjhJWDlkTHQ5QWgrcTBDdmtTMWt2SVE9PSIsInZhbHVlIjoiZUN5Q293dExvNVJPRjFYaHdlTWxUL0NEditON2JJMmFheVh3QXFka0JxUEFOZXVNcTJvOXF3NFY4eXg1QklEQmYxbVo0WEZTT3oyamo5akVjQWNna00rVkxDYmNRM1orZkxoZnlGcEpBRVhtSTQxVm5KVDRJTDVPWWNzUmZra2NydWo3UnBjMGxjM2ltZk1ac2FGRWdnZEZjb1Q2cmo4anV0cVd2eHBwcmpLMlNIY0cyT084WWZBeFY3MkpyV1ByNlRIcFp1SDdlOG9qSTFidElDdTdxOGtQMGhCNC85RmQzL0R5MnVKWURBeWxVUWlzbFo3bXRlaElFSzVadjJocUdqYjJsa1h2Zm4rZTZqenJXVUZHR1hwNWtzQnNWQ2ZsZlE5WDluNFBYZnZFbE9BeHZ4ajc4WXFTR1FWa2tDR096QkpxTHFDWTNQN3Iydi9DTEdscm01bUp5YWxMb0kwcmlubkFNQUV3M29paXJUV2o0Qm5vUGVIK2JXd2Z3eW5UaFIyay9Xa1Zldy9DanlXUFMrMTBvOG15amZNbjBUd1l0M0RoZU42dTl2WnlLSzd2MHZzaTVhb2ErdEIwSjN1L2JvK0luankrLzgwL0R0Uy8rVndEMTVkd3pMbVk0cnFxc0xUME9YMnJJQ1IrcDRKaUMrc09DY1p5aVhaNnJvVHMiLCJtYWMiOiI4YjdlOTRjODQxMjAxZDc4MDM0NzYxNzBhMjFlNTJlYWIyNThhNjlhZjA2MDhmNTU0OWNlZDQ2ODYwM2I3MjQwIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InFDWnRiK0E3NW5Nd25YSFZKc1dqL0E9PSIsInZhbHVlIjoiTTJNL2JZNmRaRThRN0RDQ2Nkd1Q0YWVma1JPY2haM3RZVFVSYUtwL3pabHZZbmVtL09CaFNwQThWZmlxYnpIeEs1SysyVlZ5clA4S0VBUkk4RUhXRU5FTzUxU29ZN3BDYXZucXJkUTJzOWR3dlpZU2JoeWN3RzIzdTQzQi9SU3diNVBDSjFzUks5elJVV1RsWFNaTmRNcS9XZm5uS1psT3Z6TU5mUjdoUEFneXB3a3QwT25JNy9nbk1qTVJmYWRVTVV3K3NzWk5HQzcvMmJ0YjJwY1d4cjhJY0lnajc4RDhjMkVadVE4QTdmZmFXSVRWeEdRai9Lc21jQ0NXVFh6bm5MNHlQaUtHUWFnVUNleWlZUlVLd3VuZ3VoZXgrenArUFR5R0FCc01hV3phY0RyOGxNSHZnWXc5cUFJSjVpUDhJTGdDVjVYOGlHd2RSeVlheFRiWExGbEhlZFhVa3Z1VjJqRHBVOTFtWjA3d2N5anAxeDNMS1hYV2dkaFRxaVVKOWtlb0UrUFhOUzFycjhOSGY2SitPVUphYldZKzhhcEdiQVNBSHFzOCtyQXlTM3IzTWpQOHJsc0R0MjRDSWZGSUxaM2dwekljZ0c1Ly85bnh5bXRMb3lwa3duWURjYjBRODFWUVgrRHV6Mnlhckc5eStTN0ZQUExta0c3TGx3TEkiLCJtYWMiOiI0OTY3YjM1NzU4ODRjZDM1OTZmMmFjYzg2M2EwZDY3MmZlN2VhN2E3MWE4MjVlY2Q5ZmY4MTgyZDk5MzliYjFlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0ps0kU08Y9VObrS8MGH1isQjRNeK7Gn2ObkN9gvL</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xUs158fce27RRZxBwxL6eongJCrFGAQz1wiETmFD</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-869389667 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 15:59:38 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImhOaGROM05scGNwbFo4djd0cUphZkE9PSIsInZhbHVlIjoidGZFeVJUejhMcVREOWpTVlZqU3NncFJSY29RanFwbWo3VG9BM2FkYUU3cjRlTTJCOUNSRlVhT1BxdmJYQzdkZ0RaTm9jQ01IRklaRW5YeHltZkJYVWMrNm5KTktralJmNjVkbjVrb1gyYVg0WFlrTXppMGZrQmlURnFnOWNmcW9NYjdkbGozczNlRkJaTEI4WVllRnl5V3ArYTV0emNtcjZTQzZONWtLeE5nQkdMUDg5bjB0NXo5TEM4SktNdUs2YTZnMEJRN2ZORGVxeWdyZE93L1labkJoUUg1Vk1qZHM5OGI3U0VBNTJEbFV6anhFa0VsbndTU1lkUy9EaE5zK3Jld1cwVmEyZmhER1JuUGtWNHdRbUxkTXBLSEJrUy9QT2tzNm1nSGUzWDVzS1JPdzhRRHhvV3o3Y1luRlVpOC9NL21jTlE2NCtDbDdGcFdQUExCVjlhdGc4cm9COGpMU2tEL0RobWUzaExUdjVkU2paM29lZjRDS1ZKazF3QW1ZR0hzOHlwM1ZQbnZ1eElHUlRaTTJOaWp4aGwxV056Tk5OSjh0VzIycDhTYUVCYnVaUG16VGR6NWlicjBiYXBaVTJCUjdSZDVndnZsMTBzM254MEtZdUgvWFBNRXNyV1NqZ0pNbUdWa0Nvcnd6QkxLNWR4amtZbG9UZER6MWFIbG8iLCJtYWMiOiJiNGI2MWZiN2M5NjVhN2ViM2I0OWMxNWNmNDUzYzVmOGViMDYxMjBjMGY3MzM3ZGI2NjhkM2QwYzg0M2ZhYTAxIiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 17:59:38 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkFRMTZDRVJSZFBmYlEraFhHWnc1RkE9PSIsInZhbHVlIjoiem9BT3VEZmhYc251ak9YN1hnazI5N2U1aWVGSFNQOUlDaUFvUDBMUnpveWtqV3MwVEVLMEZtRStZeDloK21VQnA0MnU4RXlDTTdsM3NaZHZ5R2ZlL014c0J2UEhCT2JLVmZUa05IcXg3L09YMmtDYjFtMW9HdGFwc2tsRlhzRGtiYjlxcEtrTXhwakFOZU5NRGpWVFh5ckNqWGZZTjhGQ0RuaXFmd3pDZUtGMTlnTHIzUTJqb0lUZG1yQU1tK2ZSbXFzWHRxelozOFR3TXdYc3phRWlnQlZ6VTIrWnVoLytUVmlLc0RpWmhmQU5tL0doU0U0S0VSL1lTY3hhQWhpb0pRZWNNbEllMCs1Z2kxNWdFbFMwa0wrSHo3ODgrM1ZKTklBUnl5cFJicUVzQnd1am1QZ2JNL0NEaWdYdTVkcm00ZlJEd3M2bDVDTVFTN0RoR2MvV0pwcjlVN1dpK0dhZmlGaUxGbkM4Z1VQVTQvTmJLVndtWUVMcjhtMXRLVStKVzh2am9vNlZ0TmlkeW9xQ1B6eWxPWEJDTi9TanVkalhzQkdjMWtUQk9ncXg2NE5uSmNKb3FVM1BheWF2VnV4T1NQenorTC8vajZMcFNORU1xTmFMMkRZeUJaRFdydDlMNlVVNmo5ZTRTODM2WklMVzcxa0ZQOFpxN0N1STRESjgiLCJtYWMiOiJjMzhhYzgyMTdiMGU4NjhmNTk0ZTBkZTA2OWU4OTI3MDMwMTNlZGQ3MzBmYWY3Y2RkMGI4YzBhZjQ5NGY3OWI2IiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 17:59:38 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImhOaGROM05scGNwbFo4djd0cUphZkE9PSIsInZhbHVlIjoidGZFeVJUejhMcVREOWpTVlZqU3NncFJSY29RanFwbWo3VG9BM2FkYUU3cjRlTTJCOUNSRlVhT1BxdmJYQzdkZ0RaTm9jQ01IRklaRW5YeHltZkJYVWMrNm5KTktralJmNjVkbjVrb1gyYVg0WFlrTXppMGZrQmlURnFnOWNmcW9NYjdkbGozczNlRkJaTEI4WVllRnl5V3ArYTV0emNtcjZTQzZONWtLeE5nQkdMUDg5bjB0NXo5TEM4SktNdUs2YTZnMEJRN2ZORGVxeWdyZE93L1labkJoUUg1Vk1qZHM5OGI3U0VBNTJEbFV6anhFa0VsbndTU1lkUy9EaE5zK3Jld1cwVmEyZmhER1JuUGtWNHdRbUxkTXBLSEJrUy9QT2tzNm1nSGUzWDVzS1JPdzhRRHhvV3o3Y1luRlVpOC9NL21jTlE2NCtDbDdGcFdQUExCVjlhdGc4cm9COGpMU2tEL0RobWUzaExUdjVkU2paM29lZjRDS1ZKazF3QW1ZR0hzOHlwM1ZQbnZ1eElHUlRaTTJOaWp4aGwxV056Tk5OSjh0VzIycDhTYUVCYnVaUG16VGR6NWlicjBiYXBaVTJCUjdSZDVndnZsMTBzM254MEtZdUgvWFBNRXNyV1NqZ0pNbUdWa0Nvcnd6QkxLNWR4amtZbG9UZER6MWFIbG8iLCJtYWMiOiJiNGI2MWZiN2M5NjVhN2ViM2I0OWMxNWNmNDUzYzVmOGViMDYxMjBjMGY3MzM3ZGI2NjhkM2QwYzg0M2ZhYTAxIiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 17:59:38 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkFRMTZDRVJSZFBmYlEraFhHWnc1RkE9PSIsInZhbHVlIjoiem9BT3VEZmhYc251ak9YN1hnazI5N2U1aWVGSFNQOUlDaUFvUDBMUnpveWtqV3MwVEVLMEZtRStZeDloK21VQnA0MnU4RXlDTTdsM3NaZHZ5R2ZlL014c0J2UEhCT2JLVmZUa05IcXg3L09YMmtDYjFtMW9HdGFwc2tsRlhzRGtiYjlxcEtrTXhwakFOZU5NRGpWVFh5ckNqWGZZTjhGQ0RuaXFmd3pDZUtGMTlnTHIzUTJqb0lUZG1yQU1tK2ZSbXFzWHRxelozOFR3TXdYc3phRWlnQlZ6VTIrWnVoLytUVmlLc0RpWmhmQU5tL0doU0U0S0VSL1lTY3hhQWhpb0pRZWNNbEllMCs1Z2kxNWdFbFMwa0wrSHo3ODgrM1ZKTklBUnl5cFJicUVzQnd1am1QZ2JNL0NEaWdYdTVkcm00ZlJEd3M2bDVDTVFTN0RoR2MvV0pwcjlVN1dpK0dhZmlGaUxGbkM4Z1VQVTQvTmJLVndtWUVMcjhtMXRLVStKVzh2am9vNlZ0TmlkeW9xQ1B6eWxPWEJDTi9TanVkalhzQkdjMWtUQk9ncXg2NE5uSmNKb3FVM1BheWF2VnV4T1NQenorTC8vajZMcFNORU1xTmFMMkRZeUJaRFdydDlMNlVVNmo5ZTRTODM2WklMVzcxa0ZQOFpxN0N1STRESjgiLCJtYWMiOiJjMzhhYzgyMTdiMGU4NjhmNTk0ZTBkZTA2OWU4OTI3MDMwMTNlZGQ3MzBmYWY3Y2RkMGI4YzBhZjQ5NGY3OWI2IiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 17:59:38 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-869389667\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1983401577 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0ps0kU08Y9VObrS8MGH1isQjRNeK7Gn2ObkN9gvL</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1983401577\", {\"maxDepth\":0})</script>\n"}}