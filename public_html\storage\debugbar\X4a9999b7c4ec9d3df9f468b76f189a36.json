{"__meta": {"id": "X4a9999b7c4ec9d3df9f468b76f189a36", "datetime": "2025-06-26 15:59:46", "utime": **********.062089, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.693268, "end": **********.062104, "duration": 0.****************, "duration_str": "369ms", "measures": [{"label": "Booting", "start": **********.693268, "relative_start": 0, "end": **********.007282, "relative_end": **********.007282, "duration": 0.****************, "duration_str": "314ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.007291, "relative_start": 0.****************, "end": **********.062105, "relative_end": 9.5367431640625e-07, "duration": 0.*****************, "duration_str": "54.81ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00297, "accumulated_duration_str": "2.97ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.032779, "duration": 0.00157, "duration_str": "1.57ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 52.862}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.041805, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 52.862, "width_percent": 10.774}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.050569, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "kdmkjkqknb", "start_percent": 63.636, "width_percent": 36.364}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "ET1WIQmDwH5DmIalEePkmJkdqregxR405rAMGD3B", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1842 characters\">_clck=1lqc4d8%7C2%7Cfx3%7C0%7C2003; _clsk=1h4itwx%7C1750953578911%7C2%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Imk2c2cvL1NtQUdOS010NXRYbFEvTGc9PSIsInZhbHVlIjoiNEk3WUdmM2VtNVE2eHJmM1I1amU1MXJqZi82dGFHdjFVajVQMHdnZC9adWVRYnZJdHpZVXNWdDBxdjhHN0JUeVlyMUlaZXIzUnU1RURBUEN5dFBxVXZHK1QrVVAvUjlweDlJQUJCbTIwallGcjl3bG8vV2tnQ2xybzJrZGZIZzJQUDJRdnlIZjJIMmJra2pBM1ZOWTQzcVlWMWJFczBOWkwyRUJJNW9Md1JISVE1dXVEL0pkUXg1YVNlZXN5eXVSOG90OHFwa200UVZuZUZReW92aE9uQkg1V000WjBsNkJtckV3S21wMUNUQ21zRTUwNmh6Y3oyWlFGT3hKeXdEWDhXU3dSRVozYnFDTFUwQTVCbENHR1lSOWpEZDN6MEt5R24rZ0IzNEtyelo5a0ZSTGMxbTNiemtKczFyVGg1UWZ1VWlHcytuTjk3N21hUUFwR3JzY25HSTJaUzAwdkJmY3EzUTNtbVN2YnB0dk41eFNEdXRSeHdLbWxWMGo5eVdJTnhkbDQxS2VJWEt1bm9wUlNkVDluaTVGZkpINjZEdjhjUTN0a0tENGlkWVRhMVRSTDByblBySTRwQ1pQcGUvOVVzNHpMYjZwVld2MzdoK3ozQzZZWGVGckdhSnF1bnJBNFE1TTdSVHZXVUpyZHdQZU5LamE1cTdqWFdMWHhLYU0iLCJtYWMiOiI1YzAyMTQyMjVmY2MzY2U4NTU1ZjJhOWQ1ZjQ2N2M0ODBiMGY4YzNlYTFjMTYyZWEzZDk1NGI5YmFlNDZjNzVkIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlYxQVdsYzZid0k1bGEyUlhiVkJtV3c9PSIsInZhbHVlIjoiMk5XSStIU2JoVGtYSS81WWNPemRqNmI3R2pVN2FXWkEvM25nbzdqc1ZURWhZT0F5YjJKN2s3TWpPSWtYcDhiTnJrT1NQMHJzRUpHeWZkNGxmQnpiV05xQTFjamkwRFBGMUZNSlBNMkNJMFJhdVJzMjlubXprRVdPcnBiOXhoN29YQVpPU2RzbTk1V0FRRU4rRzRTdDNGemF5dGdRTWVDb1hSNFl4QjVBenV3SmdSdlkvU3hLYmJCd2V2THZWcXhSNTdPdk5Ob05ranAyekl2NzZuRkhQdFJDOFRIdGc3SDNaQXZ5eU5Ea1lWOC9Fc1dzZ25DQnBBd2VMTUYreHN4N0NTWGdIeUphTlJNaURKOUtVUnFZT0FMelZlZnUzTlZRMTlmMVVES0UwZFUyS0M5TFpXcHJIeFpXOFVaRGhCcmREUEl0bWJGTFlzVDJnYnNzeEZHS0YvNll1d252MnVkUVd1dm1vbkNlVVNFU1VKMDlTRnBNTGhPUG81a2pWY2M2cFkxMWJlOHYxOTVrQnd3UjBqcGs2NTJpYnp2ME03ZkJzM1FJMWtYeXpyM09FUDFZMVpWYk5ZbzJMb242NzAzVlVVOGx0cVczcVJGOEZDa3FHb25pU2s0SlpSejU1cVY2TWhJR0o0dmRFM3dtNkl1ZFM0TGl3RkUrL2xQdEdDQnAiLCJtYWMiOiI2NjBiYzZhYWMxY2FiYjVjMGVmMzQ2YTFhNTUzNzJmMTdhYzgyMDRhMmRjMzM3ZGQ1MzdiOTA4ZmQ4NWU5ZDc1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ET1WIQmDwH5DmIalEePkmJkdqregxR405rAMGD3B</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OKKbyGk1l1oGFNUe7gxioQDH0z4ATppFVbVTtLek</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-666103449 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 15:59:46 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InlUZlFkMzF2YmlqbUN3QWEwYXpJNWc9PSIsInZhbHVlIjoibi9WZ3d0WXNIRFRoK2JxK3J4OUtlemZRcW9ESE9yczZCODI4YXhNNmh5YVhFc1ZPc1hrUnlMWXZFMmMvN2lXVzl0U29JZUNKS0FMUUlIcitRcWtwenBmNHFlcGQxU3pDMWczOGRCMUl3R2pXb3E3MjhzNjMvNW9tUzk2NEZZeExlSWNUS3RUeTJsRHlCUjI1cXRINFQvSW5PWkFXZzBYd1JqdzUzbFZQV3lXN3FVVE5vNzQ5UDhMS1lXSWorYU50UzZxakk0d1hxQ1lYMmFxaDRsSlhoUE5RZElGTDBQOFpzMjBncHp3VlVINytDcVdaL0hyS014ekY1bTI0NEhaNDNSSll4NHFvVE9GZHNDZGppZkt2K0NvRysvM3FvWUpQa2VXMmozVS80MWlvVDV2ckJVMVpXNkYyeWk3cnZ6SmxOckxDcmtkb2FTTldMclZwWVZjaGJoMVorM0w4MWk3M0ZCU2o0NS9ON2lwTUsrVjFlZGt4V0hMVVdoYlpjQS8zaXc3YkhwRGU1WFFJVWMxNVJKUDcyZXNOS1d1L0daUmxEbno0Y0pBeE9pdDF3R3B3RWNiYmxCUFJQZ0tMQ2NlSElTWGVlamVPM0dyVjR0bnNxRWR2MldEY0w4MjBvNXVDQzR3NDlyV3RJNUlISHZtMGh4eUFnVmpielVaZUlrTWgiLCJtYWMiOiIzYWVhZGJjYjgwOTA3MzAwYzFhNzBiN2NhN2Q4YzUwMThlMzU3N2Q2N2JkY2M4N2RkZmI0N2JiNGQxOWYxMzA0IiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 17:59:46 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ild1U0ZvekZrdXBaRDJmd0FGeEprM3c9PSIsInZhbHVlIjoiOFdJcXpJWU9GeG13N1dhY3lySjdYQkNrQ2VTakJxUGhVSDQrM1I1YVJrYk5NY0lwc0JhQnNWak1kanJIZFo2NmxEclV6ZVY2T05saEI4ZEdBYWh1Z0lLSGZ4WnVHTUJQeGY2aml3MzJOUkhVbG9NNjJ6cktzQTVnT21ON0FsRElsVmZ5OFBkSU9zeFVabnRMSzU3MFhkYjNnTXRNZ3RlQXdyZHRzVWRjVWpjc0ZhVnZLSHM1VGcybXBwQldiYzBmWWhkc1BNMXVIdC9OcUxCZnV1aDRpVEpUdUFIUERFQ0wzSXB1V2FvbHJ3K2hKQjl6L2d4emJqS2gzSFZacjhWWHFoMzZub05pNnIydkg0ZUFvdVZJNVZhVUc4aWRTaE5uZUVGZ0xSNmtiNFRQbzQ2Q3lzcW9lTTQzeVNBOUlZOHU3OUxydnoyZnk4NWlXUU8wQmlCYUR6QUpPOUZaWm56anNQdWFYdTVtVDVyMXRxbS9HOG1Jak5tdkJZWS9rQzM0T2ZSczJZelJSWG5JdjdiSGQ3RXdHaTBIa1VpV2Nod2c4bW9sUk14U3plSCtWcDRVOXh1YzFyVUE2amYvQ0VJamFrWG4vT1FQd244ZHBsMGhEV1NoZWJwTFVzb3ltRFVQUEY1WG5hVGE1a0RVV2doQjNaMlJvaFJsOGZTczZMcjgiLCJtYWMiOiIxMzQ5NWJhYmQyMGE3NzFiOTU2YjY3YzM0MGE0NmFiYTM1ZmIxNzJkZjNkYmFkNWFhZmM3YmIyYzQ5MDBhYWE5IiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 17:59:46 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InlUZlFkMzF2YmlqbUN3QWEwYXpJNWc9PSIsInZhbHVlIjoibi9WZ3d0WXNIRFRoK2JxK3J4OUtlemZRcW9ESE9yczZCODI4YXhNNmh5YVhFc1ZPc1hrUnlMWXZFMmMvN2lXVzl0U29JZUNKS0FMUUlIcitRcWtwenBmNHFlcGQxU3pDMWczOGRCMUl3R2pXb3E3MjhzNjMvNW9tUzk2NEZZeExlSWNUS3RUeTJsRHlCUjI1cXRINFQvSW5PWkFXZzBYd1JqdzUzbFZQV3lXN3FVVE5vNzQ5UDhMS1lXSWorYU50UzZxakk0d1hxQ1lYMmFxaDRsSlhoUE5RZElGTDBQOFpzMjBncHp3VlVINytDcVdaL0hyS014ekY1bTI0NEhaNDNSSll4NHFvVE9GZHNDZGppZkt2K0NvRysvM3FvWUpQa2VXMmozVS80MWlvVDV2ckJVMVpXNkYyeWk3cnZ6SmxOckxDcmtkb2FTTldMclZwWVZjaGJoMVorM0w4MWk3M0ZCU2o0NS9ON2lwTUsrVjFlZGt4V0hMVVdoYlpjQS8zaXc3YkhwRGU1WFFJVWMxNVJKUDcyZXNOS1d1L0daUmxEbno0Y0pBeE9pdDF3R3B3RWNiYmxCUFJQZ0tMQ2NlSElTWGVlamVPM0dyVjR0bnNxRWR2MldEY0w4MjBvNXVDQzR3NDlyV3RJNUlISHZtMGh4eUFnVmpielVaZUlrTWgiLCJtYWMiOiIzYWVhZGJjYjgwOTA3MzAwYzFhNzBiN2NhN2Q4YzUwMThlMzU3N2Q2N2JkY2M4N2RkZmI0N2JiNGQxOWYxMzA0IiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 17:59:46 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ild1U0ZvekZrdXBaRDJmd0FGeEprM3c9PSIsInZhbHVlIjoiOFdJcXpJWU9GeG13N1dhY3lySjdYQkNrQ2VTakJxUGhVSDQrM1I1YVJrYk5NY0lwc0JhQnNWak1kanJIZFo2NmxEclV6ZVY2T05saEI4ZEdBYWh1Z0lLSGZ4WnVHTUJQeGY2aml3MzJOUkhVbG9NNjJ6cktzQTVnT21ON0FsRElsVmZ5OFBkSU9zeFVabnRMSzU3MFhkYjNnTXRNZ3RlQXdyZHRzVWRjVWpjc0ZhVnZLSHM1VGcybXBwQldiYzBmWWhkc1BNMXVIdC9OcUxCZnV1aDRpVEpUdUFIUERFQ0wzSXB1V2FvbHJ3K2hKQjl6L2d4emJqS2gzSFZacjhWWHFoMzZub05pNnIydkg0ZUFvdVZJNVZhVUc4aWRTaE5uZUVGZ0xSNmtiNFRQbzQ2Q3lzcW9lTTQzeVNBOUlZOHU3OUxydnoyZnk4NWlXUU8wQmlCYUR6QUpPOUZaWm56anNQdWFYdTVtVDVyMXRxbS9HOG1Jak5tdkJZWS9rQzM0T2ZSczJZelJSWG5JdjdiSGQ3RXdHaTBIa1VpV2Nod2c4bW9sUk14U3plSCtWcDRVOXh1YzFyVUE2amYvQ0VJamFrWG4vT1FQd244ZHBsMGhEV1NoZWJwTFVzb3ltRFVQUEY1WG5hVGE1a0RVV2doQjNaMlJvaFJsOGZTczZMcjgiLCJtYWMiOiIxMzQ5NWJhYmQyMGE3NzFiOTU2YjY3YzM0MGE0NmFiYTM1ZmIxNzJkZjNkYmFkNWFhZmM3YmIyYzQ5MDBhYWE5IiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 17:59:46 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-666103449\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-******** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ET1WIQmDwH5DmIalEePkmJkdqregxR405rAMGD3B</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-********\", {\"maxDepth\":0})</script>\n"}}