{"__meta": {"id": "X3c577d1c39a2b0481859726ddaf32970", "datetime": "2025-06-26 16:27:50", "utime": **********.530199, "method": "POST", "uri": "/warehouse-empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.108852, "end": **********.530216, "duration": 0.42136406898498535, "duration_str": "421ms", "measures": [{"label": "Booting", "start": **********.108852, "relative_start": 0, "end": **********.480499, "relative_end": **********.480499, "duration": 0.3716471195220947, "duration_str": "372ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.480508, "relative_start": 0.3716561794281006, "end": **********.530218, "relative_end": 1.9073486328125e-06, "duration": 0.04970979690551758, "duration_str": "49.71ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45880368, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST warehouse-empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@warehouseemptyCart", "namespace": null, "prefix": "", "where": [], "as": "warehouse-empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1627\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1627-1637</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00218, "accumulated_duration_str": "2.18ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 17 limit 1", "type": "query", "params": [], "bindings": ["17"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.512384, "duration": 0.00172, "duration_str": "1.72ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 78.899}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.522403, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 78.899, "width_percent": 21.101}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PKqZteC3IabV2rdCxsXBMLnTb72fzH4qdR2KdQSn", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "17"}, "request": {"path_info": "/warehouse-empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-298278230 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-298278230\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1892991333 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1892991333\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1905678642 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1905678642\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2052952149 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PKqZteC3IabV2rdCxsXBMLnTb72fzH4qdR2KdQSn</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1bowxa%7C2%7Cfx3%7C0%7C1999; _clsk=1osc4j2%7C1750953875247%7C2%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InBiWVVTYXFoVU96UXc5alJKSmFiN0E9PSIsInZhbHVlIjoiTWRXTjV5TElDeHowRkR5N3Q0bWIxWEJoME9obXdsWEdhaGNFbnJlMWMzczhQRWhZL1dnbW5RdjM5THRDbGJ2elFOamczRkE3clNuUzFjNGQ3V2Mxa0FpNGN4MlNqSXByVm9vTVFnMm1xWm8vQzYrVWRVOG9MOE1SUFhYSTdDd3NzT0FGdjZrTGxjQzFVbDFPSmFKQ3Jwb2NnNWpBdUpPYjdmZ0tFdyt6YmxhRzlpbTg5S1dJWVVQYW5RL3RBTmdJRVF6U2ZaZERZcldZOXlqenhNVzVVaHBObDdnaTViMisvL2dETjliSTVuaXc0RzJ2RURoeHljNFRNeVFBbk5wVkhveThGOE1MNVNxcEMrdTZiWjlBTDZxKy9XOUFuSUJJUFBzNU9rVndQUWQyRjdnM1hRV2w0ODhDdW9mQ3FwZFpZNS9DTmkveEd6UUJIYW1KSmNxNUZIR09jY0FuWmxodTVWYXBMQ2pvR3plVElGNUJTWWVqYWtMWHJMbzZZUlJmb1RqR0FOajA0QXh6YXhnTWJTd0l2K3ViL1lBdzZvZVp5VWEzWmdmSVcyZ3NWZ3g2UllmWHJ6ZVNZeTZhendDYUY1TGlCS3QydGxVSWg1eDBMSTZGbG1kQkhlTlZ3K2h0d3ZNazRsSWVPN0pqTDVQeElpTzdJaEVjZW40MzlwTHQiLCJtYWMiOiI0YzE4OGI4MjQ0YjE5OWMwZmQzNWE5ZTg1NmI4MzhlNTA0YjBhZTMxOTIyZTEyOTMzNzY3ZGUzYTg3ZjczNmYyIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImppUHlnQUdKVkRsbUZrenhWU1hjWUE9PSIsInZhbHVlIjoiZEF0ZXdlT0t4cjlWbnBoY3FRT1ExRDBmM3JaSlJlZzBhRi9BQW5hMSs3U0QvS0ZrOEF3UXcreklSVUlwUXFkaTFOUkdHVlhMWFFQRkNGL3Y3T1dnbUJlUGhqZ09Ydzk2ejIrZkZoNnF3d2toNGgweXVQNWtqVVRWMS9aTVhDSXZ3MGRoaCs3YTIxWndiSTVyZVBERmxzUU40QTFNSm0vWHZReS9CSW1lTWhkZFdyeG41TXlmVFEyTnlzVnlyR0VaU2FWRWJhcE05ekd3Q3duVnlHNDJ2ai82WUQyMlBDQXZmNVgzZXBoU0x1d2UrY1hlN3UzUHhobEhFOWxCbGtMS2ZEd2lNNHV3R1ZmNzFJVHVVbVd2YXlWbmNPNWFDNjZzVk1jaFNLUFpVVjRsL0RoU0JGTUtrYjBGMUd1Q1RXR1RDdEU2TEt1dTZIeU43bHpTY05ZcFdtRUZHd2ZJYzhlYjBKUzViOWlIdTc5U0QrUGVrL3dWWk9IdVRhbmNFb3A5Vk40aHdyVUgxbE5DbXR1UC9PSTFrRTJlc2JlVDFkZmdlcXpBRnoxdVU4TG9TeXlHY3NZTVFCelVHZCsyTkx1OXdKUDF5bGN3NHFsM2RNb3NwMWQvbVRPN3ZLaWFIcGQ4L1VaRHoyQkJjZ01INE1TaExiNW92K1A3aWdCZFFnNDMiLCJtYWMiOiI1NTA5ZGViNDc1OWQ1YzU0MGI4YTUyYmY0MjgwYzZkM2ViNjYwYmRiZmI0MzVmOWE2OTRhMjk5MTc3YmY3NDhhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2052952149\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1005548654 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PKqZteC3IabV2rdCxsXBMLnTb72fzH4qdR2KdQSn</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GUUMfjgJKgBqP5oaDcRP4Rfu0FM4k6V9zWwhkyoG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1005548654\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 16:27:50 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjNmWGJtK0M0aEpnaWNMSjdZb1lUd1E9PSIsInZhbHVlIjoiOS9hQ2RjcDRTcHJrUHJ5Wkk0TzBiaXpYMTRqUnZzWFBydUpZQWthYUY4U0pDclQ3SmtLUm5PM05YZVlmc2VyTklWMTBYcmRSYWh6TTZkTGZCL0Z0am9tQzFlVWxsZDhGZTJXaTJEQ1NrY2VpNFJpTkpkdVF6Y09acitDc3BYVncxWWhHOG05R3doVTRUMDhIU0dDRUNoYXRwSk9LSkoyUEdSVGdOekIrbWwyM3l0TVdSOVhpM0UrNXVpSjRIYksxOXEvWGtwUlVyZlNTNk8zelRpbldqS0pHVk5IV1lIbTlWWXUrSjdyVWIrTm1GcE5PbnV2dFRPU3NaV21GdkR2YzYxekxQQkl4S3RvYlRySnhlV1hGWld3ZllLNVUwZGd4aTRvaEJuN0FRUk9LWGhiZFIyZUtOakdERm5ZZUtzZU9iR0Njc1Fta3NXWU80V21GeVI3enZpdVpOTzJEQXlFb2xoY05GVVRVaTdWOUhSSDFzL0cvSGRKc1FySnZLSkR6dzU4UW9MWTZiSmRJbkhhMnVPTzgvci9CRE9ObWxhUURIMVZFZ2xOQVVhY0dtZ0E3RU5JNXBnRDVFc1R4V3JTMi9rYmFsdEFESVBLcWRSR2laT0tzUDh2cWNXeWFWcjZVcXcvVSs2OGdkS1hTek1Cb0I0TlRQZ3lMZWlkbEU1QWIiLCJtYWMiOiI5MmMzYWJjYWU0OGNkYjA1NjdmN2FhYjdmYmIxNDMzYzVlNDMwY2ZhNWFjNWU3MTYzY2I5YTQ2NGYwMDA4ZDZiIiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 18:27:50 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InpFdTFLMzJDcEVubnhHTitFTVBqK2c9PSIsInZhbHVlIjoiL0ZwV1hXMHp5T096RTlMdWYwM0VvNThhTHUwT2dpZHhITjB3U0Z2V0tMZnJvTUZZbCs3L2p4eVNGYzRaajd0anVZREVvYjR6REtlajBCUG43OXRtMERLeEJnQXJGSEJlM1pzM3NBcGJWemRrQ2dNQ24ramNHZXBqUHlaaWg4Q0NyUUJZamRLU3pmaXN0Y3A3YTMyREIzTW5jK01wSGZqWlBuUmlTWUQzdzZnL3dGdXZJcW1PaWM3Zmd0OU1KSEZlSW5ZSHZ0RWN5RTNPbWZzR2FJcTRjOTAzemFCcG1Xb0lKTE9CbW9EUldUcjlIa1dqeGFpcW81b0NpVkFWQitUNElLTHljbXBxQ1I4OVhuMWF4blI5V1lPY0JVazhIRkNVQ3loOVNHSzVVY05zZHRFQ0svS2MwRFdPVnAxVit3M1VBaVV4UkZxbHgrMHRrRnNiNGdqQWROYW5oSitYVUc1emZuOEt5eWUrM0RwWVc2QWFRR1g5aURjNUJQTUU3QVVlVjFhZG5MQXVlMG5ZTktiL01ZQUsrQkltUVkvQmxhUTRZQ1ptVnNhSmxYWkZhUVJBcFpoWmtRbW1XTlZVSEtqTVNnamJjZ3hseDZnQ2N5M0grQWVVdkZPNU1rSndzNTJQbUJpZTV2ZWg2WWVQQUh5b3c2bW96QU1hVEp0QUZXelEiLCJtYWMiOiI1MGEwZGFmM2Y0YzRmMjcxNjBiZmMwNjc0MzU5YjBkMzM2ZjAyOWI1M2I3M2Q0YjM2YTYzMzM3OWEyM2RmMGEwIiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 18:27:50 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjNmWGJtK0M0aEpnaWNMSjdZb1lUd1E9PSIsInZhbHVlIjoiOS9hQ2RjcDRTcHJrUHJ5Wkk0TzBiaXpYMTRqUnZzWFBydUpZQWthYUY4U0pDclQ3SmtLUm5PM05YZVlmc2VyTklWMTBYcmRSYWh6TTZkTGZCL0Z0am9tQzFlVWxsZDhGZTJXaTJEQ1NrY2VpNFJpTkpkdVF6Y09acitDc3BYVncxWWhHOG05R3doVTRUMDhIU0dDRUNoYXRwSk9LSkoyUEdSVGdOekIrbWwyM3l0TVdSOVhpM0UrNXVpSjRIYksxOXEvWGtwUlVyZlNTNk8zelRpbldqS0pHVk5IV1lIbTlWWXUrSjdyVWIrTm1GcE5PbnV2dFRPU3NaV21GdkR2YzYxekxQQkl4S3RvYlRySnhlV1hGWld3ZllLNVUwZGd4aTRvaEJuN0FRUk9LWGhiZFIyZUtOakdERm5ZZUtzZU9iR0Njc1Fta3NXWU80V21GeVI3enZpdVpOTzJEQXlFb2xoY05GVVRVaTdWOUhSSDFzL0cvSGRKc1FySnZLSkR6dzU4UW9MWTZiSmRJbkhhMnVPTzgvci9CRE9ObWxhUURIMVZFZ2xOQVVhY0dtZ0E3RU5JNXBnRDVFc1R4V3JTMi9rYmFsdEFESVBLcWRSR2laT0tzUDh2cWNXeWFWcjZVcXcvVSs2OGdkS1hTek1Cb0I0TlRQZ3lMZWlkbEU1QWIiLCJtYWMiOiI5MmMzYWJjYWU0OGNkYjA1NjdmN2FhYjdmYmIxNDMzYzVlNDMwY2ZhNWFjNWU3MTYzY2I5YTQ2NGYwMDA4ZDZiIiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 18:27:50 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InpFdTFLMzJDcEVubnhHTitFTVBqK2c9PSIsInZhbHVlIjoiL0ZwV1hXMHp5T096RTlMdWYwM0VvNThhTHUwT2dpZHhITjB3U0Z2V0tMZnJvTUZZbCs3L2p4eVNGYzRaajd0anVZREVvYjR6REtlajBCUG43OXRtMERLeEJnQXJGSEJlM1pzM3NBcGJWemRrQ2dNQ24ramNHZXBqUHlaaWg4Q0NyUUJZamRLU3pmaXN0Y3A3YTMyREIzTW5jK01wSGZqWlBuUmlTWUQzdzZnL3dGdXZJcW1PaWM3Zmd0OU1KSEZlSW5ZSHZ0RWN5RTNPbWZzR2FJcTRjOTAzemFCcG1Xb0lKTE9CbW9EUldUcjlIa1dqeGFpcW81b0NpVkFWQitUNElLTHljbXBxQ1I4OVhuMWF4blI5V1lPY0JVazhIRkNVQ3loOVNHSzVVY05zZHRFQ0svS2MwRFdPVnAxVit3M1VBaVV4UkZxbHgrMHRrRnNiNGdqQWROYW5oSitYVUc1emZuOEt5eWUrM0RwWVc2QWFRR1g5aURjNUJQTUU3QVVlVjFhZG5MQXVlMG5ZTktiL01ZQUsrQkltUVkvQmxhUTRZQ1ptVnNhSmxYWkZhUVJBcFpoWmtRbW1XTlZVSEtqTVNnamJjZ3hseDZnQ2N5M0grQWVVdkZPNU1rSndzNTJQbUJpZTV2ZWg2WWVQQUh5b3c2bW96QU1hVEp0QUZXelEiLCJtYWMiOiI1MGEwZGFmM2Y0YzRmMjcxNjBiZmMwNjc0MzU5YjBkMzM2ZjAyOWI1M2I3M2Q0YjM2YTYzMzM3OWEyM2RmMGEwIiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 18:27:50 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-982439253 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PKqZteC3IabV2rdCxsXBMLnTb72fzH4qdR2KdQSn</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>17</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-982439253\", {\"maxDepth\":0})</script>\n"}}