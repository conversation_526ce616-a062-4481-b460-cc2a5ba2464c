{"__meta": {"id": "Xfdd339c45e8337b062b965fa3789ddeb", "datetime": "2025-06-26 16:28:37", "utime": **********.932279, "method": "GET", "uri": "/add-to-cart/2307/pos", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.461168, "end": **********.932294, "duration": 0.47112584114074707, "duration_str": "471ms", "measures": [{"label": "Booting", "start": **********.461168, "relative_start": 0, "end": **********.848286, "relative_end": **********.848286, "duration": 0.387117862701416, "duration_str": "387ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.848296, "relative_start": 0.3871278762817383, "end": **********.932296, "relative_end": 2.1457672119140625e-06, "duration": 0.0840001106262207, "duration_str": "84ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 49206984, "peak_usage_str": "47MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET add-to-cart/{id}/{session}", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@addToCart", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1320\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1320-1544</a>"}, "queries": {"nb_statements": 7, "nb_failed_statements": 0, "accumulated_duration": 0.006549999999999999, "accumulated_duration_str": "6.55ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 17 limit 1", "type": "query", "params": [], "bindings": ["17"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.884889, "duration": 0.00196, "duration_str": "1.96ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 29.924}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.89481, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 29.924, "width_percent": 6.412}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 17 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["17", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.908997, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 36.336, "width_percent": 8.55}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (17) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.911257, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 44.885, "width_percent": 6.26}, {"sql": "select * from `product_services` where `product_services`.`id` = '2307' limit 1", "type": "query", "params": [], "bindings": ["2307"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1324}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.916363, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1324", "source": "app/Http/Controllers/ProductServiceController.php:1324", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1324", "ajax": false, "filename": "ProductServiceController.php", "line": "1324"}, "connection": "kdmkjkqknb", "start_percent": 51.145, "width_percent": 6.107}, {"sql": "select sum(`quantity`) as aggregate from `warehouse_products` where `product_id` = 2307 and exists (select * from `warehouses` where `warehouse_products`.`warehouse_id` = `warehouses`.`id` and `created_by` = 15)", "type": "query", "params": [], "bindings": ["2307", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/ProductService.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\ProductService.php", "line": 155}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1328}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.920199, "duration": 0.00244, "duration_str": "2.44ms", "memory": 0, "memory_str": null, "filename": "ProductService.php:155", "source": "app/Models/ProductService.php:155", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FProductService.php&line=155", "ajax": false, "filename": "ProductService.php", "line": "155"}, "connection": "kdmkjkqknb", "start_percent": 57.252, "width_percent": 37.252}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 4748}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 4682}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1397}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.923901, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4748", "source": "app/Models/Utility.php:4748", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUtility.php&line=4748", "ajax": false, "filename": "Utility.php", "line": "4748"}, "connection": "kdmkjkqknb", "start_percent": 94.504, "width_percent": 5.496}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 17,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1699163753 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>17</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1699163753\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.915442, "xdebug_link": null}]}, "session": {"_token": "PKqZteC3IabV2rdCxsXBMLnTb72fzH4qdR2KdQSn", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "17", "pos": "array:3 [\n  2297 => array:9 [\n    \"name\" => \"ساديا - دجاج مجمّد 800جم\"\n    \"quantity\" => 2\n    \"price\" => \"16.00\"\n    \"id\" => \"2297\"\n    \"tax\" => 0\n    \"subtotal\" => 32.0\n    \"originalquantity\" => 4\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  2301 => array:8 [\n    \"name\" => \"بريكستا ويفر المقرمش والمغطى بشوكولاتة الحليب 24 حبة\"\n    \"quantity\" => 1\n    \"price\" => \"13.00\"\n    \"tax\" => 0\n    \"subtotal\" => 13.0\n    \"id\" => \"2301\"\n    \"originalquantity\" => 0\n    \"product_tax\" => \"-\"\n  ]\n  2307 => array:8 [\n    \"name\" => \"زيت دوار الشمس 1.5 لتر فونتي\"\n    \"quantity\" => 1\n    \"price\" => \"15.99\"\n    \"tax\" => 0\n    \"subtotal\" => 15.99\n    \"id\" => \"2307\"\n    \"originalquantity\" => 9\n    \"product_tax\" => \"-\"\n  ]\n]"}, "request": {"path_info": "/add-to-cart/2307/pos", "status_code": "<pre class=sf-dump id=sf-dump-1814990161 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1814990161\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-2104120513 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2104120513\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PKqZteC3IabV2rdCxsXBMLnTb72fzH4qdR2KdQSn</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1bowxa%7C2%7Cfx3%7C0%7C1999; _clsk=1osc4j2%7C1750953875247%7C2%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjVIejdEbUhmSzA1ckRXTUVDMHF4VVE9PSIsInZhbHVlIjoic2F3N3FERDFERjRrSW5sdlk4N241YWZPR1VlT2Z5bVk0TFR4UGRWaUtuTVVlR1ZLL1JvM2QxR0FNaU1PT0J4MXdCZHoyMUYydS9XTWVzR2NnSVBteXhwMm9hNDZhMFZrN2JMVDFMRzVtV0FwNjFudUJpYW40L0RwUHdpY3Y4emx1UllCVVBlTjlzTzBPWDgzb09lL3FYelUySDFHZHU3aWcxRWU3dTY5K085Q2dzMU9zbCtzWEhlQTFRYUljRnRvTFFRck4ydE93ZXVwMldYYkZWdGRjNVZGa2gycDNpazRpa3JLQWkyT2orYlhMeDVDWEFGMUlNczFmc1FXaTFjcGE3d09iOXdVaTltdEUrYy9sRXNFdE9rQVNaRE8zK0YrS1NYK3gyT0ZnRHNBOXZRZFQrQ3U5RzU1L2RFTitlbG9BdDBtM043UWVjTE5UQmJsVnQxSzR6dnV6aW5KczdFK25MRGNQc2ZSemRHZ1ZJNVNFZ3JXT2F1dlg1Y2F4NTFZU0VmS1U2VjIxSHNrekM0Y1kxM3hQbGs3d25tOHR1aWxuY2laM01vZ3FvNlNMSHpTdXZNZEkxVk1hSTlOWkNKd25TTFJxZk80cmlpclFSQ2hKTU1qSk81WXZMUWxHMjdSN2VUayt2NHZhWFpJLzVBUzZsV0ZZbVFXUnZmcyt6enYiLCJtYWMiOiIxMDJmNGRiNTAyMWI3YjBkNzVlMWZkMDNjZTIyNjYxMDU5NzI2MjI5YTA0ZmNmMzQ0M2Q3ZTQxM2U1OGQ4YTNjIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlkyRFpJYWhPbXNKajBiaUlOT1VaaWc9PSIsInZhbHVlIjoiWGZyc0ZWcnQvNklEYk9Va1NnK1oyUU9YWGo5RTZrcjA2WUVpVnlJRFZVd2tubTM4Z2NwTTMwQ3BWVUdqKzNrRmNMKzZPR2U5eFJGMkFzMEQzV25xcEhIR0twWXU5eWVyN1h3OCs4eFBRR2lRNkZWZXFjVlBYS2hoendEb3pQMEJJL0VUM2ZQUjg3SXpWWXFzeUFvM3lkdTUwSUplVmcraVk2VXR3S1dNL0Voa3FhWVBqaHRMUHROQnJjcFlpSDViUW1lSjlwYzV1TVV4Vk9hWUdNWVhHbTBBUDJtSUJpNmVEckZ2dUNvS2xrcWxvN0dqYld3VVZKbWtub0hNS0ZaWTZHRlphby9tL3BBSTNxdHJZTGZ1RlMvU3psZmZSRHJjcFNmT3JCUDVkYVduQ295eWNBN2tVUlpaWDRPcG14OXQycTloNE5YOW1KL0FMekR0ejVIMFpsYXBCYmplL2tnUGJzcmFqL05lYXM5bzMvNDVSQnVkWHRoSE54Z2w3T0kxT1RxbURmdEQ5TVE4Z3B3WG9Ucm9BaFBWNDRkVFZjZzg3cU1NTmI2cFl1WlQ5ck4ycVZveFY3dFdBOTUwSVBjSDhVMGU1QXZtMC9aQkRBbjdVRzU4RmJMSFBUR0IyVkl0VVNIL285K1F3TmxZVkdxQVlyczJTNmszZ1dUWlRHS3UiLCJtYWMiOiIwOWI5ZDIyYzQ4NTg1MWI3MTQyMGRkMGZmMjQyOTQ5YmIxOWI4ZDE5ZmY4NDA3YjY4YTI0YTc1NGRiNTdjZjZiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1076512090 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PKqZteC3IabV2rdCxsXBMLnTb72fzH4qdR2KdQSn</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GUUMfjgJKgBqP5oaDcRP4Rfu0FM4k6V9zWwhkyoG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1076512090\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-41701744 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 16:28:37 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Inc3anU1ODBneHpKOHNtb09XMTJCcUE9PSIsInZhbHVlIjoiS2YzR2xqby9KQ0pOeEgvTFp0bUQ4WGdJMmY0dm1HdFk2WEZlc2ZjSUwranM0dUZoNERDL1ZuU29pL3l3ZkZBOVdpb0dMVEtvWTBiRS9BWW9CWTU4cUhjWG9OZk1TTjRWc1Jxc0d4WjhVZSs4N2x2aUdZeTJmNHptMVVUdGFGSS9laTRNakdtVXZaMmc3aU5TcXRadkdpS3VtWTZzUmora3czTE5IV3htZEJEUGdSYVMrM2dxcUdvQ3JiN3FjOXdVZ3dZSU5pOHhnTjgzS1BRQ2dicG9GWFBvejRMK3FNdTRBMEcvbFpxU2VHdXZldElsWGM0WEgrWlBXd3FqLzhraVBYVzUxZ2U0Y1FRVHVMclcrZVJTdlRQK21RYmxiaFVuRkdKRXVsK0JTbm1MRFJlREphbmVGYUVCVGhlTEVzamUwNHl0Mm9IUm9mZzJUZ095S003UjlaRG5MVm9hd000OVlLc3FLQS85amJxN25RN3YyVXBmdFMvMkVnVzJvWDNnd3ZpdElRbjhuNFNjV1ArcmlEZ09LbGIycXNjTG1IazQrR0cyMi8zckJrdkp4d1BPa3U1QnlHbldES1l0amNybHNqMUdKeUtqWHc3T1A2YkU1VWsyREMrSTFXVm9rRGdDY2t4RE80YzBSSW1FS2dkQlh5T3NQNVVNNXpRVGlCUUkiLCJtYWMiOiI5MTUyMDYwMjI2NTBkYjU2M2NkNGM5ZGJmNzY2ODlkMGYzMmZmZTY3MmMxMDk5Yjg1OGJjYWEwOTY1NDY4OGQxIiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 18:28:37 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Im1LUHQvK3JKV3FsWjZKVFpqNjdsWkE9PSIsInZhbHVlIjoiblVrNWs5czByMEJyWTVyU0NVS3FSM3FYRmhvTm40bWJyR1QreStuU0FMVkRUZ3czVVQrZUxlQ0Y4S096RmFpZG9iQnBzbW00azI4UlRUdVNnLzVOazdRa05YelJGaGFQREhXYUJrSWZxTTNLWXZUOUtpOExXbWhsc0JRRnBka0wzenpmSnZlMFd5dWRSbWZLQVZCYytXc3pYVDNyYTFLMEUvRWxyeWl1NncxR3FIQW1KNzQyazNic1lDVHY4VjhiOHByTUhKMG05dHZoQk54NXJpNDJBdXpZbFhDNHlRZGNXZE4rdHE0M2J3alhxVDh2SzdOV3FSampFUUtEY1ZLU2hseU04d2dkbTZLcW1WSFJXbjRMYnIrMm04RmR6QnpMNW1Rb2pJbDJ5bnlzT29TYmloMTBzbmdHdXZXZW5vbytLazdoK2VhWGVHQmtDVzNyZEtSRVprZlpsYVYwc25PVVc0Rk5NMGpPMUlDVGRXYmVFSUpzd3o2NldWMUJQbjQyVzExbDVudlFPbkZLMDQ4a3YySlo4ZGx2aUYvUmI3RWplTXVaWm1IMmhmUklDTGFvR0QxZThUUEFQb1NsenhiMGZwaHZNb1o5dk1KaE5IY2NQRnhnWTZEcEpnV3BRMlkrSXVPYmJBN0RSWTdSbzg5WlMwT1JybENSZ2ZNMWNKT3ciLCJtYWMiOiI2MmY0NmNlMmMxNzhlY2U4N2QxNDhjOTJiNTliYTM4Y2NlZGQxMzNiODJiYzQ2N2M3NTZhMzc0YjRjNDI4MjQyIiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 18:28:37 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Inc3anU1ODBneHpKOHNtb09XMTJCcUE9PSIsInZhbHVlIjoiS2YzR2xqby9KQ0pOeEgvTFp0bUQ4WGdJMmY0dm1HdFk2WEZlc2ZjSUwranM0dUZoNERDL1ZuU29pL3l3ZkZBOVdpb0dMVEtvWTBiRS9BWW9CWTU4cUhjWG9OZk1TTjRWc1Jxc0d4WjhVZSs4N2x2aUdZeTJmNHptMVVUdGFGSS9laTRNakdtVXZaMmc3aU5TcXRadkdpS3VtWTZzUmora3czTE5IV3htZEJEUGdSYVMrM2dxcUdvQ3JiN3FjOXdVZ3dZSU5pOHhnTjgzS1BRQ2dicG9GWFBvejRMK3FNdTRBMEcvbFpxU2VHdXZldElsWGM0WEgrWlBXd3FqLzhraVBYVzUxZ2U0Y1FRVHVMclcrZVJTdlRQK21RYmxiaFVuRkdKRXVsK0JTbm1MRFJlREphbmVGYUVCVGhlTEVzamUwNHl0Mm9IUm9mZzJUZ095S003UjlaRG5MVm9hd000OVlLc3FLQS85amJxN25RN3YyVXBmdFMvMkVnVzJvWDNnd3ZpdElRbjhuNFNjV1ArcmlEZ09LbGIycXNjTG1IazQrR0cyMi8zckJrdkp4d1BPa3U1QnlHbldES1l0amNybHNqMUdKeUtqWHc3T1A2YkU1VWsyREMrSTFXVm9rRGdDY2t4RE80YzBSSW1FS2dkQlh5T3NQNVVNNXpRVGlCUUkiLCJtYWMiOiI5MTUyMDYwMjI2NTBkYjU2M2NkNGM5ZGJmNzY2ODlkMGYzMmZmZTY3MmMxMDk5Yjg1OGJjYWEwOTY1NDY4OGQxIiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 18:28:37 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Im1LUHQvK3JKV3FsWjZKVFpqNjdsWkE9PSIsInZhbHVlIjoiblVrNWs5czByMEJyWTVyU0NVS3FSM3FYRmhvTm40bWJyR1QreStuU0FMVkRUZ3czVVQrZUxlQ0Y4S096RmFpZG9iQnBzbW00azI4UlRUdVNnLzVOazdRa05YelJGaGFQREhXYUJrSWZxTTNLWXZUOUtpOExXbWhsc0JRRnBka0wzenpmSnZlMFd5dWRSbWZLQVZCYytXc3pYVDNyYTFLMEUvRWxyeWl1NncxR3FIQW1KNzQyazNic1lDVHY4VjhiOHByTUhKMG05dHZoQk54NXJpNDJBdXpZbFhDNHlRZGNXZE4rdHE0M2J3alhxVDh2SzdOV3FSampFUUtEY1ZLU2hseU04d2dkbTZLcW1WSFJXbjRMYnIrMm04RmR6QnpMNW1Rb2pJbDJ5bnlzT29TYmloMTBzbmdHdXZXZW5vbytLazdoK2VhWGVHQmtDVzNyZEtSRVprZlpsYVYwc25PVVc0Rk5NMGpPMUlDVGRXYmVFSUpzd3o2NldWMUJQbjQyVzExbDVudlFPbkZLMDQ4a3YySlo4ZGx2aUYvUmI3RWplTXVaWm1IMmhmUklDTGFvR0QxZThUUEFQb1NsenhiMGZwaHZNb1o5dk1KaE5IY2NQRnhnWTZEcEpnV3BRMlkrSXVPYmJBN0RSWTdSbzg5WlMwT1JybENSZ2ZNMWNKT3ciLCJtYWMiOiI2MmY0NmNlMmMxNzhlY2U4N2QxNDhjOTJiNTliYTM4Y2NlZGQxMzNiODJiYzQ2N2M3NTZhMzc0YjRjNDI4MjQyIiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 18:28:37 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-41701744\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PKqZteC3IabV2rdCxsXBMLnTb72fzH4qdR2KdQSn</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>17</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2297</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1587;&#1575;&#1583;&#1610;&#1575; - &#1583;&#1580;&#1575;&#1580; &#1605;&#1580;&#1605;&#1617;&#1583; 800&#1580;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">16.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2297</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>32.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>4</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>2301</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"52 characters\">&#1576;&#1585;&#1610;&#1603;&#1587;&#1578;&#1575; &#1608;&#1610;&#1601;&#1585; &#1575;&#1604;&#1605;&#1602;&#1585;&#1605;&#1588; &#1608;&#1575;&#1604;&#1605;&#1594;&#1591;&#1609; &#1576;&#1588;&#1608;&#1603;&#1608;&#1604;&#1575;&#1578;&#1577; &#1575;&#1604;&#1581;&#1604;&#1610;&#1576; 24 &#1581;&#1576;&#1577;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">13.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>13.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2301</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n    <span class=sf-dump-key>2307</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"28 characters\">&#1586;&#1610;&#1578; &#1583;&#1608;&#1575;&#1585; &#1575;&#1604;&#1588;&#1605;&#1587; 1.5 &#1604;&#1578;&#1585; &#1601;&#1608;&#1606;&#1578;&#1610;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">15.99</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>15.99</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2307</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>9</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}