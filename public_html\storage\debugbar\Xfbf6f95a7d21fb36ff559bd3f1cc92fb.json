{"__meta": {"id": "Xfbf6f95a7d21fb36ff559bd3f1cc92fb", "datetime": "2025-06-26 15:58:29", "utime": **********.161537, "method": "GET", "uri": "/bill/eyJpdiI6IlhwTTVrTG03L25NelZRV0dEbnFMV2c9PSIsInZhbHVlIjoiM202QWM5RGRFWllQbWE5NU0zZGUxUT09IiwibWFjIjoiMzBlMTkzNTRiODk3ZWZiM2UxYzJlOGI2MTE2YTJiOWI2YjkyYzQ5ZTQ4ZmYxYTcyNzE5YTk3MzliOGFlNzRjZCIsInRhZyI6IiJ9", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 259, "messages": [{"message": "[15:58:29] LOG.warning: Implicit conversion from float 0.25 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.058848, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 0.5 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.059052, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 0.75 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.059121, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 1.25 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.059221, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 1.5 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.05929, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 1.75 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.059352, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 2.25 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.059415, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 2.5 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.059476, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 2.75 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.059536, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 3.25 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.059603, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 3.5 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.059664, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 3.75 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.059724, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 4.25 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.059787, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 4.5 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.059848, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 4.75 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.059907, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 5.25 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.05997, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 5.5 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.06003, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 5.75 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.06009, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 6.25 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.060155, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 6.5 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.060219, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 6.75 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.060279, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 7.25 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.06034, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 7.5 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.060399, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 7.75 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.060458, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 8.25 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.06052, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 8.5 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.060585, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 8.75 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.060645, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 9.25 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.060706, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 9.5 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.060766, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 9.75 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.060826, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 10.25 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.060888, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 10.5 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.060946, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 10.75 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.061005, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 11.25 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.061068, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 11.5 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.061128, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 11.75 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.061191, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 12.25 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.061253, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 12.5 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.061313, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 12.75 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.061372, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 13.25 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.061434, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 13.5 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.061493, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 13.75 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.061556, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 14.25 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.061618, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 14.5 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.061676, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 14.75 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.061735, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 15.25 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.061797, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 15.5 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.061857, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 15.75 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.061916, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 16.25 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.061976, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 16.5 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.062034, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 16.75 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.062092, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 17.25 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.062157, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 17.5 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.062216, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 17.75 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.062276, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 18.25 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.062336, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 18.5 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.062395, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 18.75 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.062453, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 19.25 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.062514, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 19.5 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.062573, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 19.75 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.062631, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 20.25 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.062693, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 20.5 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.062752, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 20.75 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.062811, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 21.25 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.062872, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 21.5 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.062932, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 21.75 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.062991, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 22.25 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.063054, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 22.5 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.063114, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 22.75 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.063176, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 23.25 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.063238, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 23.5 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.063299, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 23.75 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.063357, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 24.25 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.063419, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 24.5 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.063478, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 24.75 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.063536, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 25.25 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.063597, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 25.5 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.063656, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 25.75 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.063714, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 26.25 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.063776, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 26.5 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.063834, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 26.75 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.063894, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 27.25 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.063955, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 27.5 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.064014, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 27.75 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.064073, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 28.25 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.064136, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 28.5 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.064199, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 28.75 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.064258, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 29.25 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.06432, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 29.5 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.064378, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 29.75 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.064437, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 30.25 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.064498, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 30.5 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.064557, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 30.75 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.064616, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 31.25 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.064678, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 31.5 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.064737, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 31.75 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.064797, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 32.25 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.064859, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 32.5 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.064917, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 32.75 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.064976, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 33.25 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.065037, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 33.5 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.065097, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 33.75 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.065159, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 34.25 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.065224, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 34.5 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.065283, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 34.75 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.065343, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 35.25 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.065405, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 35.5 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.065464, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 35.75 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.065522, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 36.25 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.065584, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 36.5 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.065643, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 36.75 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.065702, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 37.25 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.065762, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 37.5 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.06582, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 37.75 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.065879, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 38.25 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.065941, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 38.5 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.066001, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 38.75 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.066061, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 39.25 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.066122, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 39.5 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.066184, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 39.75 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.066244, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 40.25 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.066304, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 40.5 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.066364, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 40.75 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.066422, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 41.25 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.066485, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 41.5 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.066544, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 41.75 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.066604, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 42.25 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.066666, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 42.5 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.066725, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 42.75 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.066785, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 43.25 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.066847, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 43.5 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.066906, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 43.75 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.066965, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 44.25 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.067028, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 44.5 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.067089, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 44.75 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.067153, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 45.25 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.067317, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 45.5 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.067403, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 45.75 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.067473, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 46.25 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.067537, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 46.5 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.067597, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 46.75 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.067657, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 47.25 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.067721, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 47.5 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.067781, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 47.75 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.06784, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 48.25 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.067903, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 48.5 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.067963, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 48.75 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.068023, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 49.25 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.068084, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 49.5 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.068147, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 49.75 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.068208, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 50.25 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.06827, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 50.5 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.068329, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 50.75 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.068389, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 51.25 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.06845, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 51.5 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.06851, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 51.75 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.06857, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 52.25 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.06863, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 52.5 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.068689, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 52.75 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.06875, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 53.25 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.068812, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 53.5 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.068871, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 53.75 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.06893, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 54.25 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.068991, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 54.5 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.069051, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 54.75 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.06911, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 55.25 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.069177, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 55.5 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.069239, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 55.75 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.069298, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 56.25 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.06936, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 56.5 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.06942, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 56.75 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.069479, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 57.25 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.069542, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 57.5 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.069601, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 57.75 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.069661, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 58.25 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.069723, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 58.5 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.069782, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 58.75 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.069842, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 59.25 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.069906, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 59.5 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.069967, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 59.75 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.070027, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 60.25 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.07009, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 60.5 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.070152, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 60.75 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.070214, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 61.25 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.070276, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 61.5 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.070335, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 61.75 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.070395, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 62.25 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.070456, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 62.5 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.070514, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 62.75 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.070573, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 63.25 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.070635, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 63.5 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.070694, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 63.75 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.070755, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 64.25 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.070818, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 64.5 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.070877, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 64.75 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.070937, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 65.25 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.070999, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 65.5 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.071059, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 65.75 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.071118, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 66.25 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.071185, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 66.5 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.071247, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 66.75 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.071331, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 67.25 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.071486, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 67.5 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.071682, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 67.75 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.071801, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 68.25 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.071905, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 0.25 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.072008, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 0.5 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.072108, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 0.75 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.072221, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 1.25 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.072327, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 1.5 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.072398, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 1.75 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.072462, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 2.25 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.072527, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 2.5 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.072587, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 2.75 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.072648, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 3.25 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.07271, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 3.5 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.072769, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 3.75 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.072828, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 4.25 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.07289, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 4.5 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.07295, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 4.75 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.07301, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 5.25 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.073072, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 5.5 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.073132, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 5.75 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.073192, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 6.25 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.073255, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 6.5 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.073314, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 6.75 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.073375, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 7.25 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.073436, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 7.5 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.073496, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 7.75 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.073556, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 8.25 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.073619, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 8.5 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.073678, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 8.75 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.073737, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 9.25 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.073801, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 9.5 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.073861, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 9.75 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.07392, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 10.25 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.073983, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 10.5 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.074044, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 10.75 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.074104, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 11.25 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.074166, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 11.5 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.074228, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 11.75 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.074287, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 12.25 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.07435, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 12.5 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.074411, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 12.75 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.07447, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 13.25 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.074532, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 13.5 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.074597, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 13.75 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.074657, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 14.25 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.074719, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 14.5 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.074779, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 14.75 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.074838, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 15.25 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.074899, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 15.5 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.074957, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 15.75 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.07502, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 16.25 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.075082, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 16.5 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.075141, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 16.75 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.0752, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 17.25 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.075261, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 17.5 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.075321, "xdebug_link": null, "collector": "log"}, {"message": "[15:58:29] LOG.warning: Implicit conversion from float 17.75 to int loses precision in C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.075381, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.092501, "end": **********.161734, "duration": 1.0692331790924072, "duration_str": "1.07s", "measures": [{"label": "Booting", "start": **********.092501, "relative_start": 0, "end": **********.472556, "relative_end": **********.472556, "duration": 0.38005518913269043, "duration_str": "380ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.472566, "relative_start": 0.3800649642944336, "end": **********.161736, "relative_end": 1.9073486328125e-06, "duration": 0.6891701221466064, "duration_str": "689ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 54850680, "peak_usage_str": "52MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 7, "templates": [{"name": "1x bill.view", "param_count": null, "params": [], "start": **********.558257, "type": "blade", "hash": "bladeC:\\laragon\\www\\ERPcopy\\public_html\\resources\\views/bill/view.blade.phpbill.view", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fresources%2Fviews%2Fbill%2Fview.blade.php&line=1", "ajax": false, "filename": "view.blade.php", "line": "?"}, "render_count": 1, "name_original": "bill.view"}, {"name": "1x layouts.admin", "param_count": null, "params": [], "start": **********.091515, "type": "blade", "hash": "bladeC:\\laragon\\www\\ERPcopy\\public_html\\resources\\views/layouts/admin.blade.phplayouts.admin", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fresources%2Fviews%2Flayouts%2Fadmin.blade.php&line=1", "ajax": false, "filename": "admin.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.admin"}, {"name": "1x partials.admin.menu", "param_count": null, "params": [], "start": **********.095038, "type": "blade", "hash": "bladeC:\\laragon\\www\\ERPcopy\\public_html\\resources\\views/partials/admin/menu.blade.phppartials.admin.menu", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fresources%2Fviews%2Fpartials%2Fadmin%2Fmenu.blade.php&line=1", "ajax": false, "filename": "menu.blade.php", "line": "?"}, "render_count": 1, "name_original": "partials.admin.menu"}, {"name": "1x partials.admin.header", "param_count": null, "params": [], "start": **********.141204, "type": "blade", "hash": "bladeC:\\laragon\\www\\ERPcopy\\public_html\\resources\\views/partials/admin/header.blade.phppartials.admin.header", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fresources%2Fviews%2Fpartials%2Fadmin%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "partials.admin.header"}, {"name": "1x partials.admin.footer", "param_count": null, "params": [], "start": **********.151829, "type": "blade", "hash": "bladeC:\\laragon\\www\\ERPcopy\\public_html\\resources\\views/partials/admin/footer.blade.phppartials.admin.footer", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fresources%2Fviews%2Fpartials%2Fadmin%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "partials.admin.footer"}, {"name": "1x layouts.cookie_consent", "param_count": null, "params": [], "start": **********.153973, "type": "blade", "hash": "bladeC:\\laragon\\www\\ERPcopy\\public_html\\resources\\views/layouts/cookie_consent.blade.phplayouts.cookie_consent", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fresources%2Fviews%2Flayouts%2Fcookie_consent.blade.php&line=1", "ajax": false, "filename": "cookie_consent.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.cookie_consent"}, {"name": "1x Chatify::layouts.footerLinks", "param_count": null, "params": [], "start": **********.154344, "type": "blade", "hash": "bladeC:\\laragon\\www\\ERPcopy\\public_html\\resources\\views/vendor/Chatify/layouts/footerLinks.blade.phpChatify::layouts.footerLinks", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fresources%2Fviews%2Fvendor%2FChatify%2Flayouts%2FfooterLinks.blade.php&line=1", "ajax": false, "filename": "footerLinks.blade.php", "line": "?"}, "render_count": 1, "name_original": "Chatify::layouts.footerLinks"}]}, "route": {"uri": "GET bill/{bill}", "middleware": "web, verified, auth, XSS, revalidate", "as": "bill.show", "controller": "App\\Http\\Controllers\\BillController@show", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FHttp%2FControllers%2FBillController.php&line=333\" onclick=\"\">app/Http/Controllers/BillController.php:333-393</a>"}, "queries": {"nb_statements": 28, "nb_failed_statements": 0, "accumulated_duration": 0.015519999999999997, "accumulated_duration_str": "15.52ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.5014732, "duration": 0.0022, "duration_str": "2.2ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 14.175}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.512514, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 14.175, "width_percent": 3.093}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 15 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["15", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.525575, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 17.268, "width_percent": 4.704}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.5275888, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 21.972, "width_percent": 2.706}, {"sql": "select * from `bills` where `bills`.`id` = 4 limit 1", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Http/Controllers/BillController.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Controllers\\BillController.php", "line": 345}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.531852, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "BillController.php:345", "source": "app/Http/Controllers/BillController.php:345", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FHttp%2FControllers%2FBillController.php&line=345", "ajax": false, "filename": "BillController.php", "line": "345"}, "connection": "kdmkjkqknb", "start_percent": 24.678, "width_percent": 2.706}, {"sql": "select * from `debit_notes` where `debit_notes`.`bill` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/BillController.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Controllers\\BillController.php", "line": 345}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.535242, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "BillController.php:345", "source": "app/Http/Controllers/BillController.php:345", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FHttp%2FControllers%2FBillController.php&line=345", "ajax": false, "filename": "BillController.php", "line": "345"}, "connection": "kdmkjkqknb", "start_percent": 27.384, "width_percent": 2.835}, {"sql": "select * from `bill_payments` where `bill_payments`.`bill_id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/BillController.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Controllers\\BillController.php", "line": 345}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.537567, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "BillController.php:345", "source": "app/Http/Controllers/BillController.php:345", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FHttp%2FControllers%2FBillController.php&line=345", "ajax": false, "filename": "BillController.php", "line": "345"}, "connection": "kdmkjkqknb", "start_percent": 30.219, "width_percent": 2.255}, {"sql": "select * from `bill_products` where `bill_products`.`bill_id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/BillController.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Controllers\\BillController.php", "line": 345}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.5396738, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "BillController.php:345", "source": "app/Http/Controllers/BillController.php:345", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FHttp%2FControllers%2FBillController.php&line=345", "ajax": false, "filename": "BillController.php", "line": "345"}, "connection": "kdmkjkqknb", "start_percent": 32.474, "width_percent": 2.706}, {"sql": "select * from `product_services` where `product_services`.`id` in (0)", "type": "query", "params": [], "bindings": ["0"], "hints": null, "show_copy": false, "backtrace": [{"index": 27, "namespace": null, "name": "app/Http/Controllers/BillController.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Controllers\\BillController.php", "line": 345}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.542674, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "BillController.php:345", "source": "app/Http/Controllers/BillController.php:345", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FHttp%2FControllers%2FBillController.php&line=345", "ajax": false, "filename": "BillController.php", "line": "345"}, "connection": "kdmkjkqknb", "start_percent": 35.18, "width_percent": 1.869}, {"sql": "select * from `bill_payments` where `bill_id` = 4 limit 1", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/BillController.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Controllers\\BillController.php", "line": 349}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.543854, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "BillController.php:349", "source": "app/Http/Controllers/BillController.php:349", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FHttp%2FControllers%2FBillController.php&line=349", "ajax": false, "filename": "BillController.php", "line": "349"}, "connection": "kdmkjkqknb", "start_percent": 37.049, "width_percent": 1.224}, {"sql": "select * from `venders` where `venders`.`id` = 4 and `venders`.`id` is not null limit 1", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/BillController.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Controllers\\BillController.php", "line": 350}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.545758, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "BillController.php:350", "source": "app/Http/Controllers/BillController.php:350", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FHttp%2FControllers%2FBillController.php&line=350", "ajax": false, "filename": "BillController.php", "line": "350"}, "connection": "kdmkjkqknb", "start_percent": 38.273, "width_percent": 2.191}, {"sql": "select * from `bill_accounts` where `bill_accounts`.`ref_id` = 4 and `bill_accounts`.`ref_id` is not null", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/BillController.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Controllers\\BillController.php", "line": 353}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.547323, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "BillController.php:353", "source": "app/Http/Controllers/BillController.php:353", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FHttp%2FControllers%2FBillController.php&line=353", "ajax": false, "filename": "BillController.php", "line": "353"}, "connection": "kdmkjkqknb", "start_percent": 40.464, "width_percent": 1.675}, {"sql": "select `custom_field_values`.`value`, `custom_fields`.`id` from `custom_field_values` inner join `custom_fields` on `custom_field_values`.`field_id` = `custom_fields`.`id` where `custom_fields`.`module` = 'bill' and `record_id` = 4", "type": "query", "params": [], "bindings": ["bill", "4"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/CustomField.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\CustomField.php", "line": 63}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/BillController.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Controllers\\BillController.php", "line": 379}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.548844, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "CustomField.php:63", "source": "app/Models/CustomField.php:63", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FCustomField.php&line=63", "ajax": false, "filename": "CustomField.php", "line": "63"}, "connection": "kdmkjkqknb", "start_percent": 42.139, "width_percent": 3.028}, {"sql": "select * from `custom_fields` where `created_by` = 15 and `module` = 'bill'", "type": "query", "params": [], "bindings": ["15", "bill"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BillController.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Controllers\\BillController.php", "line": 380}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.550625, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "BillController.php:380", "source": "app/Http/Controllers/BillController.php:380", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FHttp%2FControllers%2FBillController.php&line=380", "ajax": false, "filename": "BillController.php", "line": "380"}, "connection": "kdmkjkqknb", "start_percent": 45.168, "width_percent": 1.16}, {"sql": "select * from `chart_of_accounts` where `chart_of_accounts`.`id` = 229 limit 1", "type": "query", "params": [], "bindings": ["229"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "bill.view", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\resources\\views/bill/view.blade.php", "line": 362}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.0814059, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "bill.view:362", "source": "view::bill.view:362", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fresources%2Fviews%2Fbill%2Fview.blade.php&line=362", "ajax": false, "filename": "view.blade.php", "line": "362"}, "connection": "kdmkjkqknb", "start_percent": 46.327, "width_percent": 3.737}, {"sql": "select * from `chart_of_accounts` where `chart_of_accounts`.`id` = 229 limit 1", "type": "query", "params": [], "bindings": ["229"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "bill.view", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\resources\\views/bill/view.blade.php", "line": 417}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.083718, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "bill.view:417", "source": "view::bill.view:417", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fresources%2Fviews%2Fbill%2Fview.blade.php&line=417", "ajax": false, "filename": "view.blade.php", "line": "417"}, "connection": "kdmkjkqknb", "start_percent": 50.064, "width_percent": 2.191}, {"sql": "select * from `taxes`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 790}, {"index": 19, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Bill.php", "line": 102}, {"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Bill.php", "line": 152}, {"index": 21, "namespace": "view", "name": "bill.view", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\resources\\views/bill/view.blade.php", "line": 488}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.086357, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "Utility.php:790", "source": "app/Models/Utility.php:790", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUtility.php&line=790", "ajax": false, "filename": "Utility.php", "line": "790"}, "connection": "kdmkjkqknb", "start_percent": 52.255, "width_percent": 2.062}, {"sql": "select * from `bill_attachments` where `bill_attachments`.`bill_id` = 4 and `bill_attachments`.`bill_id` is not null", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "bill.view", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\resources\\views/bill/view.blade.php", "line": 516}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.088765, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "bill.view:516", "source": "view::bill.view:516", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fresources%2Fviews%2Fbill%2Fview.blade.php&line=516", "ajax": false, "filename": "view.blade.php", "line": "516"}, "connection": "kdmkjkqknb", "start_percent": 54.317, "width_percent": 2.771}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 4748}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 4682}, {"index": 15, "namespace": "view", "name": "layouts.admin", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\resources\\views/layouts/admin.blade.php", "line": 5}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.091971, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4748", "source": "app/Models/Utility.php:4748", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUtility.php&line=4748", "ajax": false, "filename": "Utility.php", "line": "4748"}, "connection": "kdmkjkqknb", "start_percent": 57.088, "width_percent": 2.577}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 4748}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 4682}, {"index": 15, "namespace": "view", "name": "layouts.admin", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\resources\\views/layouts/admin.blade.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.093467, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4748", "source": "app/Models/Utility.php:4748", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUtility.php&line=4748", "ajax": false, "filename": "Utility.php", "line": "4748"}, "connection": "kdmkjkqknb", "start_percent": 59.665, "width_percent": 2.255}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 4748}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 4682}, {"index": 15, "namespace": "view", "name": "partials.admin.menu", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\resources\\views/partials/admin/menu.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.097403, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4748", "source": "app/Models/Utility.php:4748", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUtility.php&line=4748", "ajax": false, "filename": "Utility.php", "line": "4748"}, "connection": "kdmkjkqknb", "start_percent": 61.92, "width_percent": 2.448}, {"sql": "select * from `email_templates` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/EmailTemplate.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\EmailTemplate.php", "line": 27}, {"index": 20, "namespace": "view", "name": "partials.admin.menu", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\resources\\views/partials/admin/menu.blade.php", "line": 10}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.099154, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "EmailTemplate.php:27", "source": "app/Models/EmailTemplate.php:27", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FEmailTemplate.php&line=27", "ajax": false, "filename": "EmailTemplate.php", "line": "27"}, "connection": "kdmkjkqknb", "start_percent": 64.369, "width_percent": 1.675}, {"sql": "select * from `plans` where `plans`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Plan.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Plan.php", "line": 65}, {"index": 21, "namespace": "view", "name": "partials.admin.menu", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\resources\\views/partials/admin/menu.blade.php", "line": 13}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.1006389, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "Plan.php:65", "source": "app/Models/Plan.php:65", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FPlan.php&line=65", "ajax": false, "filename": "Plan.php", "line": "65"}, "connection": "kdmkjkqknb", "start_percent": 66.044, "width_percent": 2.771}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 4748}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 4682}, {"index": 15, "namespace": "view", "name": "partials.admin.header", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\resources\\views/partials/admin/header.blade.php", "line": 3}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.1416318, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4748", "source": "app/Models/Utility.php:4748", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUtility.php&line=4748", "ajax": false, "filename": "Utility.php", "line": "4748"}, "connection": "kdmkjkqknb", "start_percent": 68.814, "width_percent": 3.93}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'kdmkjkqknb' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 537}, {"index": 14, "namespace": "view", "name": "partials.admin.header", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\resources\\views/partials/admin/header.blade.php", "line": 4}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.1433399, "duration": 0.00302, "duration_str": "3.02ms", "memory": 0, "memory_str": null, "filename": "Utility.php:537", "source": "app/Models/Utility.php:537", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUtility.php&line=537", "ajax": false, "filename": "Utility.php", "line": "537"}, "connection": "kdmkjkqknb", "start_percent": 72.745, "width_percent": 19.459}, {"sql": "select `full_name`, `code` from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 543}, {"index": 18, "namespace": "view", "name": "partials.admin.header", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\resources\\views/partials/admin/header.blade.php", "line": 4}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.147731, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "Utility.php:543", "source": "app/Models/Utility.php:543", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUtility.php&line=543", "ajax": false, "filename": "Utility.php", "line": "543"}, "connection": "kdmkjkqknb", "start_percent": 92.204, "width_percent": 2.835}, {"sql": "select count(*) as aggregate from `ch_messages` where `to_id` = 15 and `seen` = 0", "type": "query", "params": [], "bindings": ["15", "0"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "partials.admin.header", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\resources\\views/partials/admin/header.blade.php", "line": 18}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.149675, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "partials.admin.header:18", "source": "view::partials.admin.header:18", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fresources%2Fviews%2Fpartials%2Fadmin%2Fheader.blade.php&line=18", "ajax": false, "filename": "header.blade.php", "line": "18"}, "connection": "kdmkjkqknb", "start_percent": 95.039, "width_percent": 2.642}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 4748}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 6246}, {"index": 15, "namespace": "view", "name": "partials.admin.footer", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\resources\\views/partials/admin/footer.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.152198, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4748", "source": "app/Models/Utility.php:4748", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUtility.php&line=4748", "ajax": false, "filename": "Utility.php", "line": "4748"}, "connection": "kdmkjkqknb", "start_percent": 97.68, "width_percent": 2.32}]}, "models": {"data": {"App\\Models\\ChartOfAccount": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FChartOfAccount.php&line=1", "ajax": false, "filename": "ChartOfAccount.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\Bill": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FBill.php&line=1", "ajax": false, "filename": "Bill.php", "line": "?"}}, "App\\Models\\BillProduct": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FBillProduct.php&line=1", "ajax": false, "filename": "BillProduct.php", "line": "?"}}, "App\\Models\\Vender": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FVender.php&line=1", "ajax": false, "filename": "Vender.php", "line": "?"}}, "App\\Models\\BillAccount": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FBillAccount.php&line=1", "ajax": false, "filename": "BillAccount.php", "line": "?"}}, "App\\Models\\EmailTemplate": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FEmailTemplate.php&line=1", "ajax": false, "filename": "EmailTemplate.php", "line": "?"}}, "App\\Models\\Plan": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FPlan.php&line=1", "ajax": false, "filename": "Plan.php", "line": "?"}}}, "count": 10, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 77, "messages": [{"message": "[ability => show bill, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>show bill</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">show bill</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.531073, "xdebug_link": null}, {"message": "[ability => send bill, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1501749252 data-indent-pad=\"  \"><span class=sf-dump-note>send bill</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">send bill</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1501749252\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.995764, "xdebug_link": null}, {"message": "[ability => edit bill, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1184522330 data-indent-pad=\"  \"><span class=sf-dump-note>edit bill</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">edit bill</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1184522330\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.996115, "xdebug_link": null}, {"message": "[ability => create payment bill, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1727652178 data-indent-pad=\"  \"><span class=sf-dump-note>create payment bill</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">create payment bill</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1727652178\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.996525, "xdebug_link": null}, {"message": "[ability => edit bill, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-286579596 data-indent-pad=\"  \"><span class=sf-dump-note>edit bill</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">edit bill</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-286579596\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.090375, "xdebug_link": null}, {"message": "[ability => delete payment bill, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-154538914 data-indent-pad=\"  \"><span class=sf-dump-note>delete payment bill</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">delete payment bill</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-154538914\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.090661, "xdebug_link": null}, {"message": "[ability => edit debit note, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1474692854 data-indent-pad=\"  \"><span class=sf-dump-note>edit debit note</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">edit debit note</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1474692854\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.091117, "xdebug_link": null}, {"message": "[ability => manage pos, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1507843859 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1507843859\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.102704, "xdebug_link": null}, {"message": "[ability => show hrm dashboard, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>show hrm dashboard</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">show hrm dashboard</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.103025, "xdebug_link": null}, {"message": "[\n  ability => show account dashboard,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>show account dashboard</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">show account dashboard</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.103221, "xdebug_link": null}, {"message": "[\n  ability => show account dashboard,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>show account dashboard</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">show account dashboard</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.103376, "xdebug_link": null}, {"message": "[ability => income report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>income report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">income report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.103656, "xdebug_link": null}, {"message": "[ability => statement report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-349091628 data-indent-pad=\"  \"><span class=sf-dump-note>statement report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">statement report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-349091628\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.103945, "xdebug_link": null}, {"message": "[ability => invoice report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1064195814 data-indent-pad=\"  \"><span class=sf-dump-note>invoice report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">invoice report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1064195814\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.1042, "xdebug_link": null}, {"message": "[ability => bill report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1393573022 data-indent-pad=\"  \"><span class=sf-dump-note>bill report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">bill report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1393573022\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.104482, "xdebug_link": null}, {"message": "[ability => stock report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-684394765 data-indent-pad=\"  \"><span class=sf-dump-note>stock report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">stock report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-684394765\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.104793, "xdebug_link": null}, {"message": "[ability => loss & profit report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1681129005 data-indent-pad=\"  \"><span class=sf-dump-note>loss & profit report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">loss &amp; profit report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1681129005\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.105108, "xdebug_link": null}, {"message": "[ability => manage transaction, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-377264282 data-indent-pad=\"  \"><span class=sf-dump-note>manage transaction</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">manage transaction</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-377264282\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.105509, "xdebug_link": null}, {"message": "[ability => income report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2117968199 data-indent-pad=\"  \"><span class=sf-dump-note>income report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">income report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2117968199\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.105943, "xdebug_link": null}, {"message": "[ability => expense report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-41914368 data-indent-pad=\"  \"><span class=sf-dump-note>expense report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">expense report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-41914368\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.106325, "xdebug_link": null}, {"message": "[\n  ability => income vs expense report,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>income vs expense report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">income vs expense report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.106599, "xdebug_link": null}, {"message": "[ability => tax report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1707276502 data-indent-pad=\"  \"><span class=sf-dump-note>tax report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">tax report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1707276502\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.106858, "xdebug_link": null}, {"message": "[ability => show hrm dashboard, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>show hrm dashboard</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">show hrm dashboard</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.107055, "xdebug_link": null}, {"message": "[ability => manage report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-52432600 data-indent-pad=\"  \"><span class=sf-dump-note>manage report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">manage report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-52432600\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.107668, "xdebug_link": null}, {"message": "[ability => show pos dashboard, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1969285126 data-indent-pad=\"  \"><span class=sf-dump-note>show pos dashboard</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">show pos dashboard</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1969285126\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.107881, "xdebug_link": null}, {"message": "[ability => manage employee, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1406025346 data-indent-pad=\"  \"><span class=sf-dump-note>manage employee</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage employee</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1406025346\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.108805, "xdebug_link": null}, {"message": "[ability => manage set salary, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1680644494 data-indent-pad=\"  \"><span class=sf-dump-note>manage set salary</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">manage set salary</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1680644494\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.10933, "xdebug_link": null}, {"message": "[ability => manage set salary, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-779118598 data-indent-pad=\"  \"><span class=sf-dump-note>manage set salary</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">manage set salary</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-779118598\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.110078, "xdebug_link": null}, {"message": "[ability => manage pay slip, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1654099414 data-indent-pad=\"  \"><span class=sf-dump-note>manage pay slip</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage pay slip</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1654099414\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.110808, "xdebug_link": null}, {"message": "[ability => manage leave, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-286528418 data-indent-pad=\"  \"><span class=sf-dump-note>manage leave</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">manage leave</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-286528418\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.111453, "xdebug_link": null}, {"message": "[ability => manage leave, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1634048269 data-indent-pad=\"  \"><span class=sf-dump-note>manage leave</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">manage leave</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1634048269\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.112136, "xdebug_link": null}, {"message": "[ability => manage attendance, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1017469640 data-indent-pad=\"  \"><span class=sf-dump-note>manage attendance</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">manage attendance</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1017469640\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.112728, "xdebug_link": null}, {"message": "[ability => create attendance, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1238238403 data-indent-pad=\"  \"><span class=sf-dump-note>create attendance</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">create attendance</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1238238403\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.113304, "xdebug_link": null}, {"message": "[ability => manage award, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1158922330 data-indent-pad=\"  \"><span class=sf-dump-note>manage award</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">manage award</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1158922330\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.113968, "xdebug_link": null}, {"message": "[ability => manage award, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-773576184 data-indent-pad=\"  \"><span class=sf-dump-note>manage award</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">manage award</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-773576184\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.114451, "xdebug_link": null}, {"message": "[ability => manage transfer, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1651910588 data-indent-pad=\"  \"><span class=sf-dump-note>manage transfer</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage transfer</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1651910588\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.115021, "xdebug_link": null}, {"message": "[ability => manage resignation, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-46267870 data-indent-pad=\"  \"><span class=sf-dump-note>manage resignation</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">manage resignation</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-46267870\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.115503, "xdebug_link": null}, {"message": "[ability => manage travel, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>manage travel</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">manage travel</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.115994, "xdebug_link": null}, {"message": "[ability => manage promotion, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1332154560 data-indent-pad=\"  \"><span class=sf-dump-note>manage promotion</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">manage promotion</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1332154560\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.116493, "xdebug_link": null}, {"message": "[ability => manage complaint, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1473905846 data-indent-pad=\"  \"><span class=sf-dump-note>manage complaint</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">manage complaint</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1473905846\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.117236, "xdebug_link": null}, {"message": "[ability => manage warning, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1810219460 data-indent-pad=\"  \"><span class=sf-dump-note>manage warning</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">manage warning</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1810219460\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.11804, "xdebug_link": null}, {"message": "[ability => manage termination, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1224013326 data-indent-pad=\"  \"><span class=sf-dump-note>manage termination</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">manage termination</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1224013326\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.118643, "xdebug_link": null}, {"message": "[ability => manage announcement, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-622084309 data-indent-pad=\"  \"><span class=sf-dump-note>manage announcement</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">manage announcement</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-622084309\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.119266, "xdebug_link": null}, {"message": "[ability => manage holiday, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1472257460 data-indent-pad=\"  \"><span class=sf-dump-note>manage holiday</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">manage holiday</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1472257460\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.119842, "xdebug_link": null}, {"message": "[ability => manage document, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>manage document</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage document</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.120283, "xdebug_link": null}, {"message": "[ability => manage company policy, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1087121057 data-indent-pad=\"  \"><span class=sf-dump-note>manage company policy</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">manage company policy</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1087121057\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.12094, "xdebug_link": null}, {"message": "[ability => manage customer, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>manage customer</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage customer</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.121691, "xdebug_link": null}, {"message": "[ability => manage bank account, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>manage bank account</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">manage bank account</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.122125, "xdebug_link": null}, {"message": "[ability => manage customer, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>manage customer</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage customer</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.122519, "xdebug_link": null}, {"message": "[ability => manage customer, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2058365919 data-indent-pad=\"  \"><span class=sf-dump-note>manage customer</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage customer</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2058365919\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.122847, "xdebug_link": null}, {"message": "[ability => manage proposal, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1991605720 data-indent-pad=\"  \"><span class=sf-dump-note>manage proposal</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage proposal</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1991605720\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.123287, "xdebug_link": null}, {"message": "[ability => manage vender, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-994816952 data-indent-pad=\"  \"><span class=sf-dump-note>manage vender</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">manage vender</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-994816952\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.123608, "xdebug_link": null}, {"message": "[ability => manage vender, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>manage vender</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">manage vender</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.12393, "xdebug_link": null}, {"message": "[\n  ability => manage chart of account,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>manage chart of account</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">manage chart of account</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.124516, "xdebug_link": null}, {"message": "[ability => manage goal, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>manage goal</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">manage goal</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.125158, "xdebug_link": null}, {"message": "[ability => manage constant tax, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1971088942 data-indent-pad=\"  \"><span class=sf-dump-note>manage constant tax</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">manage constant tax</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1971088942\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.125458, "xdebug_link": null}, {"message": "[ability => manage print settings, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>manage print settings</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">manage print settings</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.125767, "xdebug_link": null}, {"message": "[ability => manage user, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1608926580 data-indent-pad=\"  \"><span class=sf-dump-note>manage user</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">manage user</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1608926580\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.126021, "xdebug_link": null}, {"message": "[ability => manage user, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-940730552 data-indent-pad=\"  \"><span class=sf-dump-note>manage user</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">manage user</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-940730552\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.126237, "xdebug_link": null}, {"message": "[ability => manage role, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-209336722 data-indent-pad=\"  \"><span class=sf-dump-note>manage role</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">manage role</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-209336722\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.126509, "xdebug_link": null}, {"message": "[ability => manage client, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-346128904 data-indent-pad=\"  \"><span class=sf-dump-note>manage client</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">manage client</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-346128904\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.127052, "xdebug_link": null}, {"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1345896649 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1345896649\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.12742, "xdebug_link": null}, {"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-889068460 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-889068460\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.12779, "xdebug_link": null}, {"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-604697322 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-604697322\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.128179, "xdebug_link": null}, {"message": "[ability => show warehouse, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-667832606 data-indent-pad=\"  \"><span class=sf-dump-note>show warehouse</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">show warehouse</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-667832606\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.12929, "xdebug_link": null}, {"message": "[ability => manage pos, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-216875576 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-216875576\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.130569, "xdebug_link": null}, {"message": "[ability => show pos, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-219334524 data-indent-pad=\"  \"><span class=sf-dump-note>show pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"8 characters\">show pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-219334524\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.131889, "xdebug_link": null}, {"message": "[ability => create barcode, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2084066384 data-indent-pad=\"  \"><span class=sf-dump-note>create barcode</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">create barcode</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2084066384\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.133158, "xdebug_link": null}, {"message": "[ability => show financial record, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1008938071 data-indent-pad=\"  \"><span class=sf-dump-note>show financial record</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">show financial record</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1008938071\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.134483, "xdebug_link": null}, {"message": "[ability => manage warehouse, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-326115510 data-indent-pad=\"  \"><span class=sf-dump-note>manage warehouse</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">manage warehouse</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-326115510\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.135728, "xdebug_link": null}, {"message": "[ability => show financial record, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2017348630 data-indent-pad=\"  \"><span class=sf-dump-note>show financial record</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">show financial record</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2017348630\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.136888, "xdebug_link": null}, {"message": "[ability => show financial record, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1389427322 data-indent-pad=\"  \"><span class=sf-dump-note>show financial record</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">show financial record</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1389427322\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.137677, "xdebug_link": null}, {"message": "[ability => manage delevery, result => null, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1223365567 data-indent-pad=\"  \"><span class=sf-dump-note>manage delevery</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage delevery</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1223365567\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.139089, "xdebug_link": null}, {"message": "[ability => manage company plan, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2045284158 data-indent-pad=\"  \"><span class=sf-dump-note>manage company plan</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">manage company plan</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2045284158\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.139865, "xdebug_link": null}, {"message": "[\n  ability => manage company settings,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>manage company settings</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">manage company settings</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.14005, "xdebug_link": null}, {"message": "[ability => manage company plan, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>manage company plan</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">manage company plan</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.140707, "xdebug_link": null}, {"message": "[ability => manage order, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1733007294 data-indent-pad=\"  \"><span class=sf-dump-note>manage order</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">manage order</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1733007294\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.140973, "xdebug_link": null}]}, "session": {"_token": "G4oUC4dvL91DvSdXtTSUa87JqfM4sfNNzjOiK6ns", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/bill/eyJpdiI6IlhwTTVrTG03L25NelZRV0dEbnFMV2c9PSIsInZhbHVlIjoiM202QWM5RGRFWllQbWE5NU0zZGUxUT09IiwibWFjIjoiMzBlMTkzNTRiODk3ZWZiM2UxYzJlOGI2MTE2YTJiOWI2YjkyYzQ5ZTQ4ZmYxYTcyNzE5YTk3MzliOGFlNzRjZCIsInRhZyI6IiJ9\"\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/bill/eyJpdiI6IlhwTTVrTG03L25NelZRV0dEbnFMV2c9PSIsInZhbHVlIjoiM202QWM5RGRFWllQbWE5NU0zZGUxUT09IiwibWFjIjoiMzBlMTkzNTRiODk3ZWZiM2UxYzJlOGI2MTE2YTJiOWI2YjkyYzQ5ZTQ4ZmYxYTcyNzE5YTk3MzliOGFlNzRjZCIsInRhZyI6IiJ9", "status_code": "<pre class=sf-dump id=sf-dump-631845085 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-631845085\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1492400926 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1492400926\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-101279653 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-101279653\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1526545998 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost/bill</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1ap6d1q%7C2%7Cfx3%7C0%7C1998; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clsk=p74v85%7C1750953505703%7C80%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImtpclhJMmZLTE9QYmR1eWk3cHcwUWc9PSIsInZhbHVlIjoiL3pkQnQ2WW5RNTd1dFZhMTFmeklIRWszNkdrTzVwbVFyUmRrMDRSWVFuSXFndkM1T3ZQQkswaFBicE5aRDVIemlKNW11SFFTVjdQaStIWVA0SzE3TGc0aWZrMnN3NGord2RhNFM5UDFZcVhlU1lBeUs0eEplS3VqZU5XdjZkVzk1VmRiSGhUOGRyWXRjU0tCWjdwRnoyM2VseVA1QUdJZzh6Uk9RdnMrT3NBUDYzOFdER2d2SHpaQmJZZ01pYVRlSnhWWnBKR2xPaGJYdkt0SzMyNTllNkFvZHNCNXUwaFF2L2lHalpzakF0MTBveDUvWDdFYmhvQnBWZ1NRRmVwZEtybjNCOTRzTWNDWTVXbzZ5UjhjTS9iczZac2lXZnRUUVMzeFFtcU1ZQ0k5bjNuV25zYU5FTWxueWpGNS82YmYweXRUWFB3VllDWnVlalZDT0Z3bTN0elBkc2RKaDZsaFloVjRUeW4xUTk3RERsL1M0U05FeUhPUEo1N0RSNXB0dk1ORzlXSkdrTHNRWEFKVFJ4ellzTEJWMnFhU0pMemtXRlhMb3hBV21tTEViT2pDTyt6bVdrclB1SEtuZ1dGN0JvaHk4aFZiZGI5dHNhR0VJeGpFblVqOFFhallMRjVBUHR6bVZ5Q0xkNlpnZDg4QU84eVc2NFlvbW1TWktobm4iLCJtYWMiOiI3NjkxODMwMTA3ZTJkNTdhNWNhMDZhYzBhYTkwMGVmYTEzZDhhYjUzNWM3NGVmYmEzZTNkMTRlNDMxOTlhMmE5IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkZ2RXBkSis2UStqclRyWUZqaXFyckE9PSIsInZhbHVlIjoibTVYRlBBNWEzeU1uSmxXMHN2RTIrK3dubGdSTkF2VTRveXp2Z1paU3NjaVdjU3RmWnRHbG1mZVpIVThDelBQb3AyZzlpMHZmQnByKytxM2d3M2I4M1RTMXhDWnZqTzZvVmI3TXFZN0NkZ3gzM1NObHYxOEZxYjYzRHpaRlN4SWxGWTBGMUI0Nms1N0V2NlVZNDFadk9kV3ZZTnFJRk5DRHJieGFBK0lKQjlPVVY5NmwybVdxTmxtYkpMeDNxVGtSOXcwdVlidGZpdDJsVkcvK0M4eTFaYmxoV0J0RnNTUklGOFl3RVlBb2QxUWVibUhBeDcwaW9FU2ZvMEhrZ1Znb3FiMjNvQTJ6Wjl1Tmdyb25RVmozb0VuSy9nTVVEeVlKVzJ3eGQ3WUNWdnZ4ZWQrZWI3K01uUWVOZTlkUEEyMU9rN0dKVzllYWRZM201SU14TU0reHpPWmQ0VmQ0ZHlpbEZjWG93eGRaS0s2QjRYRzVWeG5Xdk5yU2Q4R3o0cWFISU1YWm1TMlNEaGtDRWNkalBLMHc2MFg2blZBYUNsY295SDlwSFBINlN2Tmg0U2J3OHRFdXBmaEQ2UitZWVdpZGpBcU51TlRoMUQzNTliaWF1MVRxVHN0cGpOVjhMOWRGRVo3aVB4bWJ4bFhsU2dCK2xuZmpaU2h2cDMwWlE4WjQiLCJtYWMiOiI3NTMzZjc3YWNhYjYzMGQwMzMyMzUzYjJmOWVlOTNjYzQ4MzcyYzlhZmVjNTA4NzQxNjMzMjlkMjU2NzE0MDA0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1526545998\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-618414419 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G4oUC4dvL91DvSdXtTSUa87JqfM4sfNNzjOiK6ns</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">16z2m35FIhU8zNuOqkDvqLO3UyIjTT7e4c1Y0Zqf</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-618414419\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 15:58:28 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Iko5K25BdmVPdW9JY0tFU0lwNCtFcUE9PSIsInZhbHVlIjoiK1pFT0RCa0lqZ1hybHV4NHFxbU93cGxCakx0VytaVGxjeGdvbnRxUW1ucEhHS05PbEdiR08xUWlMbGR4cHRqazJ0Z2t5cEc2NklhZkQ3eXNzVTNFZFFWbXROdUx0bDFHQkwvd0pwaHlBemUwSjhwNHhKUWJWM3Q2TTduamxVQ3UxeTJqSUZjckdGbmdoTTB4eUVrT1VOTDdCaDJIR1ZUMisrRGl6WFI4ZVBoS1NLVTViSHgxcnBkUTgwY29TTUh5SnR0L3VaM3FHaXZGK3k5THJWbXhBUkdFOE44c0c0Y1RTSk4xSElRMlFHeUlYRUd4NXFEOFNhR3JaV29qSUlDdy9mRFYxWWFDU0Q5ZFdna0h1R2t3elUwbHo1UXRDakRZR1dhSE00WDNLcHdKcERFQnlpWkt6NVNuQUpFQ2VxZlp2bUdjeVhPZjcyREwvcWNraXJHWmZjdUg4TC9QT2cvalI2OWNUUjZGeW4xODFXanQrMFhtVnl5NjBsdkd0V2t2ZTMxcUNBMGtaTitxMG4vbUFJWUE5RC9QRzhheWYzS2V6eUsyRUtFOTkzSmtOVmFVYlhSZzFFa3ZFU0hzbHFPMUJxRmh1am5KQXFlMllSVHprV3dyMkdZeXYzR09VMEwvSjRFdG11SGUzaVcveGlDeUZTYWFMUWRWZU95WE1QWU0iLCJtYWMiOiIzZTM4YWI4NmFhODZkYjdmZjUxZDUzZGU5Y2Q0MDRjNzJhNDIyODBhMTBlNDE1MTQwOTc2MGVmZjhhMGQ1MzgzIiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 17:58:29 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjlMcTY0eFlZaHBuYXYyQ2NISnVXSVE9PSIsInZhbHVlIjoiblpYVDRIZlRHWVgyMjBBYVJNbm14RUt4MlFPU3MzR2EwaFNLWmk2dllTUjNEaUpEWXJudFhnT09lanJ6bktpZEs1RG9qYlVnTVArdTZ1RWp6djhMdTlKWE1QOGtsL1ZGWkh3eEUwOCswME5ERFY0NGZzM3grSFcwalBKL0hFTVJ6bGhhQzd4VFpzS3lhT0g1cHp5SHRTRW1GV0d5dTh4M242Z1dFZjNyYXUrVUN0eDFwNUFOZDJ6a0pYZGtIUnBnTWpGVklhTFFhUzd0SGRiSnBsVkdrNEpJNk04Mk5NY1NEYUZmb0Rxd28yV2h3MjdUNHhqYzlRUnk2KzN6dmVuMnlyTSt6SHBmeE8zRGJRY3BCczVpOTFZMmphVGFSQTVPckplNDNzOURpRVFUak0veTlSSHEwMjEvZkRrOEJDRnFoMEpEVjZtM2k4THVTM0FoUGdjTWxseHc3RVZab0ZMc0NMdGJ3OWZmUURlVmxWdFVpZFo0VzcvSFdnbktEZ0RuVWlaNzNMU3RQUVFYbkxldlZQWW1aOWZLVGovQjdNcmg3K3ZSNFZ1dnkwM2xxZzY1aEVjY2Y0SGlJQ1FoaFhqSEUyWjFsRWhMWjJLS3NEeTArT21FWGhxbDlxV0pCQWJPSXRCRkUxbmhTaTRNOE42cStxMkhGRWxqZjJldk9lQ0QiLCJtYWMiOiJmNDNiYjY4N2RiOTgzNWU3ZjczZjIwYjI3ZGUwYWY5M2NmMDI2MWI2MzhkZjMxYjViYTM2NjcyMjlhZGIzZTU3IiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 17:58:29 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Iko5K25BdmVPdW9JY0tFU0lwNCtFcUE9PSIsInZhbHVlIjoiK1pFT0RCa0lqZ1hybHV4NHFxbU93cGxCakx0VytaVGxjeGdvbnRxUW1ucEhHS05PbEdiR08xUWlMbGR4cHRqazJ0Z2t5cEc2NklhZkQ3eXNzVTNFZFFWbXROdUx0bDFHQkwvd0pwaHlBemUwSjhwNHhKUWJWM3Q2TTduamxVQ3UxeTJqSUZjckdGbmdoTTB4eUVrT1VOTDdCaDJIR1ZUMisrRGl6WFI4ZVBoS1NLVTViSHgxcnBkUTgwY29TTUh5SnR0L3VaM3FHaXZGK3k5THJWbXhBUkdFOE44c0c0Y1RTSk4xSElRMlFHeUlYRUd4NXFEOFNhR3JaV29qSUlDdy9mRFYxWWFDU0Q5ZFdna0h1R2t3elUwbHo1UXRDakRZR1dhSE00WDNLcHdKcERFQnlpWkt6NVNuQUpFQ2VxZlp2bUdjeVhPZjcyREwvcWNraXJHWmZjdUg4TC9QT2cvalI2OWNUUjZGeW4xODFXanQrMFhtVnl5NjBsdkd0V2t2ZTMxcUNBMGtaTitxMG4vbUFJWUE5RC9QRzhheWYzS2V6eUsyRUtFOTkzSmtOVmFVYlhSZzFFa3ZFU0hzbHFPMUJxRmh1am5KQXFlMllSVHprV3dyMkdZeXYzR09VMEwvSjRFdG11SGUzaVcveGlDeUZTYWFMUWRWZU95WE1QWU0iLCJtYWMiOiIzZTM4YWI4NmFhODZkYjdmZjUxZDUzZGU5Y2Q0MDRjNzJhNDIyODBhMTBlNDE1MTQwOTc2MGVmZjhhMGQ1MzgzIiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 17:58:29 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjlMcTY0eFlZaHBuYXYyQ2NISnVXSVE9PSIsInZhbHVlIjoiblpYVDRIZlRHWVgyMjBBYVJNbm14RUt4MlFPU3MzR2EwaFNLWmk2dllTUjNEaUpEWXJudFhnT09lanJ6bktpZEs1RG9qYlVnTVArdTZ1RWp6djhMdTlKWE1QOGtsL1ZGWkh3eEUwOCswME5ERFY0NGZzM3grSFcwalBKL0hFTVJ6bGhhQzd4VFpzS3lhT0g1cHp5SHRTRW1GV0d5dTh4M242Z1dFZjNyYXUrVUN0eDFwNUFOZDJ6a0pYZGtIUnBnTWpGVklhTFFhUzd0SGRiSnBsVkdrNEpJNk04Mk5NY1NEYUZmb0Rxd28yV2h3MjdUNHhqYzlRUnk2KzN6dmVuMnlyTSt6SHBmeE8zRGJRY3BCczVpOTFZMmphVGFSQTVPckplNDNzOURpRVFUak0veTlSSHEwMjEvZkRrOEJDRnFoMEpEVjZtM2k4THVTM0FoUGdjTWxseHc3RVZab0ZMc0NMdGJ3OWZmUURlVmxWdFVpZFo0VzcvSFdnbktEZ0RuVWlaNzNMU3RQUVFYbkxldlZQWW1aOWZLVGovQjdNcmg3K3ZSNFZ1dnkwM2xxZzY1aEVjY2Y0SGlJQ1FoaFhqSEUyWjFsRWhMWjJLS3NEeTArT21FWGhxbDlxV0pCQWJPSXRCRkUxbmhTaTRNOE42cStxMkhGRWxqZjJldk9lQ0QiLCJtYWMiOiJmNDNiYjY4N2RiOTgzNWU3ZjczZjIwYjI3ZGUwYWY5M2NmMDI2MWI2MzhkZjMxYjViYTM2NjcyMjlhZGIzZTU3IiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 17:58:29 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G4oUC4dvL91DvSdXtTSUa87JqfM4sfNNzjOiK6ns</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"222 characters\">http://localhost/bill/eyJpdiI6IlhwTTVrTG03L25NelZRV0dEbnFMV2c9PSIsInZhbHVlIjoiM202QWM5RGRFWllQbWE5NU0zZGUxUT09IiwibWFjIjoiMzBlMTkzNTRiODk3ZWZiM2UxYzJlOGI2MTE2YTJiOWI2YjkyYzQ5ZTQ4ZmYxYTcyNzE5YTk3MzliOGFlNzRjZCIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}