{"__meta": {"id": "X561d2e411904d50e6fd92e6f95e4eb4f", "datetime": "2025-06-26 16:04:31", "utime": **********.786768, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.280064, "end": **********.786789, "duration": 0.5067248344421387, "duration_str": "507ms", "measures": [{"label": "Booting", "start": **********.280064, "relative_start": 0, "end": **********.704909, "relative_end": **********.704909, "duration": 0.42484498023986816, "duration_str": "425ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.70492, "relative_start": 0.42485594749450684, "end": **********.78679, "relative_end": 9.5367431640625e-07, "duration": 0.08186984062194824, "duration_str": "81.87ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46189280, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.022310000000000003, "accumulated_duration_str": "22.31ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 17 limit 1", "type": "query", "params": [], "bindings": ["17"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.733735, "duration": 0.021, "duration_str": "21ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 94.128}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.764738, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 94.128, "width_percent": 2.241}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (17) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.7722762, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 96.369, "width_percent": 3.631}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PKqZteC3IabV2rdCxsXBMLnTb72fzH4qdR2KdQSn", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "17", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-483319758 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-483319758\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-750770231 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-750770231\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1660610365 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PKqZteC3IabV2rdCxsXBMLnTb72fzH4qdR2KdQSn</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1660610365\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1016729492 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1bowxa%7C2%7Cfx3%7C0%7C1999; _clsk=1osc4j2%7C1750953855370%7C1%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InY5UCtLQm9TYzV4UlpnKzdiSHNtd0E9PSIsInZhbHVlIjoiZlFnd0I5TTBwSnlpK3dYd3owODdja3lCNW81eWgrOGZXd01mMmgwQnNUd3VCK3NiV3psMHZvc2hIblhqY2lUZTYwcTh2emFQYnordFAxSFJtR0ozR3Bmckc3eDY3N2NiM2JocFppOHpHdkZ0bFFCb2RlbVB0K2hIcTQ0MzNORWxCYVRGL0lnb0JkcmI5Y0hld2RLK0ZjVkk3YmdwSmxrQzBQQWUwQ0NJelNrQjVhSEtlRDJhVmErZzBZeDlYN3dFNHIzRVNVTzljL3FEZWF0QjlkMTFYeG0wWUcyWHFnMWh6akFlMkFBdFRCQ1pISHd6V1lXRXBkUFZoWmRtajZTSml3QlRSck9RSlhraytaMFBER29kY2pOcDZaaVdKV1NQWU5tUncweVVFUlVOdGU3cnEvU29CMUxxSWlBdjhzdytGUVRJRDNXMHNyNEthNUJ1a2ZVM2U5VGJXd0dnNXRtV04yTlZQS3pOZkhDVVB5bkNpdkRPeEg4VDJQdnVJTGdCejVORjZvT2RGdFcrTmRIbzNrZGJBUFQ5dDhGZGhkT2JrOXdQaG1JdzJEL1UxK0Q3alpIVVZPL3FsVU1iSjRtblByMmdFaTR2U3J1WnplaUNjc1MycHZJTEd1SFQyaDJRdUR4ZUtxTE1adnZkSU1MRXFwYWpOak5qdS9hRnhHQysiLCJtYWMiOiJhNjNjOGExZjgyNGVjMWJmNDk2NDdhM2I2MDU2ZTIzMWEwZTg5MDIxOGExMTgzNWEzMDcwMGFjNGI3MDEyZDg3IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IndsQUZ6SVQ2SEIxZ014QU9iRlRVVFE9PSIsInZhbHVlIjoiRXJoVnNwaEJzQnhXQW93L21QSVlxYnFwK2k4ZW1Mdi9vcEZBZk1xMkJONm1wK2tSdEFBR3l3YjJkcnBkMnpJVnhnNGRWS1ZRcWZ1V01oc0U4N251VWJxcWs2bjlGQlVtMkw4WlZnbVJKWU1oZkI5SlFsbEtkRXVkZFh6dTNMU09Jcng3Z3VnQVIyYTdNODdXbU16bHhGV0VObGk2d1JOekVOOXYySVhqMjRiaDdFOU1nVTE0L29xam42QWM0N2RmMWpZSjlIdDZOY3RYTE0yV1NMdWkweWZId1A0R0ZZRHdqYWI2VU5LT3pyVDJwTTNZaHJxODBtTUNpL2R2UTVEUzQ5V1JrRGRFdDBSMmorZEpaY1QwMVRyajQ2VDdHcHNud014TE9pM3p6ZE5EaTExMURPU2MzU3MvbmQ0bWYrbHFTTmNnb1hpRTYra1FHc2lzaElLMGJwR3d1VTBFOVhJNnlsRktWV1dBWHc2cEhteEdoaitERS8zMytxZ2FKeWgwT0JtSy8rSGpXSGc3eHBYNkE3YWUzVTJNalBoV1luMHVYbnpOTmUzaFBlNkJIc3l5VXpPd2dkZVlBRGRlMk5GNm1wNzMxVHkrRDQwMlpjcUJaWkNaZmdHK3BRclh1Z2E2TGhaNDFxaXlUajdWMXMrcUtYMHBvSzlyOWlqT3RTY24iLCJtYWMiOiJiZjNhNDNlNmIyYWQwOTE5NTQwN2U2MGM3NWVmM2FhYzMxNTUxODQwMTU4ZWY1MDYzMWY4ZDllMmQ2NWU4ODI1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1016729492\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1211024963 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PKqZteC3IabV2rdCxsXBMLnTb72fzH4qdR2KdQSn</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GUUMfjgJKgBqP5oaDcRP4Rfu0FM4k6V9zWwhkyoG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1211024963\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-923052851 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 16:04:31 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImQ4SndDTkNNZERZUWJLUVpRM2FNMFE9PSIsInZhbHVlIjoiZ09ZOVJzTDlMbUVsc0dPdmtMZkNrTHd5TTU1aHMveWlrWmVLNlA4eGQyS2NxcjFvSFJUbER2bWZlRzVGNVVjZUtVMFpLRVNDM3JEeWxZSXgxd3JrZTRzMEI2alJNajNlaGJmR3RVRmtDZTEvazRiRnROOVpCTXBHNG5BWlhReGovMGJjcGN0Y2tkOHlkQTNuVXhjUzBkVmtiK2xtWkprRmZJazdIRlh5RWluM2F4aXZ3SEhscUhJb2lIZXVMT3l3Vjh5K2VudFZjTk5WOWV0cHVBTVVlN202cG1YUUNuUnQraTA1WkJFTk4wc1RHTmJrVW5IZlluM2JUUjdZenNuUDRySXVqbUxDejBycDQvVWJ0QlpQdHk3MXRPQTRQMlJURHdSMGlzcGJMd0xYNEx4emE0RjZudXluUFlFTmhKQXpDVDFCeXYvM1pkd09abG1XWU1IR1RBZEplM1NVR29nYk5uWFRQUmQwOGt2VUdFdGlla0pWOGZoWnFjcVRSYmgvdFF4WGFITkwyRUl3SDFTMG1JMU5UOXZsVXhuSWpVeHZUTUFuYmFlQjZONDloTWZueW9DN0s2cU5tSnhuNm5aUFlFTjk2WDBJSm03elNaOG9KNUdheXVyc1BVNnpjZmlIck1nZWRyTHViZWtoek1Ud1RFOXNUMEJiWG5XVDNsTFkiLCJtYWMiOiI1OWVjNGQyMGRiNWIzNjljZDI2NmIxMjVhYzk4MWY2MmNjMDlhMWVkZTBiMmZkMzQzMDU4MGU5NDc4YWMxMTZjIiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 18:04:31 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjlmWWNMK1czbHViNTRoaFR5eTZSWVE9PSIsInZhbHVlIjoiSk01WmtUOFhYdjBiODBqeGVaVVdNV0FTOHpjSWM4K1l1OC9QdE1OR0k5TVBmamJtcXczQXF0QnVMVW1JNnZWUWVtdEpQRzVKWHYzMTV4S2svOHczcjFxdXhCSXRDN1h6eVlNazEyditKcDk3THlkL1ppMkExWWU1N3M5YWZ1TDNmRE9mYnpWdUxxMjI2ZlNsL1dSemRJOXNveXJnK2pLWVlRdG9MUlNxLzFvT1lCMWdHZnE3ZW9FSmd1Yk1aL1NPL3Q5VXZYZ05MWitPWGZlOVdhaEhVbnJBd0ZlNTZsSDBpNGY1cXQxSVF5bktvRTBlV05iQWJFVm1XMTMvcDJucFlzWW1mU2ZyYldRaHhpOFdVbURURFZFTlVaSHFtVXloL3dWd1JXUnF4ckdSUHZJNWM1SjNyWG45TFF5a0JVQVpyRVNId056OHVybmMxcmxSTGVHMmRtOXBvWnNqcmlsdEMyZHB5dzcwSjBGU3RscWlCUjl4ZkRwWDdMLytlR1dXemxkbkczcmZEMlo5bWJWaW5YSERycmdCVHRCNi9RMzhrTnkrRWtuaE9CMGpBZVF4L2Q5M2pyQnBjdS9MaWlmSzNseEV0dmJiQnRKS3B0QnErZzNzNDJpSktHUmp4VS95dnhKcUw4TEtSVTlISGF0OUZhUDJSL2drdzZwWVlFTzUiLCJtYWMiOiIwZTBiOTIzMWE0MDNkMzA2NTAyZDJjMDk2YjJiMjEyYjVlMDIzZjJlYmIzN2VjOTRiYWY5NjlmOGVjNzc3MDI1IiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 18:04:31 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImQ4SndDTkNNZERZUWJLUVpRM2FNMFE9PSIsInZhbHVlIjoiZ09ZOVJzTDlMbUVsc0dPdmtMZkNrTHd5TTU1aHMveWlrWmVLNlA4eGQyS2NxcjFvSFJUbER2bWZlRzVGNVVjZUtVMFpLRVNDM3JEeWxZSXgxd3JrZTRzMEI2alJNajNlaGJmR3RVRmtDZTEvazRiRnROOVpCTXBHNG5BWlhReGovMGJjcGN0Y2tkOHlkQTNuVXhjUzBkVmtiK2xtWkprRmZJazdIRlh5RWluM2F4aXZ3SEhscUhJb2lIZXVMT3l3Vjh5K2VudFZjTk5WOWV0cHVBTVVlN202cG1YUUNuUnQraTA1WkJFTk4wc1RHTmJrVW5IZlluM2JUUjdZenNuUDRySXVqbUxDejBycDQvVWJ0QlpQdHk3MXRPQTRQMlJURHdSMGlzcGJMd0xYNEx4emE0RjZudXluUFlFTmhKQXpDVDFCeXYvM1pkd09abG1XWU1IR1RBZEplM1NVR29nYk5uWFRQUmQwOGt2VUdFdGlla0pWOGZoWnFjcVRSYmgvdFF4WGFITkwyRUl3SDFTMG1JMU5UOXZsVXhuSWpVeHZUTUFuYmFlQjZONDloTWZueW9DN0s2cU5tSnhuNm5aUFlFTjk2WDBJSm03elNaOG9KNUdheXVyc1BVNnpjZmlIck1nZWRyTHViZWtoek1Ud1RFOXNUMEJiWG5XVDNsTFkiLCJtYWMiOiI1OWVjNGQyMGRiNWIzNjljZDI2NmIxMjVhYzk4MWY2MmNjMDlhMWVkZTBiMmZkMzQzMDU4MGU5NDc4YWMxMTZjIiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 18:04:31 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjlmWWNMK1czbHViNTRoaFR5eTZSWVE9PSIsInZhbHVlIjoiSk01WmtUOFhYdjBiODBqeGVaVVdNV0FTOHpjSWM4K1l1OC9QdE1OR0k5TVBmamJtcXczQXF0QnVMVW1JNnZWUWVtdEpQRzVKWHYzMTV4S2svOHczcjFxdXhCSXRDN1h6eVlNazEyditKcDk3THlkL1ppMkExWWU1N3M5YWZ1TDNmRE9mYnpWdUxxMjI2ZlNsL1dSemRJOXNveXJnK2pLWVlRdG9MUlNxLzFvT1lCMWdHZnE3ZW9FSmd1Yk1aL1NPL3Q5VXZYZ05MWitPWGZlOVdhaEhVbnJBd0ZlNTZsSDBpNGY1cXQxSVF5bktvRTBlV05iQWJFVm1XMTMvcDJucFlzWW1mU2ZyYldRaHhpOFdVbURURFZFTlVaSHFtVXloL3dWd1JXUnF4ckdSUHZJNWM1SjNyWG45TFF5a0JVQVpyRVNId056OHVybmMxcmxSTGVHMmRtOXBvWnNqcmlsdEMyZHB5dzcwSjBGU3RscWlCUjl4ZkRwWDdMLytlR1dXemxkbkczcmZEMlo5bWJWaW5YSERycmdCVHRCNi9RMzhrTnkrRWtuaE9CMGpBZVF4L2Q5M2pyQnBjdS9MaWlmSzNseEV0dmJiQnRKS3B0QnErZzNzNDJpSktHUmp4VS95dnhKcUw4TEtSVTlISGF0OUZhUDJSL2drdzZwWVlFTzUiLCJtYWMiOiIwZTBiOTIzMWE0MDNkMzA2NTAyZDJjMDk2YjJiMjEyYjVlMDIzZjJlYmIzN2VjOTRiYWY5NjlmOGVjNzc3MDI1IiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 18:04:31 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-923052851\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1338082883 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PKqZteC3IabV2rdCxsXBMLnTb72fzH4qdR2KdQSn</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>17</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1338082883\", {\"maxDepth\":0})</script>\n"}}