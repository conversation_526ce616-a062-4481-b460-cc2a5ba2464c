{"__meta": {"id": "X02bcc12b0c0417369edd93be6a44ff29", "datetime": "2025-06-26 16:00:25", "utime": **********.171811, "method": "GET", "uri": "/login", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750953624.756644, "end": **********.171824, "duration": 0.415179967880249, "duration_str": "415ms", "measures": [{"label": "Booting", "start": 1750953624.756644, "relative_start": 0, "end": **********.090269, "relative_end": **********.090269, "duration": 0.33362507820129395, "duration_str": "334ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.090278, "relative_start": 0.3336338996887207, "end": **********.171825, "relative_end": 9.5367431640625e-07, "duration": 0.08154702186584473, "duration_str": "81.55ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46564096, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 4, "templates": [{"name": "1x auth.login", "param_count": null, "params": [], "start": **********.144413, "type": "blade", "hash": "bladeC:\\laragon\\www\\ERPcopy\\public_html\\resources\\views/auth/login.blade.phpauth.login", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fresources%2Fviews%2Fauth%2Flogin.blade.php&line=1", "ajax": false, "filename": "login.blade.php", "line": "?"}, "render_count": 1, "name_original": "auth.login"}, {"name": "1x layouts.auth", "param_count": null, "params": [], "start": **********.14871, "type": "blade", "hash": "bladeC:\\laragon\\www\\ERPcopy\\public_html\\resources\\views/layouts/auth.blade.phplayouts.auth", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fresources%2Fviews%2Flayouts%2Fauth.blade.php&line=1", "ajax": false, "filename": "auth.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.auth"}, {"name": "1x landingpage::layouts.buttons", "param_count": null, "params": [], "start": **********.163975, "type": "blade", "hash": "bladeC:\\laragon\\www\\ERPcopy\\public_html\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.phplandingpage::layouts.buttons", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2FModules%2FLandingPage%2FResources%2Fviews%2Flayouts%2Fbuttons.blade.php&line=1", "ajax": false, "filename": "buttons.blade.php", "line": "?"}, "render_count": 1, "name_original": "landingpage::layouts.buttons"}, {"name": "1x layouts.cookie_consent", "param_count": null, "params": [], "start": **********.16658, "type": "blade", "hash": "bladeC:\\laragon\\www\\ERPcopy\\public_html\\resources\\views/layouts/cookie_consent.blade.phplayouts.cookie_consent", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fresources%2Fviews%2Flayouts%2Fcookie_consent.blade.php&line=1", "ajax": false, "filename": "cookie_consent.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.cookie_consent"}]}, "route": {"uri": "GET login/{lang?}", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@showLoginForm", "namespace": null, "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=344\" onclick=\"\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:344-359</a>"}, "queries": {"nb_statements": 9, "nb_failed_statements": 0, "accumulated_duration": 0.022240000000000006, "accumulated_duration_str": "22.24ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 555}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 348}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.1144, "duration": 0.01726, "duration_str": "17.26ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 77.608}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'kdmkjkqknb' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 537}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.133264, "duration": 0.00263, "duration_str": "2.63ms", "memory": 0, "memory_str": null, "filename": "Utility.php:537", "source": "app/Models/Utility.php:537", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUtility.php&line=537", "ajax": false, "filename": "Utility.php", "line": "537"}, "connection": "kdmkjkqknb", "start_percent": 77.608, "width_percent": 11.826}, {"sql": "select `full_name`, `code` from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 543}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.137626, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "Utility.php:543", "source": "app/Models/Utility.php:543", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUtility.php&line=543", "ajax": false, "filename": "Utility.php", "line": "543"}, "connection": "kdmkjkqknb", "start_percent": 89.433, "width_percent": 0.719}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 4748}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 4682}, {"index": 15, "namespace": "view", "name": "auth.login", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\resources\\views/auth/login.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.1448739, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4748", "source": "app/Models/Utility.php:4748", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUtility.php&line=4748", "ajax": false, "filename": "Utility.php", "line": "4748"}, "connection": "kdmkjkqknb", "start_percent": 90.153, "width_percent": 1.124}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 4748}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 4682}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\resources\\views/layouts/auth.blade.php", "line": 10}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.14956, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4748", "source": "app/Models/Utility.php:4748", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUtility.php&line=4748", "ajax": false, "filename": "Utility.php", "line": "4748"}, "connection": "kdmkjkqknb", "start_percent": 91.277, "width_percent": 1.484}, {"sql": "select * from `users` where `type` = 'super admin' limit 1", "type": "query", "params": [], "bindings": ["super admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 4113}, {"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 4155}, {"index": 18, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.1571522, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4113", "source": "app/Models/Utility.php:4113", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUtility.php&line=4113", "ajax": false, "filename": "Utility.php", "line": "4113"}, "connection": "kdmkjkqknb", "start_percent": 92.761, "width_percent": 2.158}, {"sql": "select `value`, `name` from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 4114}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 4155}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.1595001, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4114", "source": "app/Models/Utility.php:4114", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUtility.php&line=4114", "ajax": false, "filename": "Utility.php", "line": "4114"}, "connection": "kdmkjkqknb", "start_percent": 94.919, "width_percent": 1.484}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 4748}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 4682}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\resources\\views/layouts/auth.blade.php", "line": 39}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.160996, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4748", "source": "app/Models/Utility.php:4748", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUtility.php&line=4748", "ajax": false, "filename": "Utility.php", "line": "4748"}, "connection": "kdmkjkqknb", "start_percent": 96.403, "width_percent": 2.203}, {"sql": "select * from `landing_page_settings`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "Modules/LandingPage/Entities/LandingPageSetting.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\Modules\\LandingPage\\Entities\\LandingPageSetting.php", "line": 27}, {"index": 19, "namespace": "view", "name": "landingpage::layouts.buttons", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.164812, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "LandingPageSetting.php:27", "source": "Modules/LandingPage/Entities/LandingPageSetting.php:27", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2FModules%2FLandingPage%2FEntities%2FLandingPageSetting.php&line=27", "ajax": false, "filename": "LandingPageSetting.php", "line": "27"}, "connection": "kdmkjkqknb", "start_percent": 98.606, "width_percent": 1.394}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "c7Olbm9j3VGWU97WwGbmhGgptmjEatJHEweNGUI4", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/login\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-106347272 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-106347272\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-180909312 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-180909312\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-27432766 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-27432766\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1532805129 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1842 characters\">_clck=1lqc4d8%7C2%7Cfx3%7C0%7C2003; _clsk=1h4itwx%7C1750953618501%7C7%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkxLMTJiNmkzQ1JSK2RPdUVhV1l0UVE9PSIsInZhbHVlIjoiTVUvenJiTUw1RyszV3VoWXFueFVkU1gyZE93SFBoanNydTRacDVkOVNkQmtYSzR3b1RoamRiMG1KTGFrdTc2RTczU2ErQWxtL2dnUUlIc00zQmlLS2diRGxpOEpaaHBxUU5DOW9XclhTU0ZMWWMxVGR4WFZJYWlWakkyLzVNSUdVWEw4YllTcGVreEswTmV2c3d1akplR0ZSODVkSEthS2U5Q3JhQzdLeXNra3VIQ0xYdVlVSTN2NGJuMHJweDZySDdldTVxeU03bkE5WGx1cUNlb2NPZ2hoQzZjcFVpcm1SSlFQTkk0OURLWG94djdXUkVsUGpST1lsVGEza1hlQ3pqN3BvbHBnRVQ0MUZEVFBkem9WanlWSGQ2WE5DdklUZEpOZTRXOEh0TjUrWkFjZHNjSytvcmp3alpPV2hVRWVDSkRUVVFtZUZYUjRzWWh2UVpONHpQZTBxdTJabDc1ZTJjcnl2KzN6WjAzTVR0bjRpQVN4aDVJaWRhMkhLanYycnMycis2UHUwUzUyM1I1MGpUUzdnUmo3L0VvclNHMlFnRjNLTnl3eGFjM29sWkJzTGpQZ3pvSVJzQjJoRHdLY01BeGhlQjI5SUNJdklieS9qNnd0WnpvZDZST2VjYStHZDBPRlQwaS94SkJ3S21KMG1CbFNsZ2kzRHIycDI1OUoiLCJtYWMiOiJiYWUwZmVjNzNiNDcwOGM3MDFhZjhiZTViZWM3ZWRjMTcyYTFjMDIwY2E3ODllNzBmMGVkY2M0NTM5NjI2MzhjIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlhkeVRWZ1VPUklXSUJPdzJ3TGF4U0E9PSIsInZhbHVlIjoiVTF6Z0JmTEJNMkZwTUcwdW4vUERyYzd5dTlMdHdWNWU0WlBCRXpqZnlHUFk4VEpsNHhPbTBmTWJCMFR2cTRhSjhINWtQV2FldVByT254WlpJQzVOYmVIUG1oRmlRNC9kckFqV2YrSktZQVpRV3I0NElNWDBCYVIxYTFpWGxqRzR0bVpQQjhNSlpmd1hMOXdZbkxkQTZvenN1Y3NGd1dWYktETG9MbUxHMVBFYnZoMmE1NHJPbnIvbHNxdVlRSC9mRlIrZ2F0NFE3akxheHRJZi9VVE5GSWdJdXpqd0hLQkgxYk5DT3BjbDMzb0hCclF3SmVGMWRscWZHbTEyWjhmaFl1TUlSYlVjbDBuemVITGFVb1dsbEd3Y0FmcUhjTFp4UE5MYXprWCtIay9ucngxdjRTV3d5aDdydTR2WUlUNHV3MkZkUVpzK05SbzF2cGN4VEtNVmJRYWJCelZCL0x1eUJVcGJkY1dOZFUzRGg1WDN4Zmcya0FFYURYbDVuWUhWTlE5cHhlQnBMQVhveFVLZ0N0dFBWcGV0WTloOFNKVXVOYWs4NU02TlVUeUptZnBMT1p4YkNzVHpUSGg2WTArNlBzOFpHZ3Z6WVYxZ2F4YXNZZlBnT2h0SVpwRW9zck1MblpDREtSV0NBWmxDeDUzMTA1R0pFVmw0VFJUSGhpZHAiLCJtYWMiOiI0NWM0NWIyOWU1MTAzODRlNGNhYjAxZTAwZTExYzY5MDFlZDI2OTY4YWMyOWQ2MzA1MzVmYWM2ZDlhZjI0ODMxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1532805129\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-316008118 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c7Olbm9j3VGWU97WwGbmhGgptmjEatJHEweNGUI4</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ISP8BnnxcALCnxfnm8xYKkDYjUF7hhDCxrzDRCoE</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-316008118\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-879163955 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 16:00:25 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjYvUTNkSVpvcGtBTE1KeG5HVkRHUmc9PSIsInZhbHVlIjoiblEwZVlJa0VEYWM2Rk1vZDQ4Qi9mZGlpL0xFdEFINlA1b2xTRytSellYMnJWNHpCVDJ6d3FsTGtKTVdkVEswTkdHQUJDdGVvY29YbnJ2Q1ExNm1sY3ErM1RqSmtJTUh3c3BxVk1CZXJQRzZVaTZyNGdWSWJQTG1ZbTVzdUxzWk1UM09VcjZwQVJ3TlFBanFOME94OUNWUlBkUG1MWGFuYjhMQ0VsRGhya055U0FOdkhoU0xoZGxVbFBka29kT2NjVTlDZXNFbDdUa3hicERRRGI5VlJCZTlwMGhEU081TFJ0WFdoREM2WHVjb2NzaW1xSlo0R3pVSitxVnJBOTFha0dPb0lGTDVHZ251ZHA4RGxxZlV0RkpTdXZzUURTZCtUQnRvN2IveWFtWXFyM2hTZENvajNjbVFkVWUrS2tZcVFtdzlBdjBRS0ZPYkR4Ukl1QXdpM3NoQTlPTDFKZWVCTVRLUTlQdmJpVUhPNVY4MTRsNDZlQXRLUVppdWlpdmR4SUZYR3MycU5Sakxqbk52OE9HaWNobUphd1RMclplVElmTUFXV1Q3bVBSbHFGUDQ3VDRkblhYYkptbmFBbXROc2xXa3dwbnd4enY0Qy9BdjUzS0J5VUFFakEwdGRDcnJ6d2p3QnMyOGd1cjRwTVpaTy9xaHE2Z2NkTk9qSHFFaWEiLCJtYWMiOiI5ODAxNmQ5NzYxZWJlOTA0MDRmYzkwZTgxZDlkMjc2MjE4ZjI0ZDAxMGZiOTJlYTZhYTBkMWUyYTBlNDgxODNhIiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 18:00:25 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjNnQmxGaFhjdm8xcnBHaUdvbW1SWUE9PSIsInZhbHVlIjoiR0RGMFMwcGZMVGkrS3JWdWE4SGp4Yi8zU0cwZThKMjFHc2hPS3ZrZHRyQnJyZWtqL3oxc2hHelVpTWJZQTFuTXcrbytYY3QxWEp4cUwxMmNuOUVCWlZSSkdqZDdnR3QwaTBYOWFwZUphTnpPbVNUeS81My9nSmtTMVcrKzI0d1NxSVlIc0ZZTjJFRGt3dWdUbWNPdDduUHhROEFvaEtzanZVSlNHZk83eEFTTkpoUEN4bVBSQ25wUDhyYTY4c3JhZzhIblFnTm95dEVyUi94aThFTjUxVkNPdml1VVRYVUczbDJQaDBDL3QxS1E5Q3llczlGbERlT1NrekxZTWxMdEpBRFZJZ1lRbFg0NU9yZ1pvS2NNNWRqU0FhZzgvQ2VTV1NjN2ozbldXZ25UNkNUa29Xc1hyTHcrRGRWNXVDYjlyenN2N2srL1I0SHBDZGEzUDBxUzdFZDNDR3Z1UVY3RVpGREJjV1dEbFlOc3gwSGYzbWIrZHh2OWFzRDlXYXdnN3k3RTIzeFJONlZTN3VrdDdENm9NZ1oveGJLY0tnalRWMTQ3ZmptbWZLbkFTNG9WZDJOaktvN3dZRTkrRGw0ZTFZb1liTFJkZ2VGOUdUVkVXc3FmK2xrU01wUkVJOE5kVE52czdZblJheFJXQi9kWkIrd0d0cUxLdUYweUlVaGEiLCJtYWMiOiJmOTVhYTg5ZDE1OWExYzgxYjQ1ODRiZmE5MDFiYWEzYmNkMjFjOWE0MWEzNzdmMDEwYTEwNDgzMjFlNmUzNmNmIiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 18:00:25 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjYvUTNkSVpvcGtBTE1KeG5HVkRHUmc9PSIsInZhbHVlIjoiblEwZVlJa0VEYWM2Rk1vZDQ4Qi9mZGlpL0xFdEFINlA1b2xTRytSellYMnJWNHpCVDJ6d3FsTGtKTVdkVEswTkdHQUJDdGVvY29YbnJ2Q1ExNm1sY3ErM1RqSmtJTUh3c3BxVk1CZXJQRzZVaTZyNGdWSWJQTG1ZbTVzdUxzWk1UM09VcjZwQVJ3TlFBanFOME94OUNWUlBkUG1MWGFuYjhMQ0VsRGhya055U0FOdkhoU0xoZGxVbFBka29kT2NjVTlDZXNFbDdUa3hicERRRGI5VlJCZTlwMGhEU081TFJ0WFdoREM2WHVjb2NzaW1xSlo0R3pVSitxVnJBOTFha0dPb0lGTDVHZ251ZHA4RGxxZlV0RkpTdXZzUURTZCtUQnRvN2IveWFtWXFyM2hTZENvajNjbVFkVWUrS2tZcVFtdzlBdjBRS0ZPYkR4Ukl1QXdpM3NoQTlPTDFKZWVCTVRLUTlQdmJpVUhPNVY4MTRsNDZlQXRLUVppdWlpdmR4SUZYR3MycU5Sakxqbk52OE9HaWNobUphd1RMclplVElmTUFXV1Q3bVBSbHFGUDQ3VDRkblhYYkptbmFBbXROc2xXa3dwbnd4enY0Qy9BdjUzS0J5VUFFakEwdGRDcnJ6d2p3QnMyOGd1cjRwTVpaTy9xaHE2Z2NkTk9qSHFFaWEiLCJtYWMiOiI5ODAxNmQ5NzYxZWJlOTA0MDRmYzkwZTgxZDlkMjc2MjE4ZjI0ZDAxMGZiOTJlYTZhYTBkMWUyYTBlNDgxODNhIiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 18:00:25 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjNnQmxGaFhjdm8xcnBHaUdvbW1SWUE9PSIsInZhbHVlIjoiR0RGMFMwcGZMVGkrS3JWdWE4SGp4Yi8zU0cwZThKMjFHc2hPS3ZrZHRyQnJyZWtqL3oxc2hHelVpTWJZQTFuTXcrbytYY3QxWEp4cUwxMmNuOUVCWlZSSkdqZDdnR3QwaTBYOWFwZUphTnpPbVNUeS81My9nSmtTMVcrKzI0d1NxSVlIc0ZZTjJFRGt3dWdUbWNPdDduUHhROEFvaEtzanZVSlNHZk83eEFTTkpoUEN4bVBSQ25wUDhyYTY4c3JhZzhIblFnTm95dEVyUi94aThFTjUxVkNPdml1VVRYVUczbDJQaDBDL3QxS1E5Q3llczlGbERlT1NrekxZTWxMdEpBRFZJZ1lRbFg0NU9yZ1pvS2NNNWRqU0FhZzgvQ2VTV1NjN2ozbldXZ25UNkNUa29Xc1hyTHcrRGRWNXVDYjlyenN2N2srL1I0SHBDZGEzUDBxUzdFZDNDR3Z1UVY3RVpGREJjV1dEbFlOc3gwSGYzbWIrZHh2OWFzRDlXYXdnN3k3RTIzeFJONlZTN3VrdDdENm9NZ1oveGJLY0tnalRWMTQ3ZmptbWZLbkFTNG9WZDJOaktvN3dZRTkrRGw0ZTFZb1liTFJkZ2VGOUdUVkVXc3FmK2xrU01wUkVJOE5kVE52czdZblJheFJXQi9kWkIrd0d0cUxLdUYweUlVaGEiLCJtYWMiOiJmOTVhYTg5ZDE1OWExYzgxYjQ1ODRiZmE5MDFiYWEzYmNkMjFjOWE0MWEzNzdmMDEwYTEwNDgzMjFlNmUzNmNmIiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 18:00:25 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-879163955\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-297028693 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c7Olbm9j3VGWU97WwGbmhGgptmjEatJHEweNGUI4</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-297028693\", {\"maxDepth\":0})</script>\n"}}