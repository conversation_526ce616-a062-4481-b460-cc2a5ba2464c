{"__meta": {"id": "X3493a1ad60f1594d0d4412016c254534", "datetime": "2025-06-26 16:01:58", "utime": **********.146175, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750953717.693201, "end": **********.146191, "duration": 0.4529898166656494, "duration_str": "453ms", "measures": [{"label": "Booting", "start": 1750953717.693201, "relative_start": 0, "end": **********.080221, "relative_end": **********.080221, "duration": 0.3870198726654053, "duration_str": "387ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.080231, "relative_start": 0.38702988624572754, "end": **********.146193, "relative_end": 2.1457672119140625e-06, "duration": 0.06596207618713379, "duration_str": "65.96ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46191032, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01483, "accumulated_duration_str": "14.83ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 17 limit 1", "type": "query", "params": [], "bindings": ["17"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.106663, "duration": 0.01402, "duration_str": "14.02ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 94.538}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.128586, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 94.538, "width_percent": 2.495}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (17) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.1336122, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.033, "width_percent": 2.967}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "jEiKRXRaeQw7kpqtZmXNWu3bpmEnaxKAnNI8mVhc", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "17", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1679865707 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1679865707\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1505731205 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1505731205\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-50253246 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">jEiKRXRaeQw7kpqtZmXNWu3bpmEnaxKAnNI8mVhc</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-50253246\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1842 characters\">_clck=1lqc4d8%7C2%7Cfx3%7C0%7C2003; _clsk=1h4itwx%7C1750953692214%7C9%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjY2VkN6S01VeDVtZElheFEyNVpVNkE9PSIsInZhbHVlIjoiWnZpZi9LRUxiQ3o2dmRBOUtYbktKbjQxSjNOUlhueDNDL1NVTTN6WkpCV0tPb2Q2NGxnRUNwZUVQMlFPaFg2UnNDUUYzME5zZmpJaUo5djdvTFZHUW00QndTNmxsd1hUVGk2Z3JiZHBKQW4reUJNbE5td3NPK1Brc1hDOGM2eEo0M0J5VGU1NWNoaWdWY0dXcWZpREYrMTQ5Zm1XTkpvdXc1REVMNW1FVXVDb1FVMXE1Zmt3SGlNbUNxcVphVndWNTdGelZVU2FCOURtaFZ5UTkwMjQ3bUtmNmprMk9QU1U5Q3BmVmp2NHBtM2o5ZWc5cG9kQzgrakhyWTdVREVKTHQ2NDIzYWpQNkxzbWUzajRWSjRpL09IcmUwYUpxTWZQb2ErUXIzTnFvUHhjUkZWVlhaUDBWNXA3ajJaTmg4V2xBTlFxNmJLUG5YTE9MQmlYMkVtQkFrdnZzK1ppN3dKZ0Z6ZU5rYlZmYUN4MWxudFFmVkVndkpaUjFZNWU1ZkVFSkJRWkFsc3NCVXBIdDlDRVMwU3AzYUptR0hqVjcrSWdWV1lteU9Ea1VYQmtnTE5pUFdXeCtUMW45LzlYWnhGUlpZTSsvdVN5QUxQV256bWR3Szdxejg5aGxKelc4S2pxaVhUSVVaSWpobWt6a1JpempzTm1VTFJZemRjSXlrT1giLCJtYWMiOiI0NTIyOTI3YzYxMWMyZjc3YzVhYjc4NzEyNzQ3Y2M4Y2UzNDYyYTkxYjZkODg3Yjc4N2EyZDMyNWE5YmJjY2ZmIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlhKNUl0WFpQSE5VcC90eUZPaE04WkE9PSIsInZhbHVlIjoiRFJFZllYNUdTelRjb1FCVlhZTlhpN2RjemF4T21BV1U4aFJyamRwbXhZd2lUQmo4aWNiQjVsZXBHaE9wR2lVN2g1RkxXdmt4dC84SE5EMy8rV3g2bjUzWUV6WVZkM1hSRVFFdUhZZklOY0w1SmxKUk1zM3AzUWlaeTJENmMvK2NHaGJLY3N1WGhoYUxUbGhKNTh1aUFOU05LaHFub0cyNytPN2RROXJha0gvMVVZalFEdk5xdXY3UW5seDUvR2p2Z1BrcG5oc0RVQTVrVkttKzcwWEMwWmYxMjE5aldSSkkrM3dXV3Vlb1pDUkhBdnlKZ2RoTWdMejhOenFEL1pSeVcxNFlTeWlqMkk0Y1FDNW5CcnJVQWhnSlZvc2RRUVVoeVhmZ3B2c1ZKbHRyaTVta2loK1NzeHlJK1ljZ011NFRlQi9LTjZsMWNqcEt1cXZGUFRiVnk5dnpkSTZsUitrN2ttM0F2QTRxNVJvSGNVNXJjcURwZnBGMDEydEkzTlFmQ01FRjlsSy9RazhOVExtMXVLc1krdUIxMjk2MWNhQW94K2RBTjNFTjMzcTl5KzNMZlJUUkVJdUFsM1d4VmhkWXp6dVhWWXFyemxQQmwxQzVaLzFvM2JuWnJkbnJvZ2ZTUW1pbHdtdWtpRzQ2ZWFOSDFLbmR2cGdZTlNWK2JDNUIiLCJtYWMiOiJmNzViZTYwNzk3YmY4YWY0YTJlYjk0NTQ2MDEyMDk0NmY5YjNkZmM5ODM0ZDdiNDM1NjVkNDNmNDdlZmJhZWFkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">jEiKRXRaeQw7kpqtZmXNWu3bpmEnaxKAnNI8mVhc</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PSbLK6QAWuUzu3JpIF2TDtYQ1L7TOqCcejFUVX3V</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1951937231 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 16:01:58 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkVieXBGQk1qTXZBNWRiREZFcWt4c2c9PSIsInZhbHVlIjoiMUdSc0VqeEdhNzc3ekR2RjdVb2d0Q2dMelF6cFF1R2w3eUpGem1OaCsyUmlLWk8yRkEzV2dmQlAvb3ZraTIvUEZsTE9MMUVwdlZRSmhGMHZ3c3cxS1lPUDJQSjN0c0U0ZGc4ZDZETXBIRWFsUy95NVAxZVk1YjREYjk1Yi9FL0hkL09LZkNoNjRuWGhHZ1VoMVdvcEw4UEd4SWl1cTdWVVowOFJmZzRzeTkxaDNvMHlZa1FjMm5Jc1JoNUEwYTFiZmVQeldaUGRQdXFTZVJPdHVibnhJZ0dPYWt0dFA5d3pqcnhqVnRCYzUyZWVEa1V2RlJ6WVo3OUEyenFRRUpyeERFMGtrL3hrQ0daYjNPVDBjd3dXRCtyMlczSkdVUml0S0JuUGFLYWVuQlRVcU5lN3ZZUmdNQWhwMzBidnZ6QkFYWnFOMkVQMnQyNHU3aUdibld1VXNuMXdjYXhvNXhOU0w1QzBMaGJNVmVvcGVHYWsxTStHYUtoeS9FLzhuTW5jWDRNY0dTallBYkpPdlU1SWNUbXZQR0JVaXZKaFdkNUsrMTdoUFFhM2lESTRScFRJN3E1KzVQNXFtV21DeXQ1UWd2dDhMVGZZcy9PVG55VUtLd1BVOENUZHkweldHMkdPY0FpMzllN2huWXFSZ094V2szMHg4eCtxcnc4ajZlWEYiLCJtYWMiOiJiMThmODQyNDc4MWNmMGVlOTIxMTIyNzNjM2U4YzIyNDcxMGE5OTdmYWRhYjdiNjYwZWFiMzIyOTMxZDAwYzRhIiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 18:01:58 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Img4eE9ZK1Njb1lZUFliamF0eHNETmc9PSIsInZhbHVlIjoiYk1lMi9iQ1c2a1pQOU5qa2xaQlAxK3NkeUxONVJJYVJnQzVZd1NXL1gvQ0VMOGVINlRTK05SSHM4b3VkQlNmMHhxNGt2b3J3ZTJCU084UndvUjhrSFpndVB0QmJTckZBUzhQd0Z5cUgwRXBwdHJ3ZGJoVS8rMFk4M3FKdzBzd2hiSHNRT3FUcDRNdTJGeDVINmJiK0VYYU9NdGhFSFlSZzJ4RUh1cGZmdTFpK0pUQVB5a0RyTWRUbXExUTlHTGN4cGpOb3pVVDBnYjFuUm11WXBLNERtMndjdk05dzZyZEJ0QU1BNlNEZzlHZFFjeWJSbll5ZFdZMGVvSGh0dnB5amlsQktsYUVzV1JIckRmS1RZSzFGdHdiM3gvRFBWbFMzNmJHSFNFVVhRbWdudzN1SGhPTGxvdmcrKzBmNUVwMnVXajdldEhiMVNuUEVsUGV1SmZVdTlqRUNnZjlJTUovU3BuRDlWTC9FU29SekJGc0dqN01NYS85Y3BZN3ZmQnpxNHJ1aDFiTFhIc3Q0eWpnMEpEbTRDVDB1TlRlaDlaMWgzV3NhVG1NT0tqT2s0SlBtSnZ1OHU0OG5iNnVUK3hxUnRaR3ArRGZZZnRTRW1Oelkxalh2OGZVMzBLenhYQWp0UldPRnBieGRhaGx3Mkg5cFI0ZHJ5anNwbDBzaFoyQVMiLCJtYWMiOiI0Yzk3ODFkMjQ2NmNkNDkxM2M0YTc0MDBkMjRhMWI5MDA0NTQ4N2M3YjIzNzJjYjU0MTU3MDQxZjViNGI5NDM4IiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 18:01:58 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkVieXBGQk1qTXZBNWRiREZFcWt4c2c9PSIsInZhbHVlIjoiMUdSc0VqeEdhNzc3ekR2RjdVb2d0Q2dMelF6cFF1R2w3eUpGem1OaCsyUmlLWk8yRkEzV2dmQlAvb3ZraTIvUEZsTE9MMUVwdlZRSmhGMHZ3c3cxS1lPUDJQSjN0c0U0ZGc4ZDZETXBIRWFsUy95NVAxZVk1YjREYjk1Yi9FL0hkL09LZkNoNjRuWGhHZ1VoMVdvcEw4UEd4SWl1cTdWVVowOFJmZzRzeTkxaDNvMHlZa1FjMm5Jc1JoNUEwYTFiZmVQeldaUGRQdXFTZVJPdHVibnhJZ0dPYWt0dFA5d3pqcnhqVnRCYzUyZWVEa1V2RlJ6WVo3OUEyenFRRUpyeERFMGtrL3hrQ0daYjNPVDBjd3dXRCtyMlczSkdVUml0S0JuUGFLYWVuQlRVcU5lN3ZZUmdNQWhwMzBidnZ6QkFYWnFOMkVQMnQyNHU3aUdibld1VXNuMXdjYXhvNXhOU0w1QzBMaGJNVmVvcGVHYWsxTStHYUtoeS9FLzhuTW5jWDRNY0dTallBYkpPdlU1SWNUbXZQR0JVaXZKaFdkNUsrMTdoUFFhM2lESTRScFRJN3E1KzVQNXFtV21DeXQ1UWd2dDhMVGZZcy9PVG55VUtLd1BVOENUZHkweldHMkdPY0FpMzllN2huWXFSZ094V2szMHg4eCtxcnc4ajZlWEYiLCJtYWMiOiJiMThmODQyNDc4MWNmMGVlOTIxMTIyNzNjM2U4YzIyNDcxMGE5OTdmYWRhYjdiNjYwZWFiMzIyOTMxZDAwYzRhIiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 18:01:58 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Img4eE9ZK1Njb1lZUFliamF0eHNETmc9PSIsInZhbHVlIjoiYk1lMi9iQ1c2a1pQOU5qa2xaQlAxK3NkeUxONVJJYVJnQzVZd1NXL1gvQ0VMOGVINlRTK05SSHM4b3VkQlNmMHhxNGt2b3J3ZTJCU084UndvUjhrSFpndVB0QmJTckZBUzhQd0Z5cUgwRXBwdHJ3ZGJoVS8rMFk4M3FKdzBzd2hiSHNRT3FUcDRNdTJGeDVINmJiK0VYYU9NdGhFSFlSZzJ4RUh1cGZmdTFpK0pUQVB5a0RyTWRUbXExUTlHTGN4cGpOb3pVVDBnYjFuUm11WXBLNERtMndjdk05dzZyZEJ0QU1BNlNEZzlHZFFjeWJSbll5ZFdZMGVvSGh0dnB5amlsQktsYUVzV1JIckRmS1RZSzFGdHdiM3gvRFBWbFMzNmJHSFNFVVhRbWdudzN1SGhPTGxvdmcrKzBmNUVwMnVXajdldEhiMVNuUEVsUGV1SmZVdTlqRUNnZjlJTUovU3BuRDlWTC9FU29SekJGc0dqN01NYS85Y3BZN3ZmQnpxNHJ1aDFiTFhIc3Q0eWpnMEpEbTRDVDB1TlRlaDlaMWgzV3NhVG1NT0tqT2s0SlBtSnZ1OHU0OG5iNnVUK3hxUnRaR3ArRGZZZnRTRW1Oelkxalh2OGZVMzBLenhYQWp0UldPRnBieGRhaGx3Mkg5cFI0ZHJ5anNwbDBzaFoyQVMiLCJtYWMiOiI0Yzk3ODFkMjQ2NmNkNDkxM2M0YTc0MDBkMjRhMWI5MDA0NTQ4N2M3YjIzNzJjYjU0MTU3MDQxZjViNGI5NDM4IiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 18:01:58 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1951937231\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-523615865 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">jEiKRXRaeQw7kpqtZmXNWu3bpmEnaxKAnNI8mVhc</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>17</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-523615865\", {\"maxDepth\":0})</script>\n"}}