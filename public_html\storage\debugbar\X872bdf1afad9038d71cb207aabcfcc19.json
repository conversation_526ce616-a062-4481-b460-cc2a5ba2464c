{"__meta": {"id": "X872bdf1afad9038d71cb207aabcfcc19", "datetime": "2025-06-26 16:27:36", "utime": **********.14213, "method": "GET", "uri": "/customer/check/delivery?customer_id=6", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750955255.711978, "end": **********.142144, "duration": 0.43016600608825684, "duration_str": "430ms", "measures": [{"label": "Booting", "start": 1750955255.711978, "relative_start": 0, "end": **********.094649, "relative_end": **********.094649, "duration": 0.3826711177825928, "duration_str": "383ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.09466, "relative_start": 0.38268208503723145, "end": **********.142147, "relative_end": 3.0994415283203125e-06, "duration": 0.04748702049255371, "duration_str": "47.49ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44493552, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00229, "accumulated_duration_str": "2.29ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 17 limit 1", "type": "query", "params": [], "bindings": ["17"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.129454, "duration": 0.00182, "duration_str": "1.82ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 79.476}, {"sql": "select * from `customers` where `customers`.`id` = '6' limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.1352181, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "kdmkjkqknb", "start_percent": 79.476, "width_percent": 20.524}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PKqZteC3IabV2rdCxsXBMLnTb72fzH4qdR2KdQSn", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "17", "pos": "array:4 [\n  2295 => array:9 [\n    \"name\" => \"منتوس علكة ابيض نعناع 54جم\"\n    \"quantity\" => 2\n    \"price\" => \"15.00\"\n    \"id\" => \"2295\"\n    \"tax\" => 0\n    \"subtotal\" => 30.0\n    \"originalquantity\" => 5\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  2296 => array:8 [\n    \"name\" => \"بطيخ إضافي 60 ثانية\"\n    \"quantity\" => 2\n    \"price\" => \"18.00\"\n    \"tax\" => 0\n    \"subtotal\" => 36.0\n    \"id\" => \"2296\"\n    \"originalquantity\" => 2\n    \"product_tax\" => \"-\"\n  ]\n  2297 => array:8 [\n    \"name\" => \"ساديا - دجاج مجمّد 800جم\"\n    \"quantity\" => 2\n    \"price\" => \"16.00\"\n    \"tax\" => 0\n    \"subtotal\" => 32.0\n    \"id\" => \"2297\"\n    \"originalquantity\" => 6\n    \"product_tax\" => \"-\"\n  ]\n  2301 => array:8 [\n    \"name\" => \"بريكستا ويفر المقرمش والمغطى بشوكولاتة الحليب 24 حبة\"\n    \"quantity\" => 1\n    \"price\" => \"13.00\"\n    \"tax\" => 0\n    \"subtotal\" => 13.0\n    \"id\" => \"2301\"\n    \"originalquantity\" => 0\n    \"product_tax\" => \"-\"\n  ]\n]"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-2032728509 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2032728509\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1091335483 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>6</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1091335483\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1226291285 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PKqZteC3IabV2rdCxsXBMLnTb72fzH4qdR2KdQSn</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1bowxa%7C2%7Cfx3%7C0%7C1999; _clsk=1osc4j2%7C1750953875247%7C2%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IllaYWJkczNGQUlwaGhGcjIzWUdwd1E9PSIsInZhbHVlIjoibVc4K0RmS1JlaXVXTEs4bi9LL253YUJvQzRTQWp3WFBNbmFWSXBGZ3kzQUJTeS92b3VjNDFaV0RTMTBzOEJJQkZQeC8yNUtHYjFtZytSa2E4emVMa3hlUGZxbUxIeXFma2dEakRLOUF6SWdzOFJIeG0yZUhsbmxCTDNnTEJ3bjFNQk1tSzJXUEVzenFJVFNqaTV0a3pnY0sxM2JjRGtUM3h1dDVrYU9YU2hBK2lWTkhvSEI2eUZWOXd2SzVxcWlQYXdxSENBaHRUYU9LbGU1bzNTQVFkWk5GRnNpY3BaazE3N0hzM0thaXk3WndSb2tWdU9DYUNabTVGV3EwRDZ6aGpKSGoyc1RLNjZ6V0pTczBRT09oZzhxUUJ0TVZpOEtyNGQ3ZkgvTW1tQ25IMktPdXFyQVgzajlnOVZzTmJBRmc5WjZkYVNieEZIeXRseFcwYjZMcURIOGV0ZTRoclh4VitjL0x5OVZCQ2tBOUNTMmlrWEZRUXRPSFlKYXV5b2lXT3R2SzlkNVpxRmNzN0oybTRlNUkzLzlHbFR2ZVBUMS9TTkR4aUF2NjlCN0VOVE8rUlg4enNRaklnNnFzbzlwK25DT2MwOC85Wm45bFpvWVYydUtUYzd5dVp3a0IyUjJ4SUp5bU40cy9UTmsvcXVTeUhETDY4a1JwVHRscCtPb3YiLCJtYWMiOiIxZWJhMTYzNzFmYTFlM2JmZTZhYjFiOTllNWU2YWIyMmE0MzU2YjQyYzAwOGM2NGUyNmMzYmU2N2I5ZGQzN2E5IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImFZNVVXL0xGOXdnWjFsQlJ2R3ZJNGc9PSIsInZhbHVlIjoidldyY2FUN1VzTEFVOVZMMXZGMkE5dWRyY3RvKzc0U28rZFpTWkxSR24vS1A0cktaSUg0Tm1ncFp1c0l5Y3lJRVA4TVVtWWJTWlYvaUlKUzdtdUd6MFpaV3RYZWFmTjMzVGdxUEFHSDlsb0U5LyttQzJ1Zk9zeHBSdDA0dS9PQ1NWQmlCckppcmJKaGVhKzNPMW1OVGllQS9Yc0IrbzhZV1U1cDBrdnQ5bW1JR09HbHJSRDdHY2tWMGxMeEpTclpuanZweWE4akp6Z1lIM3NYeUVWbW5sdmJ2MXdhOWVrTVFKWlVESEFwT2lrQVlxb1EraWxKZ1VCNm5UVWwyQTB5cS9vMi9xcUVSNWhhTWV1Mjh4Q0h1azZtc1N0VnFXRytzckk1SFhnU21uYStuM1JnTFBobld1RlJRY2V4b2ZmaXR6RlRHbVlMckZjS1NFVXJXdy9YdDk0a3IyU3U5RGpMMndFUjVEaW1oOXlpRXBkZElzZGdXSDlDN0FJZDRxeUhEc3dmQnpvQ3kxZy9VdklLVjZjOVdKWFBNZmdGb0xlSWlTSEhEQXdZMzVZSXpKcEVNMnlXSERJSk9pdVRrZEVjSFBqb1ZSVVBadmVYWkt1cmxxdURtOWZ3ZTdieGNQaGRRKy9PNFFQZEVsbGpxZHZ5U0tENnd6Z1dCaEVlMEgrem4iLCJtYWMiOiI2M2JiMjFhNDNkZDRlYmU2Y2ZhNDgzYzc3MDM0YWExNDA0ZDQxN2ZjNGQwMWI0M2EyOTE0MTM3YjFhOWVhODM3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1226291285\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1140044192 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PKqZteC3IabV2rdCxsXBMLnTb72fzH4qdR2KdQSn</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GUUMfjgJKgBqP5oaDcRP4Rfu0FM4k6V9zWwhkyoG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1140044192\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 16:27:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkR3emF6WnNqaW9PZWxtQ0lqRS9WSnc9PSIsInZhbHVlIjoiVXZRMWVvYXVoeGxjR3BXVkhoTUNwS2JjdFc2eTRBOC9hK2krbFpER1FGYWE2TnplL1pVVVIyVWpScTBoY1Y3aHFzOGRIbFl3ZHZzYTBmSEhPU3J1cEhzelFmVmNid3FzQ1ZBdjEzNWdMeDJobnA0dTNlYU14enQwYzVMRVA3bHRmWUtma0NhZnNOR21iY1NhS2ZPUzQ2WnZpdDJQU252NU82ckdHdk5Xc2VWOCthMFJ4Z2RhVm9QZzRpQUQrd1FIMGk4ZW5tSjVkRXJvWmhRc2pVZTJ4cnRaU2EzUTQyYTYzeVVaUXV5UFI2UUgreGFWQ2dnVU1KQXlwbTFMOXR2Zk9SS1QwaUVyQ2dHY3NUTFArSXFqQjBiTW9UVmpOay9pSkVJdzhDS3RIM01CaEs4VUg5MFhMYkY4dytJOXd1cmt2aUo1ZVZyZHBpdW5Vd0RxSGw3QzNjbmc0S1dtbFc3VlNIZTI3K0RGTmJMTC8rUW9KQUJxMmdIYTFidTJiSExQcEdXbmRobmN1T20zM0FxNGVkMlE4QmVGenFINElVVDMrdzg4S2ptV0VQSkRuVy9XTmxHeW1aUkFQY09pS0dTYjVtOVovUHN3UVNwWm55b2NLMFhhN2NiMXFheFgyK1VYRnRxTWlUbndyMXY2WHI5azhtWVJrZXdlVFFSdEU3UTYiLCJtYWMiOiIyYzFmZTRlOTI5MDY2MTU3MmI1OWQ5ODkwOWJlZTlmODZlYjQxOGQxZDQxYWQwYmE0YTNjMWQ3NmMwYmI4NzZmIiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 18:27:36 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlBlQWpRekcxWU5LZUJrNUlnd2VYR3c9PSIsInZhbHVlIjoiVjZjK3VKODF5eko1cXlZQ1NiNG5iU0R0NlFLeVBRK1FQK1hJSDR6OVhwcFdPMzNnb0pLNjFpRGZ5SU1RbGNlM04vZS8yY0c0VFB6UzlvQWlBMXkySUVCRDg4ZGFsaEdqOVdKb3N0WDE5VHdYNzc2U2d2NkFxMHlhRHJVYkxqYVI4UEc2d2ZRM3lSKzBCR0xqVWlDcDc5SmkrbFJUaG9ONjQvZzhienBjdjBIaDU1R2JHdy8zdUQrQ1I0MmMzaGpYSWpqWGdVanBGa3NmOE8vdTkyMjZHTG9JS1dwbW16d0ZTRnN3RWJHVWU5cGFGcXBRY01DemsrT1d6R2UvSjVRRU0yMXRKbS8waFZ3U2N1UXRTM2xhTXl4WnRJYWlscWtQVzNGaW5ueENqNzluYm9iQ0sreUo5aURLdHdnVkJvM3VnWlRqNGsyd1FXeExJQ2s4Y2NLdEdtNXRrbzdteXpqdnJQL2tWZW01bTNTdjlZQ0RRSktsb0cxNHo3R20wSzQ1WkZ6eFJFZm0vdU5XSXR6WjViR2t3QTQ4Ym5xYmlyeExIc2RhR2NBa2V4K1REOXFZWVJidE96V2pXZW1nVlZWd0c3dVJCOXFacDVkS01IcVVBSDk3NDh4NjFEbC93Z28vS1VFQW5KRnpIUmZLUW03RFhUOVhwRFcrRUpzVlBzU04iLCJtYWMiOiIyNDUyNmFkNTE0NDdjYzY0NzkzODlhYjBkNDI4YzU0NWMzMWRmY2QxZDViZjJlMzgxYTA0YTM2OGVmZjUyNDRkIiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 18:27:36 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkR3emF6WnNqaW9PZWxtQ0lqRS9WSnc9PSIsInZhbHVlIjoiVXZRMWVvYXVoeGxjR3BXVkhoTUNwS2JjdFc2eTRBOC9hK2krbFpER1FGYWE2TnplL1pVVVIyVWpScTBoY1Y3aHFzOGRIbFl3ZHZzYTBmSEhPU3J1cEhzelFmVmNid3FzQ1ZBdjEzNWdMeDJobnA0dTNlYU14enQwYzVMRVA3bHRmWUtma0NhZnNOR21iY1NhS2ZPUzQ2WnZpdDJQU252NU82ckdHdk5Xc2VWOCthMFJ4Z2RhVm9QZzRpQUQrd1FIMGk4ZW5tSjVkRXJvWmhRc2pVZTJ4cnRaU2EzUTQyYTYzeVVaUXV5UFI2UUgreGFWQ2dnVU1KQXlwbTFMOXR2Zk9SS1QwaUVyQ2dHY3NUTFArSXFqQjBiTW9UVmpOay9pSkVJdzhDS3RIM01CaEs4VUg5MFhMYkY4dytJOXd1cmt2aUo1ZVZyZHBpdW5Vd0RxSGw3QzNjbmc0S1dtbFc3VlNIZTI3K0RGTmJMTC8rUW9KQUJxMmdIYTFidTJiSExQcEdXbmRobmN1T20zM0FxNGVkMlE4QmVGenFINElVVDMrdzg4S2ptV0VQSkRuVy9XTmxHeW1aUkFQY09pS0dTYjVtOVovUHN3UVNwWm55b2NLMFhhN2NiMXFheFgyK1VYRnRxTWlUbndyMXY2WHI5azhtWVJrZXdlVFFSdEU3UTYiLCJtYWMiOiIyYzFmZTRlOTI5MDY2MTU3MmI1OWQ5ODkwOWJlZTlmODZlYjQxOGQxZDQxYWQwYmE0YTNjMWQ3NmMwYmI4NzZmIiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 18:27:36 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlBlQWpRekcxWU5LZUJrNUlnd2VYR3c9PSIsInZhbHVlIjoiVjZjK3VKODF5eko1cXlZQ1NiNG5iU0R0NlFLeVBRK1FQK1hJSDR6OVhwcFdPMzNnb0pLNjFpRGZ5SU1RbGNlM04vZS8yY0c0VFB6UzlvQWlBMXkySUVCRDg4ZGFsaEdqOVdKb3N0WDE5VHdYNzc2U2d2NkFxMHlhRHJVYkxqYVI4UEc2d2ZRM3lSKzBCR0xqVWlDcDc5SmkrbFJUaG9ONjQvZzhienBjdjBIaDU1R2JHdy8zdUQrQ1I0MmMzaGpYSWpqWGdVanBGa3NmOE8vdTkyMjZHTG9JS1dwbW16d0ZTRnN3RWJHVWU5cGFGcXBRY01DemsrT1d6R2UvSjVRRU0yMXRKbS8waFZ3U2N1UXRTM2xhTXl4WnRJYWlscWtQVzNGaW5ueENqNzluYm9iQ0sreUo5aURLdHdnVkJvM3VnWlRqNGsyd1FXeExJQ2s4Y2NLdEdtNXRrbzdteXpqdnJQL2tWZW01bTNTdjlZQ0RRSktsb0cxNHo3R20wSzQ1WkZ6eFJFZm0vdU5XSXR6WjViR2t3QTQ4Ym5xYmlyeExIc2RhR2NBa2V4K1REOXFZWVJidE96V2pXZW1nVlZWd0c3dVJCOXFacDVkS01IcVVBSDk3NDh4NjFEbC93Z28vS1VFQW5KRnpIUmZLUW03RFhUOVhwRFcrRUpzVlBzU04iLCJtYWMiOiIyNDUyNmFkNTE0NDdjYzY0NzkzODlhYjBkNDI4YzU0NWMzMWRmY2QxZDViZjJlMzgxYTA0YTM2OGVmZjUyNDRkIiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 18:27:36 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PKqZteC3IabV2rdCxsXBMLnTb72fzH4qdR2KdQSn</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>17</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2295</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"26 characters\">&#1605;&#1606;&#1578;&#1608;&#1587; &#1593;&#1604;&#1603;&#1577; &#1575;&#1576;&#1610;&#1590; &#1606;&#1593;&#1606;&#1575;&#1593; 54&#1580;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">15.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2295</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>30.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>5</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>2296</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"19 characters\">&#1576;&#1591;&#1610;&#1582; &#1573;&#1590;&#1575;&#1601;&#1610; 60 &#1579;&#1575;&#1606;&#1610;&#1577;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">18.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>36.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2296</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n    <span class=sf-dump-key>2297</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1587;&#1575;&#1583;&#1610;&#1575; - &#1583;&#1580;&#1575;&#1580; &#1605;&#1580;&#1605;&#1617;&#1583; 800&#1580;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">16.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>32.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2297</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>6</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n    <span class=sf-dump-key>2301</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"52 characters\">&#1576;&#1585;&#1610;&#1603;&#1587;&#1578;&#1575; &#1608;&#1610;&#1601;&#1585; &#1575;&#1604;&#1605;&#1602;&#1585;&#1605;&#1588; &#1608;&#1575;&#1604;&#1605;&#1594;&#1591;&#1609; &#1576;&#1588;&#1608;&#1603;&#1608;&#1604;&#1575;&#1578;&#1577; &#1575;&#1604;&#1581;&#1604;&#1610;&#1576; 24 &#1581;&#1576;&#1577;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">13.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>13.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2301</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}