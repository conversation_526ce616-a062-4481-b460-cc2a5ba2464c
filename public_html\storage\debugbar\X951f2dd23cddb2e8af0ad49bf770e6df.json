{"__meta": {"id": "X951f2dd23cddb2e8af0ad49bf770e6df", "datetime": "2025-06-26 16:02:12", "utime": **********.435537, "method": "POST", "uri": "/warehouse-empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750953731.980411, "end": **********.435551, "duration": 0.4551398754119873, "duration_str": "455ms", "measures": [{"label": "Booting", "start": 1750953731.980411, "relative_start": 0, "end": **********.344332, "relative_end": **********.344332, "duration": 0.3639209270477295, "duration_str": "364ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.344344, "relative_start": 0.36393284797668457, "end": **********.435552, "relative_end": 9.5367431640625e-07, "duration": 0.09120798110961914, "duration_str": "91.21ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45879960, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST warehouse-empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@warehouseemptyCart", "namespace": null, "prefix": "", "where": [], "as": "warehouse-empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1627\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1627-1637</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00211, "accumulated_duration_str": "2.11ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 17 limit 1", "type": "query", "params": [], "bindings": ["17"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.4172149, "duration": 0.00165, "duration_str": "1.65ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 78.199}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.4283102, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 78.199, "width_percent": 21.801}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "jEiKRXRaeQw7kpqtZmXNWu3bpmEnaxKAnNI8mVhc", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "17"}, "request": {"path_info": "/warehouse-empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-674106570 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-674106570\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-915240219 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-915240219\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1583099808 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1583099808\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-75868201 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">jEiKRXRaeQw7kpqtZmXNWu3bpmEnaxKAnNI8mVhc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1843 characters\">_clck=1lqc4d8%7C2%7Cfx3%7C0%7C2003; _clsk=1h4itwx%7C1750953725629%7C12%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Im9TVDlEOHZOT2JOQmMwUS9ZMHVjV3c9PSIsInZhbHVlIjoiK3lyeUg4dXByMDlYR2dsWXJXSzRUenA3dmZaaWdaVjNIRmNKSy8xYUpoZ01Eai9oWXg5VDg4cC90aC9kNVJMM29zNkpiTjl2WGc0WnppQVlQMHh5L2xsUEU4aDVxS254NERVM1lGM09BUFNncW4yQjBnMnVpSFMyZ00yakdHQjdHSFIrd1JNNDV4d1lBUVRISERDWkx4Mm5qbGVJZDJZWFBZS1I3S2tlSXU5bmVmeFJhVmlHSmIyNVBwNURkLzh6WExqbDlrWEFyYk9FdjdUSG1BeHgxZTg3bUJ5WEtZQkpHZW0yb3NiOTl1SGxUaVFEK3V0V3REWmV2MEhyRHZ0RmNuMWhuOGZuQWV3STkyZlVDMG11VEF0OHROUzRsSmdoRnRHckZxMERmYXNLYzFrcWhXM3pwdjZDQzM3dkltNXQ3eC9JQVVPcEhXVkhVWEp1ejNtK3pmQWRNdXVxdGxxUzlGMDBUcWkrVmgvaSsvVTNCSFc1WFhieC9WZnVIV291RkU2bmhweEV1SWJwS1lZTnZHYk1SZFZzWWgvaklBdXluS0NBRDhvT3F4SW1aN24yb21jZjlLQ0JaT0dudDBFenNaUHZNU1JqdUFISDZHSkxRRkJVbzVqYkt1SkRMb0FQT1Q4M3prWnp0ZUc3dVhmamxoNWVvUldzNU42dyt3dHAiLCJtYWMiOiJhM2I2ZDEwODVhMjAxMTQ1ZmQ3N2YwOGMyMTQ2MDVkOWZkYTE1ZTkwYTVmYWQ3OWUwZjdhYTcwMWFhN2IzYTg0IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkYxL2d3bUdTcWVVU3BEMVFVZ3AxYXc9PSIsInZhbHVlIjoicEM2T2FteTNieWp6NXlHY2JjbGUzbTl6aUY1UU1uRzhFMEtyc2wxRklvNlVGdEZLaEwwdU5sZVhlZU1ORHFuZUxTUEhlSmZEODdoYkd1K0lHOFBCeUtCa29QdEExbWhXWVBNUXY3Y0lETDNzU2tMT1dhbXgrakJKV2h6bzhnOGRkeDV2NWd3QllrKy8xREdOQ3dlZ2V4NzhUbEcxK3E2Q0JzTGFqN3I0UEprUXFUNDQ3eVpOSlVacXJRcGNiU21Vem1uVFBzUWhOSnUxN09qaTBKMDBiZTlNTW03c1orMFJSajlqbDFLZUppK254bGF6QWtkWG5WYmFpaXFudENmUGxVbXVONkRJWWxkZmpJMUEzMjRUNlVWcVhqTkV0MjNLOGpZWU5SZmhyaFNtbWhkejk1NXYrYlRVOTVNQnJmZVMvcUZIa29mQy9ZMkFMVHZlS3VobmZObDREeGFwNVlPc09UNU5RRDlHSS9Ua21meVVWU2E1NVc1cFVCSWpweFdMSmswZTJIa1Y2Z3YwUnV0WjRYL2xTcE9TanRHcHpUa1E0RUtvUVRLYWE4YXNZa2wrN0JyMWh0aTh1SGpGWHBBcUVZaHBab3BDb1NYK2JYYUVhSlNrVUZMRkRPZkJNSlhpSXQ3alNBNDVoSy9kMmhwOWM0ckM0L0poR01mQTZWQ2siLCJtYWMiOiIwMzkxODdmNGY4OThkOWYyYjQzODdkYTQ3YzMxNDM0YzRkZDQ4ZDAxYzE2YTc2MTRiYjdlOTc2NWYxMDhiMDU0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-75868201\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1319076601 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">jEiKRXRaeQw7kpqtZmXNWu3bpmEnaxKAnNI8mVhc</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PSbLK6QAWuUzu3JpIF2TDtYQ1L7TOqCcejFUVX3V</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1319076601\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1013851178 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 16:02:12 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjBSQmZINDI1bmQ1dU1zbzNXZm1MM1E9PSIsInZhbHVlIjoiVkoyTVNpQTNoTFFpWGdSRml6R0lhOGhmcEVzTjhkUVdaaVdHWFkvWk0yYUpLbVZ6TzZKMXMwRjNDbVlhenFGUFA5Y21mek9RLzg4ZnU4eWZzL0ZXSHFQU0VyVyt1QnEyQjlLeE9DQk5UYWxVOUJHNXBOY0hwRC9VQW55Uy9WcDBEYVFvUHBpN2FQQk1PUUhYKzRkNHg4ZFkyUFVjTi9sZkFpUlBaNDJxNEgxSW1TWmI1c1dPa1BxMDNMdEdPd1FPZ3JDN1ZOdGFjdDZuN0dnSWFubEdFRXZRV2ZYM04wTmtPZlZWalpISytqMlE4QzQ3akhRNEorQTJwU0lNZGgyL1YzczErZW9qUjFvL0NnSGhWSnZJeHBaeHpMSkR2OFc2NVNiQ0NOVlBENHc1eFFSQVhTMWE0dCtOQ1piLy9FSnFKK01jNGgvdW91TGFtdE1CMUk1dkZhamN3STV4NVljc1J1NVRxNnBMQWxTVHFqWTlZK2JkS3pwUVlacURtelpFbHcydDh1VmV5cFMwbkxzTVhDME9kNlJMbGFjRTdwT0JRdjVrZjNvUkVZRCtjdjVmVk1FYzR2ejJPU2tBc05sejlJVlVBWHRyM1lyaXB0WTJlZnRGOUY1OU9jUjl3dGt4Z2JUWlFlZkxxZ2ZSTE1vcjFBdzVzL29RN0taMzlzeHAiLCJtYWMiOiI2YWViYjRkNmRkMDdhNzFiNWU0NzEwOWU1ZDMyMmZlYmRmOGMwMTBlNjI0MjY0MzRlNDUwMDY0ZTBmOWExNTNkIiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 18:02:12 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlJRRXBoaTNtQUU4SE1kajdrQkJFSmc9PSIsInZhbHVlIjoiNER6aGpnL1lnTXBlMFR1VG9FQ2YrYVlGbytMRDV1NnlyaTVXTGVwZUVhbS92dk5BNmZ4SVMzMUU5Nmp3SWJlM3UzUDYxQnBkT2tpYXVZa0RCeVFhSzBUUmtBRi9DMkJDclZMRHh2cEp2RjRnczJNT1lMSWRzNUY5L0ZxTGxKVGpIMlA2U24ySXR6SUFVOHpQNWlyKzBubHd0cTZac1c0cUJSdWhSNUgveWExUklkWmhEdzAzWW5lanVjTVIrU21RVkpzeGJSalBkVk1zZEx3TTZSVG5RdGRaUHVFOEl3Sk5wTjFCMDVxVnJ1c1RGQy92QVkwNG5ZVkZSK1hvNU1kWnIzYTE0U014SEFGeEpzb0Y1akx5YXNEVjZFbDNlMmdwMmNLa3NHR25jaWRRUlpkTWFFNGFlRzBlem5vRzRTbSt6SEpodmFRQS80TlZ5UlQ3WU5aM01zaHpMSXBsa3BKbVorSk12cjdEODJEZWpOVndVeHpXa2lSa3ptcnd5YVp2OEpPQ3NBM1BFQ0ZBUHc5N3hyWVhsR083UVVmMVBVQXA1NXl6ZERKbTlzaHJSZ3c5U29tRU01VlVKT2l6eWc2TUdleGxwbHZib3RTWEp6SzlMUklXb3JoeWIrQUpRYWZmNzUxTjlDbFBzeTVVd2Vja1QzM2FaaGhMRDN5Szg3R1AiLCJtYWMiOiIyMDBhNmU5OGM5MzEyODg3ZTY5NjllOTAxZTYxMDBiMGE4NDVlOGYzOWM5ZmZlOWQxZTk5OThhM2QyZWViZjY3IiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 18:02:12 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjBSQmZINDI1bmQ1dU1zbzNXZm1MM1E9PSIsInZhbHVlIjoiVkoyTVNpQTNoTFFpWGdSRml6R0lhOGhmcEVzTjhkUVdaaVdHWFkvWk0yYUpLbVZ6TzZKMXMwRjNDbVlhenFGUFA5Y21mek9RLzg4ZnU4eWZzL0ZXSHFQU0VyVyt1QnEyQjlLeE9DQk5UYWxVOUJHNXBOY0hwRC9VQW55Uy9WcDBEYVFvUHBpN2FQQk1PUUhYKzRkNHg4ZFkyUFVjTi9sZkFpUlBaNDJxNEgxSW1TWmI1c1dPa1BxMDNMdEdPd1FPZ3JDN1ZOdGFjdDZuN0dnSWFubEdFRXZRV2ZYM04wTmtPZlZWalpISytqMlE4QzQ3akhRNEorQTJwU0lNZGgyL1YzczErZW9qUjFvL0NnSGhWSnZJeHBaeHpMSkR2OFc2NVNiQ0NOVlBENHc1eFFSQVhTMWE0dCtOQ1piLy9FSnFKK01jNGgvdW91TGFtdE1CMUk1dkZhamN3STV4NVljc1J1NVRxNnBMQWxTVHFqWTlZK2JkS3pwUVlacURtelpFbHcydDh1VmV5cFMwbkxzTVhDME9kNlJMbGFjRTdwT0JRdjVrZjNvUkVZRCtjdjVmVk1FYzR2ejJPU2tBc05sejlJVlVBWHRyM1lyaXB0WTJlZnRGOUY1OU9jUjl3dGt4Z2JUWlFlZkxxZ2ZSTE1vcjFBdzVzL29RN0taMzlzeHAiLCJtYWMiOiI2YWViYjRkNmRkMDdhNzFiNWU0NzEwOWU1ZDMyMmZlYmRmOGMwMTBlNjI0MjY0MzRlNDUwMDY0ZTBmOWExNTNkIiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 18:02:12 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlJRRXBoaTNtQUU4SE1kajdrQkJFSmc9PSIsInZhbHVlIjoiNER6aGpnL1lnTXBlMFR1VG9FQ2YrYVlGbytMRDV1NnlyaTVXTGVwZUVhbS92dk5BNmZ4SVMzMUU5Nmp3SWJlM3UzUDYxQnBkT2tpYXVZa0RCeVFhSzBUUmtBRi9DMkJDclZMRHh2cEp2RjRnczJNT1lMSWRzNUY5L0ZxTGxKVGpIMlA2U24ySXR6SUFVOHpQNWlyKzBubHd0cTZac1c0cUJSdWhSNUgveWExUklkWmhEdzAzWW5lanVjTVIrU21RVkpzeGJSalBkVk1zZEx3TTZSVG5RdGRaUHVFOEl3Sk5wTjFCMDVxVnJ1c1RGQy92QVkwNG5ZVkZSK1hvNU1kWnIzYTE0U014SEFGeEpzb0Y1akx5YXNEVjZFbDNlMmdwMmNLa3NHR25jaWRRUlpkTWFFNGFlRzBlem5vRzRTbSt6SEpodmFRQS80TlZ5UlQ3WU5aM01zaHpMSXBsa3BKbVorSk12cjdEODJEZWpOVndVeHpXa2lSa3ptcnd5YVp2OEpPQ3NBM1BFQ0ZBUHc5N3hyWVhsR083UVVmMVBVQXA1NXl6ZERKbTlzaHJSZ3c5U29tRU01VlVKT2l6eWc2TUdleGxwbHZib3RTWEp6SzlMUklXb3JoeWIrQUpRYWZmNzUxTjlDbFBzeTVVd2Vja1QzM2FaaGhMRDN5Szg3R1AiLCJtYWMiOiIyMDBhNmU5OGM5MzEyODg3ZTY5NjllOTAxZTYxMDBiMGE4NDVlOGYzOWM5ZmZlOWQxZTk5OThhM2QyZWViZjY3IiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 18:02:12 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1013851178\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1479134762 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">jEiKRXRaeQw7kpqtZmXNWu3bpmEnaxKAnNI8mVhc</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>17</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1479134762\", {\"maxDepth\":0})</script>\n"}}