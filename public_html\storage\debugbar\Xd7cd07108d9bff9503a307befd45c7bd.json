{"__meta": {"id": "Xd7cd07108d9bff9503a307befd45c7bd", "datetime": "2025-06-26 16:00:06", "utime": **********.487633, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.019112, "end": **********.48765, "duration": 0.4685378074645996, "duration_str": "469ms", "measures": [{"label": "Booting", "start": **********.019112, "relative_start": 0, "end": **********.431412, "relative_end": **********.431412, "duration": 0.41229987144470215, "duration_str": "412ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.43142, "relative_start": 0.4123079776763916, "end": **********.487652, "relative_end": 2.1457672119140625e-06, "duration": 0.05623197555541992, "duration_str": "56.23ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45648240, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00297, "accumulated_duration_str": "2.97ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.462346, "duration": 0.0018700000000000001, "duration_str": "1.87ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 62.963}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.473394, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 62.963, "width_percent": 20.539}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.4795442, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 83.502, "width_percent": 16.498}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "ET1WIQmDwH5DmIalEePkmJkdqregxR405rAMGD3B", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ET1WIQmDwH5DmIalEePkmJkdqregxR405rAMGD3B</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1842 characters\">_clck=1lqc4d8%7C2%7Cfx3%7C0%7C2003; _clsk=1h4itwx%7C1750953601700%7C4%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlBNaHZ4VnplVjR3czJuajF3SUh3c3c9PSIsInZhbHVlIjoiSUZNZ3pxbUVKcklFYnJYcUFHNS9JdWF2cjRpdGtnbHdUbWhmUGhMSWw5RWlYQU4ySjRlYzF6QlZWRG11emw5RmRFc2VLRERFOGdSb011UWsvZ0JSQXFNNU1QeEIwSytCalN1bk8raUtXekNWaFhFTlBheGJISmtUS24xci9rTUtVeVozWUJEcWs2dTFnSnRIWTRJM1hsQU5HWXNEblkrSFVnVEt5UHdYY1d2L3lCOStvQzdJKzNPV0pmUVo3bzlpWVVHci9BanBhMnR6WDd2ZzIrSC9vSFQ4YTlCMCtrWFRhNDIrYS9hWjFoQkpYVXAxWUlIV25UMDhNSmhzZlB2UzhVbVR5NUhtMU05VjhwbXk0cHlMeHNlcVJxV0VnR1R4MzgvUFE5ZUpuSkNldzh1T2tOSG1tK2pGd0dCR09xNXN3YWh1V1VUZ2MrN3VPWkhzSHhvOWcydlBYY0QvakdnWHRMNjN4R1djcU1EYkcyRFBlekRqTGdEK1dwZGpoZldIU2ZrU3BsVG5QVVZKNTE1NjZxOVJKQTd6RnZ1KytXYmEwdnluZ2w4akJjQnpUdUZYNHhBRWtvcjA0ZnNpWFhKNmJpOWM3elJqUnBIWlFKcHdiZjMxbEgrRDVSVUdRMHZ2WlkyaXFNK21EeEJJSFRDZFJiVm9sRk5qbzVYS1Q3QWgiLCJtYWMiOiI5YzgwZjdiOWViYTliMzk0ZTFhMDhkZDFiNTQ3YWZlZGRiNGMzYzVmYzAxMTZmNzg1ZGZlNzI4NDhiN2I2OTFkIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Im5zbGZlUWwyN1BZTkFvbTJDKzFJWlE9PSIsInZhbHVlIjoiWlZPV1NWbTdneGdNaU1HRUt0RW9mUzhENllYTUhuenczcmpmamlsN1F3MExCTWdVSEVsUFRKcVJrK0lvS1ArRTd3ZGpOT3ljOG14R2o4elJlMGRsNC9zbHo2ZjRlQ1JhUWZ2dUZ6MGxHLzJvYlBxckNsVW1xZFYxODlnTy84U1JDZjVFWnQrVjhhMHRUVjdjaWZ1ZUIrdmgzWFh2YmxyZ2dKK1R4SlhkdUtBQzBzcHpLM29aSWRWTXFsVDR1eUMzUWhDUnhhbThqTnNBNmNyaVVoNkJQWE9YRFRHWmVDZTRTL3VLY2lTNWdUZVkzZXpseGFXbXkyTUo5UzhLMkhBdmpKckcrSGhqNnJXcmE0MFhKUnc5b1Z3dmlpSmtkajdxZ3NKbHJmSmhOOU01VnkyRnV0OEJyVlBzZVVyWHJPbmZRUmhSWkkvR2doWlEraTl3SEIvL1FZem96RHdiV1EwNTBQdzlxVmhmanB6VVk1Wmc0T1ZMaUZtbnpqZGY3Qk1JMS9sUTFNVzNReHJKTkg5M1J0MDBwRzNEbS9Hem5xWm1BcHBTTEJ2bFRpMzdaTzlRWS8wejZVSWRobmpKRVhFeGhldTIzemVkbk1MRWNKa1ZoWUhKVTRUZDgvOGN0WFJoejNYQWJhWHZRT1VuYXE5MWFzMW1LTGxUcklGZkxJYXUiLCJtYWMiOiIyZjhmMGJkZTNkZjA4MWFmNjE5NGZiY2Q5N2I0M2JmZTcxYTFhN2IwMDI5NTYzNDM0YzRmZTFmYTY3M2IxM2Q3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ET1WIQmDwH5DmIalEePkmJkdqregxR405rAMGD3B</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GB2QVAT8duF2iPjEZeDSEXBnGVj5OQhrDoibeEka</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 16:00:06 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkloT08xTDlFaTJpWU5ybXRYa1lHUUE9PSIsInZhbHVlIjoiVDNjd0IrZEpvaFJaTE5LUDJTeE1kM2pqVHcrZWUzTUs4N2lBc1loa0JhR0R2Zm9BYng5aTloejVaZ3dpYnZhMVBsWXNHcGl0bVU1MUpmTlhBdlh1ZzJOT2V0R1dnWmwwRzMra00rdGxObjdZaUxtRHBaMU1JZ1dhaEhLR3AvWVBwZDhxWTZmTjRYMjdUUkVaaEtDLzRmTWx1cnU0Z3FCQlpreStYZ1VRL2lZejFjMEpmNnIvNUFOUlpndngrdG02Z00xMDdTVlRwN2tNOG1lY1dkaHFEYjlDSjYyeTVQbUlWMXRlNGtUZDRNYTRkS1JnTVpmdWl3OGlabTdQRnhMNzNhVXk5R3hnb0RQY3F3bDlPdko2Qko5aVhpMTBmZVNIc2tOaFhhaTAxM1RvWHZ3UEE3amt1VVhJWkJEU0E2R2ZzRVYyY1AzdmpHU3UwRUhhRzFWTE4vNzhXQ3hsUmIzR2hXckppMEhEOEZSblB5enhtR0RoaVpsSUNXR21oQlNQb01NcFBJa2xiQXF2NWFNekRaN0g5RnNjeHJ0UklJQUJLUktLd3VJZzIxaUZnaHFmclIyMW1IYjJrZHE5RlRKd0xlVVBZK1lpWHQwWkozV0pGTmY5ZjdkRTJGRFR2MDZPOWNEcW9Da0xMcWRlNndUMkQ1Zmc1SFo2VXBJaWVka1UiLCJtYWMiOiI5ODA1YTc2YzkyZDVhODcxNmFjMzRiNTA4MmJmZDllMGM5MDA2Yjk1OTRiYjE2NmFlYzczZDU5YTYwN2FkZDgzIiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 18:00:06 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IitVOUJLNGprSHRIcDBnZTlPMnM4K2c9PSIsInZhbHVlIjoiTmZKYng5Yi9sOFBCTFVYSEltaFBPUlVuUUgyV1k2Tk9mUS9kdjA2Z0Y2ZTgzOTEyM2E1cFQzOTJ6RUN4Rmpsd1hGdXZFdUVVOUNJYkZ4amE3SFNHTExxY1VUcS9FemtDR2JxL05WcitEc2F0RnVrTjZWRnpGRlJ4ZXpJUWh4cWtTRFdrVVh0Z0Z6cnYxRS9YU21HaUtMeWFrVzc2ZHF4NExaeEZzWVpBMkVWcGk0M2NyNHFTRHN6TFBNY29sdHlUam9jQTNsV2Y2aURIRW0vd3NYRTVQZnkxZ1NZWTVCQVV5aUU2TG53bmttZnhITkhNSFZER2VFNHpvdC8vK1J4T25zRUlVc3ZRMDdYcXFyd2psblI1NnFNazhTZzU5aCs5OWgzc1djZ1RHbVZzaExwM1pRZEJqQmlXOHpzMjBtVGM5d00xRHc1QUR5TExkejViVFk5ZFVLRDF5aGtRbG5IMGRPQ2NFSFJKalE4L1JTNzFOWkEvd2pGbVAxQVhnV0hZL3hNYmh0MDRiNkFYZHBsUzNIdGMyeWxtdXFPK1dJWDRmdUMrbHVtSG02SEROdXRVQXVhNVQyVyt0VnoxRHViNm5XMTZIVGVleGN4YUMvSWFLcXF0a0x3c2R6WmxlWk1HQ1QvSEFPL0g0RjNaaHNzeHU4dW1QSEg1eHZWQTVzSnAiLCJtYWMiOiIyMmJlYjZhNDgxNWYwYzA4ZTAxYWYzZTIwZTc1YjJlOWVjMjlkZjc4OTdiZjhiNzkyNmViYmQ2MDY5M2I3MGI1IiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 18:00:06 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkloT08xTDlFaTJpWU5ybXRYa1lHUUE9PSIsInZhbHVlIjoiVDNjd0IrZEpvaFJaTE5LUDJTeE1kM2pqVHcrZWUzTUs4N2lBc1loa0JhR0R2Zm9BYng5aTloejVaZ3dpYnZhMVBsWXNHcGl0bVU1MUpmTlhBdlh1ZzJOT2V0R1dnWmwwRzMra00rdGxObjdZaUxtRHBaMU1JZ1dhaEhLR3AvWVBwZDhxWTZmTjRYMjdUUkVaaEtDLzRmTWx1cnU0Z3FCQlpreStYZ1VRL2lZejFjMEpmNnIvNUFOUlpndngrdG02Z00xMDdTVlRwN2tNOG1lY1dkaHFEYjlDSjYyeTVQbUlWMXRlNGtUZDRNYTRkS1JnTVpmdWl3OGlabTdQRnhMNzNhVXk5R3hnb0RQY3F3bDlPdko2Qko5aVhpMTBmZVNIc2tOaFhhaTAxM1RvWHZ3UEE3amt1VVhJWkJEU0E2R2ZzRVYyY1AzdmpHU3UwRUhhRzFWTE4vNzhXQ3hsUmIzR2hXckppMEhEOEZSblB5enhtR0RoaVpsSUNXR21oQlNQb01NcFBJa2xiQXF2NWFNekRaN0g5RnNjeHJ0UklJQUJLUktLd3VJZzIxaUZnaHFmclIyMW1IYjJrZHE5RlRKd0xlVVBZK1lpWHQwWkozV0pGTmY5ZjdkRTJGRFR2MDZPOWNEcW9Da0xMcWRlNndUMkQ1Zmc1SFo2VXBJaWVka1UiLCJtYWMiOiI5ODA1YTc2YzkyZDVhODcxNmFjMzRiNTA4MmJmZDllMGM5MDA2Yjk1OTRiYjE2NmFlYzczZDU5YTYwN2FkZDgzIiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 18:00:06 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IitVOUJLNGprSHRIcDBnZTlPMnM4K2c9PSIsInZhbHVlIjoiTmZKYng5Yi9sOFBCTFVYSEltaFBPUlVuUUgyV1k2Tk9mUS9kdjA2Z0Y2ZTgzOTEyM2E1cFQzOTJ6RUN4Rmpsd1hGdXZFdUVVOUNJYkZ4amE3SFNHTExxY1VUcS9FemtDR2JxL05WcitEc2F0RnVrTjZWRnpGRlJ4ZXpJUWh4cWtTRFdrVVh0Z0Z6cnYxRS9YU21HaUtMeWFrVzc2ZHF4NExaeEZzWVpBMkVWcGk0M2NyNHFTRHN6TFBNY29sdHlUam9jQTNsV2Y2aURIRW0vd3NYRTVQZnkxZ1NZWTVCQVV5aUU2TG53bmttZnhITkhNSFZER2VFNHpvdC8vK1J4T25zRUlVc3ZRMDdYcXFyd2psblI1NnFNazhTZzU5aCs5OWgzc1djZ1RHbVZzaExwM1pRZEJqQmlXOHpzMjBtVGM5d00xRHc1QUR5TExkejViVFk5ZFVLRDF5aGtRbG5IMGRPQ2NFSFJKalE4L1JTNzFOWkEvd2pGbVAxQVhnV0hZL3hNYmh0MDRiNkFYZHBsUzNIdGMyeWxtdXFPK1dJWDRmdUMrbHVtSG02SEROdXRVQXVhNVQyVyt0VnoxRHViNm5XMTZIVGVleGN4YUMvSWFLcXF0a0x3c2R6WmxlWk1HQ1QvSEFPL0g0RjNaaHNzeHU4dW1QSEg1eHZWQTVzSnAiLCJtYWMiOiIyMmJlYjZhNDgxNWYwYzA4ZTAxYWYzZTIwZTc1YjJlOWVjMjlkZjc4OTdiZjhiNzkyNmViYmQ2MDY5M2I3MGI1IiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 18:00:06 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ET1WIQmDwH5DmIalEePkmJkdqregxR405rAMGD3B</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}