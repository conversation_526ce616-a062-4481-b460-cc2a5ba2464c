{"__meta": {"id": "Xc9fcc0ff484224c21c113c1df21cee98", "datetime": "2025-06-26 16:04:43", "utime": **********.32067, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750953882.869823, "end": **********.320686, "duration": 0.4508631229400635, "duration_str": "451ms", "measures": [{"label": "Booting", "start": 1750953882.869823, "relative_start": 0, "end": **********.244559, "relative_end": **********.244559, "duration": 0.37473607063293457, "duration_str": "375ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.244569, "relative_start": 0.37474608421325684, "end": **********.320688, "relative_end": 1.9073486328125e-06, "duration": 0.07611894607543945, "duration_str": "76.12ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48717616, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.00703, "accumulated_duration_str": "7.03ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 17 limit 1", "type": "query", "params": [], "bindings": ["17"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.276789, "duration": 0.00159, "duration_str": "1.59ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 22.617}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.2860968, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 22.617, "width_percent": 8.25}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 17 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["17", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.29977, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 30.868, "width_percent": 7.255}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (17) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.301669, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 38.122, "width_percent": 6.117}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.3066661, "duration": 0.0026, "duration_str": "2.6ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "kdmkjkqknb", "start_percent": 44.239, "width_percent": 36.984}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.3117602, "duration": 0.00132, "duration_str": "1.32ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "kdmkjkqknb", "start_percent": 81.223, "width_percent": 18.777}]}, "models": {"data": {"App\\Models\\ProductServiceCategory": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 28, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 17, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1985597659 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>17</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1985597659\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.305695, "xdebug_link": null}]}, "session": {"_token": "PKqZteC3IabV2rdCxsXBMLnTb72fzH4qdR2KdQSn", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "17"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-1126371658 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1126371658\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1140576239 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1140576239\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-569940533 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-569940533\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PKqZteC3IabV2rdCxsXBMLnTb72fzH4qdR2KdQSn</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1bowxa%7C2%7Cfx3%7C0%7C1999; _clsk=1osc4j2%7C1750953875247%7C2%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlkzSHdBeWdrSXR6SHNwNjJGMENhZlE9PSIsInZhbHVlIjoia0NNZkx6dC9ZVzZXNzRsVnQ2T0s1K1lLSmZLYXVlOHF3V2ZJdHpYQjRmRnZNR1I5b3F0dDA0ZVEwQWV5bjNPNlhURE8wR3hINUk4QzhWSVFuVWlwcHlXMExEUVZIaiszV1hqSWdySjZkRzd2TDZvNGVrM0dJYkoya1NIMk9pbzVuSzR3NjJ4aXcwUFMwVFZydzhLTTlka2g4YVY1QVpUcGVZY0JYcXl3R2ZvZzJZRjR4SlAvaFhOWVlQMHZLWHJHUnJiWUNac1FQMW1ud3h0engzNW1NVUZZOEhCVWNHd0U1U29zcCtLVlFTZVVPU1dReklGTVJrb2M0bXNhaWt1VkpsTERJR3JDQnJLbWJqTjFLaE14UzVmbXM4WTlIMCtXcFFaTEFLa1JzcUhpSmpWQjZsMEllWEhRS2NpVWNSbDRTN0kyaEJFMDB4Nnh0STlvSGN5a29YQVFhNDZ6MFhOb1hZdjdrWlBWZ2oxdmp0VWgzSURQVlRPcDkvWjlueG4vWDdCbmhUeG1ZUjZ0MUVHR2NkZ0h6TTMyZjk1VWszVjdCKzZpVmx1SGxrSkpzeWQ4VkZ2MXRENzE1bkhMY2lBcDhxUkFIaTdEdEVYZ2F6VzhlbmtXQ2kvUlUrUkpLZ1VuY2thdEZzam83TFowcXhIQlFhbEFRS2txQ0xCbm0raE8iLCJtYWMiOiJiMTQ3YjgxY2NjMmViYTljMjg0MmNhZjQ0OWJiMGI3YWFjOTE5ZjM4NzA2NjNmMjg2NjYxYWI1OTM2ZWIyOGMzIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkVwQWZMMHk4aGtJR252c2lOTHJqQWc9PSIsInZhbHVlIjoiSkRISnF3RExJWjZ4cGllSjg2Z0NUcDJBQ0hOWmRMa2RvNUM5d29tVjBBY3VVM1FKMXd0VVp3L0diTTl1OW9ibGlvSGd5YXRPZVdLbVhwSThRSDQ4VkhheHVBYjZ3OHhKeWNmbG9VVjZiRlR0blN5R25EMHNSaW56Yi9RWUFZSmtnWS8rT01sVFR2L1Z0SGVHL0ZwUmdWaS81d1VpeXc4TTJCNnF0SjRJRjAzRWpiWVY5aUJzSWErbWg1NmMzTlZpUlpsMy8yT3NqVjJYZUZSMm11eFhhblFpOVhSOVNPY3I4V2JVS0JHZXdLSGl2cUsxZGlCL2VSeW0wZDNKeEZoVVJiWDBQUytOWUI2SURPcWQwb1ArSytGOVVMQ1o2OFZCaldQQ2FxaGdzWmRHVEhPb2gyK09hbzV3SWFNQlJ6RDJ6YVhDa3VHWHkxOXZ0NFVyVFgybXU3OEJiRzRma2NmdHllb21oZHZYeWpnSy9rWGlZUXNyaGFmZ1ZFb1RjU2pxOFJxS1ptRkdwdDFJYVZWQ1FhOEp2VkVhYklOSFN5cWpTWWg3K3JwT1ZqcTRrbXRKcE1LTVdCVlpZMi9nWlovU2lqbERUamhIaElsS0F1ZU1HajMwMzRlcGtQWS9Lbmh0S3MvdTRiNk1nZGdra3JXZEhNY0xjT2hkOHNGbUlBRUoiLCJtYWMiOiJjMzIyMmI4MjM5NWJjNjE5ZGE4NDJiNGEyMGRkMDJmMWI2ZjM5OTk3N2NmNjJmN2RlM2JiM2MwYTNlNTk4YjA2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PKqZteC3IabV2rdCxsXBMLnTb72fzH4qdR2KdQSn</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GUUMfjgJKgBqP5oaDcRP4Rfu0FM4k6V9zWwhkyoG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1612189727 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 16:04:43 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Iit1eEluUENWa2lEMDdmbXZQdkNpNEE9PSIsInZhbHVlIjoiMGRkV1ltdG5RcGNSeGo0YWRRamdOTDBLTHJ2bnpIdzdPVmlpMWZVN0VhM255ZklRZXFnWEFReTF0bnlFZUhTZzlhN01ib3VVU0pLS3ZESGlFN285Z0w1bWM4U2t4aEpIMmVoM253dG1reVI1bjI5aS9nU0k2RUNrNU9MQSs5SU1HeXV2Q2dqTm9kK2wyUCtJSGFiSitaUXBFc1ZGMWkrNDh4VkFhVXp3Q2xNeTJoVTE1ajl3cFZKeHJlTFFieURrbW1aeFYrUkpRVHJvMWhBc1lSREVGTkVXRURucU9OY1RSUzhDbmpxU1pDcFAxVDFJWVp2alB2d25NTTFKYm5kZ0J5US91cHE4N1FiSjd0OU5UaGFpbGM2MmRUODd6cVdObXB0UW8zS05aYnhSNEQxSUdQR1IvUlBldXIySitCYnZpUzRQYzkvY2ZrbE5uRXgrR0ZDSWRJckYzc1dIZTVGNlNvVllMb2h4UmpTaHkwL1I4VmF2TnhRUkl5QklWdlJwTFlTYWduTVFSRG1INWU1Nm0zcGVadGR0aHVyS0kxVkEyRVNTK0pscVFDUmE0bkZ5dTlvU1JKWXV0MFk5dnVZRFNkYUpxR2lLOGN3L0ZqSGVuQk1aSmZTRlZMWG1CeEROK1VZNFpTTFdtb3pRSitSNHh3cXUvelFreU4wVi8xSWYiLCJtYWMiOiI4ZjY0MGQxNDEzMjk5NjlhMDFjYTE1MDA5ODJlMjQwODQwZTUwNzNjM2JjYjFjNjg0ZGVlYmI1ZmExOWMyMTcxIiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 18:04:43 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ino4TWlac2lscTlDWndQZVVpZ2lkNlE9PSIsInZhbHVlIjoiUUhVZTh6Tk5ScDhxN2tXdW82YTlFeEpWMWpINGNjTFVSOXRlVTN1UUZqNEpSWTZFUllOVVlla3FycUYrcTRpUFZtaW92aXdhZ2pOTTdLNUxMTjJZT2tjY0tmZ2M1YndicGlWQWRneW4waU03Q05SK3ZWc01mRXVjSzhtT3hoSExGQk9xMWRQV2VzSjVFc0pTcERtYkhqOEZLNWJrSzRiYXhCaEhFc0hUL0QzUUJ3R09laUFnV21rNXdpVDk2QmZwZm53b3RSWldBdzN2RmYyU2ZLTWNlSHdqNlZ5NXFia1NDMzg3d2YxN3NGM1czMmlxSHEwZHpFYWdvcll6VjhCSTJXN0FSWE1VVGRXaDg0ejVMRzdFVzR5UEpyMFhGQUZXTG0zWWplME5MYkhtQnJaYUU3QUR0enBWQWVOR2d4ZnhCSGhUbTRUYzRUVVlPOWRSR2VGbWM5ZkZBYXBCNUo2QTdLSjlPcDhHelhvWnFoczFFN3dxMTdGbkIvWDVLUGRBeFVvamNPQW9hY09JQ240aDJjQmtoNVN2QWZuSDNCR0tvbVlJTnhFRnVpSkFFeEJIUTNGblRHakw0a1pxR3lVU3FRVVVLMjJweEpZMnQxSjBCR0cwY09DUDc0ZFBSQk5kTWNHT1hYZkhsbWtwekNHb01mVE00cXh6RmhOUDN3Y08iLCJtYWMiOiIzMDY3ZmExZjcxNWM1N2Y3NGU3YjcxOTcyMWMyNGU3MmY0ODlkNGFjYjNlMWUxODEyYTNkOTU5NmVmZDg4MmUyIiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 18:04:43 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Iit1eEluUENWa2lEMDdmbXZQdkNpNEE9PSIsInZhbHVlIjoiMGRkV1ltdG5RcGNSeGo0YWRRamdOTDBLTHJ2bnpIdzdPVmlpMWZVN0VhM255ZklRZXFnWEFReTF0bnlFZUhTZzlhN01ib3VVU0pLS3ZESGlFN285Z0w1bWM4U2t4aEpIMmVoM253dG1reVI1bjI5aS9nU0k2RUNrNU9MQSs5SU1HeXV2Q2dqTm9kK2wyUCtJSGFiSitaUXBFc1ZGMWkrNDh4VkFhVXp3Q2xNeTJoVTE1ajl3cFZKeHJlTFFieURrbW1aeFYrUkpRVHJvMWhBc1lSREVGTkVXRURucU9OY1RSUzhDbmpxU1pDcFAxVDFJWVp2alB2d25NTTFKYm5kZ0J5US91cHE4N1FiSjd0OU5UaGFpbGM2MmRUODd6cVdObXB0UW8zS05aYnhSNEQxSUdQR1IvUlBldXIySitCYnZpUzRQYzkvY2ZrbE5uRXgrR0ZDSWRJckYzc1dIZTVGNlNvVllMb2h4UmpTaHkwL1I4VmF2TnhRUkl5QklWdlJwTFlTYWduTVFSRG1INWU1Nm0zcGVadGR0aHVyS0kxVkEyRVNTK0pscVFDUmE0bkZ5dTlvU1JKWXV0MFk5dnVZRFNkYUpxR2lLOGN3L0ZqSGVuQk1aSmZTRlZMWG1CeEROK1VZNFpTTFdtb3pRSitSNHh3cXUvelFreU4wVi8xSWYiLCJtYWMiOiI4ZjY0MGQxNDEzMjk5NjlhMDFjYTE1MDA5ODJlMjQwODQwZTUwNzNjM2JjYjFjNjg0ZGVlYmI1ZmExOWMyMTcxIiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 18:04:43 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ino4TWlac2lscTlDWndQZVVpZ2lkNlE9PSIsInZhbHVlIjoiUUhVZTh6Tk5ScDhxN2tXdW82YTlFeEpWMWpINGNjTFVSOXRlVTN1UUZqNEpSWTZFUllOVVlla3FycUYrcTRpUFZtaW92aXdhZ2pOTTdLNUxMTjJZT2tjY0tmZ2M1YndicGlWQWRneW4waU03Q05SK3ZWc01mRXVjSzhtT3hoSExGQk9xMWRQV2VzSjVFc0pTcERtYkhqOEZLNWJrSzRiYXhCaEhFc0hUL0QzUUJ3R09laUFnV21rNXdpVDk2QmZwZm53b3RSWldBdzN2RmYyU2ZLTWNlSHdqNlZ5NXFia1NDMzg3d2YxN3NGM1czMmlxSHEwZHpFYWdvcll6VjhCSTJXN0FSWE1VVGRXaDg0ejVMRzdFVzR5UEpyMFhGQUZXTG0zWWplME5MYkhtQnJaYUU3QUR0enBWQWVOR2d4ZnhCSGhUbTRUYzRUVVlPOWRSR2VGbWM5ZkZBYXBCNUo2QTdLSjlPcDhHelhvWnFoczFFN3dxMTdGbkIvWDVLUGRBeFVvamNPQW9hY09JQ240aDJjQmtoNVN2QWZuSDNCR0tvbVlJTnhFRnVpSkFFeEJIUTNGblRHakw0a1pxR3lVU3FRVVVLMjJweEpZMnQxSjBCR0cwY09DUDc0ZFBSQk5kTWNHT1hYZkhsbWtwekNHb01mVE00cXh6RmhOUDN3Y08iLCJtYWMiOiIzMDY3ZmExZjcxNWM1N2Y3NGU3YjcxOTcyMWMyNGU3MmY0ODlkNGFjYjNlMWUxODEyYTNkOTU5NmVmZDg4MmUyIiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 18:04:43 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1612189727\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1504472268 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PKqZteC3IabV2rdCxsXBMLnTb72fzH4qdR2KdQSn</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>17</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1504472268\", {\"maxDepth\":0})</script>\n"}}