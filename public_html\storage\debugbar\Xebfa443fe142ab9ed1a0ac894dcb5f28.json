{"__meta": {"id": "Xebfa443fe142ab9ed1a0ac894dcb5f28", "datetime": "2025-06-26 16:00:09", "utime": **********.9088, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.490688, "end": **********.908815, "duration": 0.41812682151794434, "duration_str": "418ms", "measures": [{"label": "Booting", "start": **********.490688, "relative_start": 0, "end": **********.845227, "relative_end": **********.845227, "duration": 0.3545389175415039, "duration_str": "355ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.845234, "relative_start": 0.35454583168029785, "end": **********.908816, "relative_end": 1.1920928955078125e-06, "duration": 0.06358218193054199, "duration_str": "63.58ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45648192, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.014270000000000001, "accumulated_duration_str": "14.27ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.871485, "duration": 0.01315, "duration_str": "13.15ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 92.151}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.8956351, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 92.151, "width_percent": 4.345}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.901995, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 96.496, "width_percent": 3.504}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "ET1WIQmDwH5DmIalEePkmJkdqregxR405rAMGD3B", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/bill\"\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1421568610 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1421568610\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1879352563 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1879352563\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-587100545 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ET1WIQmDwH5DmIalEePkmJkdqregxR405rAMGD3B</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-587100545\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1290918597 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost/bill</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1842 characters\">_clck=1lqc4d8%7C2%7Cfx3%7C0%7C2003; _clsk=1h4itwx%7C1750953606371%7C5%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjNhVGpIWGtSdWlMV3JjZ0w1QVpvbHc9PSIsInZhbHVlIjoibmFOSHkyWUtxWlBWSVpjbUNzdjFRaFQ2NWJHaC8xTWlnN3UyaUs5REVRTnZEUlhnYVhYbmJRRDlKZzl2Um5OVThhcnJJZmN4NU9RNUsxQkdld2x0dDJsUFh2eXFaTEREWVdUOXQrNmJJZFFpWEtka1E1eXR0VzBQdUpjeEUwUUtockErYWpPRDkrbDhJQlZaMzV3VWlwOTdWSHhjRFZYOHU0YWRWM3lzMnZYN0MxaUlTZjkreEh3LzlMZ2krUksrYXpMTmgveTRNdFJEb1N2VzFWMEJjRGlkeVVNRFVGdlVpVlViaXE4MDhDSm84dXFsNGZOV1R2L0NjTDROUXNiVHlod3FTOUJLbDdjY3E5V1FYZDNYbEN6YUR3Ukh6THAxS2FVbTdnV3VaUjNhbWJROVlLNTFIdU1yYTg3RGdiV1R6SmUxY0pXcmY5U2Q3dmdPczVWRGptcVdoeFFVTTVqTzNEYUthMExSaWJobkx5WldvN0F4alVDY0dQZ0pqWWxPc0ZQNStsZG9vRWF4d0Y4MFBxNk9saW5sc1FaZUZ1aXNVT0x5TzRna3UyaTMrVnlMeGcwOVlWaEM5US9Tck9ac2hzV2hQdndlN2dHVXRiU1FRM2oyeWM4aHdLaDVDNzBDL0NsOS9EUGVNcVhUdmdKajYySFl0YUtqbVB6WldCcjciLCJtYWMiOiJiN2NmODQ3YmYxNGZlYzVhYmU2OWQzNTUxZDdmMjhiNTgwMTFjNzA5MDk0MjU1NDkyNjZiODA0OTdiYzEwN2IzIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjBSSCtaUXNuNkhGMEZCZVJHalNJVFE9PSIsInZhbHVlIjoiSGJZSjNRb0dIcVBqL0ZTME44eERWSDRZRjFtdVN1ci81Y0o2Y0dFeHRIWTV4TTl1VnNtUnVQd2tSemVJQkNzOFJEcmNCL1NQWVJCdGxra3hNcVVMYS9uajBra2VSZGdPSEhNd1Y3UTc3ZFZBVk85WHRwVG9WNzZaVEFWb1E1VXhqSU00SkYxb080MTRhVzJKTVV6MVZ5OFVKVUV6K3FveWd1QUlKL1l5QWRqeUQ4cWV5YzBtdDZ5bTZtclpQZjRweGVSN2l1NVJkZlRzK2ErSUx6c3BGUFpTa01NV3M2RVZEc05VMEdzVkhFeTRxSjA4RnpWSlNxdUhXZ3l2Ujd6b3U0Rm43Zy9xaFBFWjVDQ1dLcUJwaGZVWmdkNUpHd2M1em9taHZCUDRBaFlJWjB6WEVzL2ZqeVo4NVY0UjFCQURZMWlZNG1rT1Q1amlncEhPb0lTZUx5V2VOb1pIeUc5NFovMzlJMnFSbFpMcGNIU21aT1dRZGE0Q1NmSDdTc3F4OWNLT01JcTBsRk9pdFVxc3NCMjdhUEdPNTBwM1d5VkUyMlFoSEsxNFhPTmt5ZWZSUmJrREE2ZTkwNUFNWUJsVjVhblRtRzFiWUpKK0Iya05mWnJ0b3krVGhyWmRHb1YrOFd2RHNVM2ROTlpJdEFMQUozTlB2T3VlTHMwT3NOQXgiLCJtYWMiOiI1YjAxZGY4YTQwZWZjOTU2Nzc1ZTAxNThiYmI1OTIzMDY2NTY0MzJiYmE5NDA0YzM5NDNkMDQ0MDJmMTRiZTM0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1290918597\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1535917037 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ET1WIQmDwH5DmIalEePkmJkdqregxR405rAMGD3B</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GB2QVAT8duF2iPjEZeDSEXBnGVj5OQhrDoibeEka</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1535917037\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1990474232 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 16:00:09 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjVHeXh6bUJkWXE0NVN1VmRZd2NtVEE9PSIsInZhbHVlIjoidzY5bFJRY0x3aHJDL1ZCWHZhQzJjUlFtK2dNbys4LzR2a3pzY0gwbFNTUE5vWlF3T0pSS1BsK2tFSXJSM3YrVm1iZkMyTXRQTDZudEFMZ0UvVGVLUEVWN3daVzZzQzRkVTNMZmdzQUpqb25tV09MTTczYTFoOU5SQ250TmdTeE5YRE5TTFdLdnJMZEErK3E1UFhyVUwvdXFHT3YrMG9jQTgwWlY4YnFiQ1FiRDZMTVpiVFhLa1F3eFR1L1NUdGNKeWdmbnpNT3YyeVFXNTlBTzhwM1ZlVzlsd0V0VllZLzl2TWprL1R6Wm1xdlM0RFpKYXdJb05Dc25NajBTTjEyRWM0djRRaENpeFNtd2xzaVAxM05jd0EybG1FMEtrOWZOZXJ5M2RZalEyeEZ0bndJS0RUZHNyN1R3Q3NOaGY1MWZGbVlGeVNRc0lINzBUWVhhaWV3UVk4dXljYmVoQUl1Um10a0xwbEJLSGk5clFEK3J0b0VaQThyaHU0TmVDaGs4US9RcWx6T1VYUTJRZjdqcnJBdWZjWTcydzViUnBKb0gyaEYrNmpMV2FQeFpreEQ5aHJjNTdEbWxnSi9JVDJ1cHg2VHZ4eVFaNDhRYzAzd1NMbWo5Q1NxNnJ4U08wbmw3WVlWTEpaLzVqMjd2VC9LakdrSlVOeTk3aW5SaHpwaDgiLCJtYWMiOiIwOWQ3ZDk2OWVjZmE5YTc5YzQxZGVlZGY2NWQxZGZkNTlkZGFiODhmZDdiNjZmYmMxNDk1YTU4Y2UxN2M5MmIxIiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 18:00:09 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImtKSzd2aDZhK2NieGg1aVRFejd6eWc9PSIsInZhbHVlIjoiS3NId0NtUWxESXZ4NlkrMk42REw3VG9ZR1JucHRwTU9zOUhKTDNkZTNUYjNUaWlRSjdSVWJQWWFDdWIxYWl5bnBoZ1hROHpsUlhXZDVpSjdxT0pKUXNoTWt0dCt0VVp2aWI2ZkVseWlqWGRrclhJU0h5em5mV0VOcUxLZVBkYVVRclJ4UFFRbG1ZN29mSnFkdVd2VmttdERrMmFWNWNrZVpMUWZSZGg3QVVydDZTSzFOa1ZoNlRON2NXbDBIZ0sxaFVvV2dwemhwbVpXR29TRE1XSEl5YjAweVE0bzJBNGtFNTBXM0J4ZlZaWjViRzIvNjlCdGtXc3pDUzRLS0xaMlNDSnZQdmtocTlRWm5mTTViMDFTdWNEU2ZhcWVuZ0dQV2liVG5aeTA1OE5QWUlFYmp4R3diSjZ2SEFKT2hOMmRqWEQrbFpoTWdYVC9xYklqVCt3aVN6cHBlZHlBNXhVRHA5NUE3cHlMSWlUZkYyRXAzckxjVmFOd3gxVzZqeTI5M3ZoQThJQkRISDRDR3Jpc0FIS2d1SHdBMVpDOXBTNGpSVkJ0Y211ZEp1clFuemtwQytUcDlNc3pNS0ZFc2pka3pEeDljNWNOUXU0bVdZY0Ewd3BRZHBMbFRtQUc3ZWU3S1E3Vjd4N0N3aUkwYzZxSjc2RG5VSkNNSkJML1ZDQ3UiLCJtYWMiOiI0OWY4M2Q4ZmQ1ZmEwYjc0YWI2ODBjOGYzMjllMzcxZjc1YWYwMmFkN2RjOGZiY2Y2YTc0Y2Q0MjRlNzlkYmVmIiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 18:00:09 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjVHeXh6bUJkWXE0NVN1VmRZd2NtVEE9PSIsInZhbHVlIjoidzY5bFJRY0x3aHJDL1ZCWHZhQzJjUlFtK2dNbys4LzR2a3pzY0gwbFNTUE5vWlF3T0pSS1BsK2tFSXJSM3YrVm1iZkMyTXRQTDZudEFMZ0UvVGVLUEVWN3daVzZzQzRkVTNMZmdzQUpqb25tV09MTTczYTFoOU5SQ250TmdTeE5YRE5TTFdLdnJMZEErK3E1UFhyVUwvdXFHT3YrMG9jQTgwWlY4YnFiQ1FiRDZMTVpiVFhLa1F3eFR1L1NUdGNKeWdmbnpNT3YyeVFXNTlBTzhwM1ZlVzlsd0V0VllZLzl2TWprL1R6Wm1xdlM0RFpKYXdJb05Dc25NajBTTjEyRWM0djRRaENpeFNtd2xzaVAxM05jd0EybG1FMEtrOWZOZXJ5M2RZalEyeEZ0bndJS0RUZHNyN1R3Q3NOaGY1MWZGbVlGeVNRc0lINzBUWVhhaWV3UVk4dXljYmVoQUl1Um10a0xwbEJLSGk5clFEK3J0b0VaQThyaHU0TmVDaGs4US9RcWx6T1VYUTJRZjdqcnJBdWZjWTcydzViUnBKb0gyaEYrNmpMV2FQeFpreEQ5aHJjNTdEbWxnSi9JVDJ1cHg2VHZ4eVFaNDhRYzAzd1NMbWo5Q1NxNnJ4U08wbmw3WVlWTEpaLzVqMjd2VC9LakdrSlVOeTk3aW5SaHpwaDgiLCJtYWMiOiIwOWQ3ZDk2OWVjZmE5YTc5YzQxZGVlZGY2NWQxZGZkNTlkZGFiODhmZDdiNjZmYmMxNDk1YTU4Y2UxN2M5MmIxIiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 18:00:09 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImtKSzd2aDZhK2NieGg1aVRFejd6eWc9PSIsInZhbHVlIjoiS3NId0NtUWxESXZ4NlkrMk42REw3VG9ZR1JucHRwTU9zOUhKTDNkZTNUYjNUaWlRSjdSVWJQWWFDdWIxYWl5bnBoZ1hROHpsUlhXZDVpSjdxT0pKUXNoTWt0dCt0VVp2aWI2ZkVseWlqWGRrclhJU0h5em5mV0VOcUxLZVBkYVVRclJ4UFFRbG1ZN29mSnFkdVd2VmttdERrMmFWNWNrZVpMUWZSZGg3QVVydDZTSzFOa1ZoNlRON2NXbDBIZ0sxaFVvV2dwemhwbVpXR29TRE1XSEl5YjAweVE0bzJBNGtFNTBXM0J4ZlZaWjViRzIvNjlCdGtXc3pDUzRLS0xaMlNDSnZQdmtocTlRWm5mTTViMDFTdWNEU2ZhcWVuZ0dQV2liVG5aeTA1OE5QWUlFYmp4R3diSjZ2SEFKT2hOMmRqWEQrbFpoTWdYVC9xYklqVCt3aVN6cHBlZHlBNXhVRHA5NUE3cHlMSWlUZkYyRXAzckxjVmFOd3gxVzZqeTI5M3ZoQThJQkRISDRDR3Jpc0FIS2d1SHdBMVpDOXBTNGpSVkJ0Y211ZEp1clFuemtwQytUcDlNc3pNS0ZFc2pka3pEeDljNWNOUXU0bVdZY0Ewd3BRZHBMbFRtQUc3ZWU3S1E3Vjd4N0N3aUkwYzZxSjc2RG5VSkNNSkJML1ZDQ3UiLCJtYWMiOiI0OWY4M2Q4ZmQ1ZmEwYjc0YWI2ODBjOGYzMjllMzcxZjc1YWYwMmFkN2RjOGZiY2Y2YTc0Y2Q0MjRlNzlkYmVmIiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 18:00:09 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1990474232\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1848673100 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ET1WIQmDwH5DmIalEePkmJkdqregxR405rAMGD3B</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://localhost/bill</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1848673100\", {\"maxDepth\":0})</script>\n"}}