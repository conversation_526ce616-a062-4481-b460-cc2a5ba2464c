{"__meta": {"id": "Xdc10db5eb2621ef82cc0021d840ad733", "datetime": "2025-06-26 16:27:48", "utime": **********.987329, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.574705, "end": **********.987343, "duration": 0.41263818740844727, "duration_str": "413ms", "measures": [{"label": "Booting", "start": **********.574705, "relative_start": 0, "end": **********.913147, "relative_end": **********.913147, "duration": 0.3384420871734619, "duration_str": "338ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.913156, "relative_start": 0.3384511470794678, "end": **********.987344, "relative_end": 9.5367431640625e-07, "duration": 0.0741879940032959, "duration_str": "74.19ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48717616, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.0066099999999999996, "accumulated_duration_str": "6.61ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 17 limit 1", "type": "query", "params": [], "bindings": ["17"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.944361, "duration": 0.00172, "duration_str": "1.72ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 26.021}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.9545858, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 26.021, "width_percent": 6.354}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 17 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["17", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.967974, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 32.375, "width_percent": 6.203}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (17) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.9696438, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 38.578, "width_percent": 3.933}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.973897, "duration": 0.00239, "duration_str": "2.39ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "kdmkjkqknb", "start_percent": 42.511, "width_percent": 36.157}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.978379, "duration": 0.00141, "duration_str": "1.41ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "kdmkjkqknb", "start_percent": 78.669, "width_percent": 21.331}]}, "models": {"data": {"App\\Models\\ProductServiceCategory": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 28, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 17, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-155108143 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>17</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-155108143\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.972897, "xdebug_link": null}]}, "session": {"_token": "PKqZteC3IabV2rdCxsXBMLnTb72fzH4qdR2KdQSn", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "17"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-1303608147 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1303608147\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1950857330 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1950857330\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1830286569 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1830286569\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PKqZteC3IabV2rdCxsXBMLnTb72fzH4qdR2KdQSn</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1bowxa%7C2%7Cfx3%7C0%7C1999; _clsk=1osc4j2%7C1750953875247%7C2%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InZLOFlaYm9Eb1RZTmRQdm9NV2J3eEE9PSIsInZhbHVlIjoiNjU4cUtJdXRJUmdUa3l0Y2E1akQyY1VNVS9ZdHl5R2Q0UlJ2dnpPU1FGSGYxWUEydDdTWFU4UlN5bTRqVWJlMEZFTk9kWUZxaWlCSFNaUHFETmRvVEdMQUdXTitOUGZlSXk3Qk5LM1E5L3dqZ1B5a0RsYk9QblZqVVF3TmFUWEtWSGRFVlBqSEQ2VER4TWZncDFiYjgvNW9DM0MyQ1ZwQkdsSDVScFk0SkYzaHRqNHNoOVlSNXFvdytaYWZ3Y0VtTlpuMWRHM0hFWWUvVXVqL2E1UHppbXg1cFJjRHpnOUtqaTRRbWNCMGJjYURVNGNjRlA3bUZBVnZwdWFGSjdXcTU1SmNlY1EvZDBBRllkSnZ0UmNIN2hBSmxGbjg4WWQ2WTV1UElML1ozWTN3N2xmVmxlaDV3WEo5QjIvRXJzdDNaOFVQNGkwdWxwMGt5eEd3UlRvZUFsVFovRG5CUDdlL01JQ1FPblp3UHdxck9STmw0T211WXpDUmUxWWFCVUh0ZEdLZ0UxYVhNNllIckJqYXdZcndWd3NUcC9vQ0J1QUZDNG5wU21IZExQZDlVMmE1QU13TUdEdFQwUjdpMWt2MWZBTDVOTDhLR1BhMHVCVjFXS2cxbFVCNk5wSWxpelNqRUdURG5yNFdJL0wya1ZxTjhpUmJRbDNUZzd1ZkR1QlMiLCJtYWMiOiI1MGJjZjk3ZTMyNGE3NDE3OWU1ZDc4NjE5YTc5ZDk1OTBkNzQ1MmY4ZjU3ZTRjMDljYjAwYTQzMzlkZGUxNjg2IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjhXNFpIT1FWaUtFWkl3TTdIN2RETFE9PSIsInZhbHVlIjoiS25zMFBNSzZiU2pweFUyUXNDdlhkTDJrQkpLZUNaa2ErTHg4Y1pVbGxyYnQzdXpHa3JGaGk0b1A1QjJ5MGZaUzhlOVFJYzUwQ1lpbkl5OWYxK2JETkdJcmFPanBxTlJMK21TYkQ1K0RsOHE5emVsYVo0ekRmSy8rYkw3WDluSHpXRzdqZ081TE9VNm1DVHhqdmRIKzdHUWZJZ015YnRNZktRSjY2TnpVai9JOFM5ZnRNSndaNzFQVzZ0NVU1RHordFhJSVltV3dvL0d4bGlBK01hOTdlV1NmdVg1eWl5VTVjQTRNTEdHNEI3ZWFHa1czMXp1TkZlS2dTcG04SlcyaDJQOU0wVXMwVW1QNHlBRStoSEpIM3hGOEhaeGtDWTUxTml5OGwrZzNxUkxKcXd4Y0tzZXZYRXR0ZVRPb3ROMEx3NFM3VEZHMVBHOVpNYi9rMjcvelkxQnFKS2ZvVkx5Y3pIOUViTHZMMnRVaElGU3FRSzJwR2QxVTRzc1J1ZnRrcUY0UHVGcWNqZTBTTEhtdFp3dlYvUEoxV1ErL3pnV1ZKdTM4ZVNlWXJPSUdUYXhHbEErRGI3VDQ3d0hYMGNhS0Y2Nmp4Umc4Y0dlVXgyRCtCMzAxZHQ2bzg3MFRPM2RUZ3NyblIrNDA1VmwwVVJLQW4xKzRQVmZoUTNhWjI1c2kiLCJtYWMiOiI5ZjZlNmZiZjI3NmMyMTg2YzYxN2NmNjA2YWQ1ZWE3ZTU5YmNmNzI5MDExN2MyYThkMTdhMDhmYzVlZWZlNWVjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PKqZteC3IabV2rdCxsXBMLnTb72fzH4qdR2KdQSn</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GUUMfjgJKgBqP5oaDcRP4Rfu0FM4k6V9zWwhkyoG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2019360741 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 16:27:48 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InBiWVVTYXFoVU96UXc5alJKSmFiN0E9PSIsInZhbHVlIjoiTWRXTjV5TElDeHowRkR5N3Q0bWIxWEJoME9obXdsWEdhaGNFbnJlMWMzczhQRWhZL1dnbW5RdjM5THRDbGJ2elFOamczRkE3clNuUzFjNGQ3V2Mxa0FpNGN4MlNqSXByVm9vTVFnMm1xWm8vQzYrVWRVOG9MOE1SUFhYSTdDd3NzT0FGdjZrTGxjQzFVbDFPSmFKQ3Jwb2NnNWpBdUpPYjdmZ0tFdyt6YmxhRzlpbTg5S1dJWVVQYW5RL3RBTmdJRVF6U2ZaZERZcldZOXlqenhNVzVVaHBObDdnaTViMisvL2dETjliSTVuaXc0RzJ2RURoeHljNFRNeVFBbk5wVkhveThGOE1MNVNxcEMrdTZiWjlBTDZxKy9XOUFuSUJJUFBzNU9rVndQUWQyRjdnM1hRV2w0ODhDdW9mQ3FwZFpZNS9DTmkveEd6UUJIYW1KSmNxNUZIR09jY0FuWmxodTVWYXBMQ2pvR3plVElGNUJTWWVqYWtMWHJMbzZZUlJmb1RqR0FOajA0QXh6YXhnTWJTd0l2K3ViL1lBdzZvZVp5VWEzWmdmSVcyZ3NWZ3g2UllmWHJ6ZVNZeTZhendDYUY1TGlCS3QydGxVSWg1eDBMSTZGbG1kQkhlTlZ3K2h0d3ZNazRsSWVPN0pqTDVQeElpTzdJaEVjZW40MzlwTHQiLCJtYWMiOiI0YzE4OGI4MjQ0YjE5OWMwZmQzNWE5ZTg1NmI4MzhlNTA0YjBhZTMxOTIyZTEyOTMzNzY3ZGUzYTg3ZjczNmYyIiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 18:27:48 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImppUHlnQUdKVkRsbUZrenhWU1hjWUE9PSIsInZhbHVlIjoiZEF0ZXdlT0t4cjlWbnBoY3FRT1ExRDBmM3JaSlJlZzBhRi9BQW5hMSs3U0QvS0ZrOEF3UXcreklSVUlwUXFkaTFOUkdHVlhMWFFQRkNGL3Y3T1dnbUJlUGhqZ09Ydzk2ejIrZkZoNnF3d2toNGgweXVQNWtqVVRWMS9aTVhDSXZ3MGRoaCs3YTIxWndiSTVyZVBERmxzUU40QTFNSm0vWHZReS9CSW1lTWhkZFdyeG41TXlmVFEyTnlzVnlyR0VaU2FWRWJhcE05ekd3Q3duVnlHNDJ2ai82WUQyMlBDQXZmNVgzZXBoU0x1d2UrY1hlN3UzUHhobEhFOWxCbGtMS2ZEd2lNNHV3R1ZmNzFJVHVVbVd2YXlWbmNPNWFDNjZzVk1jaFNLUFpVVjRsL0RoU0JGTUtrYjBGMUd1Q1RXR1RDdEU2TEt1dTZIeU43bHpTY05ZcFdtRUZHd2ZJYzhlYjBKUzViOWlIdTc5U0QrUGVrL3dWWk9IdVRhbmNFb3A5Vk40aHdyVUgxbE5DbXR1UC9PSTFrRTJlc2JlVDFkZmdlcXpBRnoxdVU4TG9TeXlHY3NZTVFCelVHZCsyTkx1OXdKUDF5bGN3NHFsM2RNb3NwMWQvbVRPN3ZLaWFIcGQ4L1VaRHoyQkJjZ01INE1TaExiNW92K1A3aWdCZFFnNDMiLCJtYWMiOiI1NTA5ZGViNDc1OWQ1YzU0MGI4YTUyYmY0MjgwYzZkM2ViNjYwYmRiZmI0MzVmOWE2OTRhMjk5MTc3YmY3NDhhIiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 18:27:48 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InBiWVVTYXFoVU96UXc5alJKSmFiN0E9PSIsInZhbHVlIjoiTWRXTjV5TElDeHowRkR5N3Q0bWIxWEJoME9obXdsWEdhaGNFbnJlMWMzczhQRWhZL1dnbW5RdjM5THRDbGJ2elFOamczRkE3clNuUzFjNGQ3V2Mxa0FpNGN4MlNqSXByVm9vTVFnMm1xWm8vQzYrVWRVOG9MOE1SUFhYSTdDd3NzT0FGdjZrTGxjQzFVbDFPSmFKQ3Jwb2NnNWpBdUpPYjdmZ0tFdyt6YmxhRzlpbTg5S1dJWVVQYW5RL3RBTmdJRVF6U2ZaZERZcldZOXlqenhNVzVVaHBObDdnaTViMisvL2dETjliSTVuaXc0RzJ2RURoeHljNFRNeVFBbk5wVkhveThGOE1MNVNxcEMrdTZiWjlBTDZxKy9XOUFuSUJJUFBzNU9rVndQUWQyRjdnM1hRV2w0ODhDdW9mQ3FwZFpZNS9DTmkveEd6UUJIYW1KSmNxNUZIR09jY0FuWmxodTVWYXBMQ2pvR3plVElGNUJTWWVqYWtMWHJMbzZZUlJmb1RqR0FOajA0QXh6YXhnTWJTd0l2K3ViL1lBdzZvZVp5VWEzWmdmSVcyZ3NWZ3g2UllmWHJ6ZVNZeTZhendDYUY1TGlCS3QydGxVSWg1eDBMSTZGbG1kQkhlTlZ3K2h0d3ZNazRsSWVPN0pqTDVQeElpTzdJaEVjZW40MzlwTHQiLCJtYWMiOiI0YzE4OGI4MjQ0YjE5OWMwZmQzNWE5ZTg1NmI4MzhlNTA0YjBhZTMxOTIyZTEyOTMzNzY3ZGUzYTg3ZjczNmYyIiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 18:27:48 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImppUHlnQUdKVkRsbUZrenhWU1hjWUE9PSIsInZhbHVlIjoiZEF0ZXdlT0t4cjlWbnBoY3FRT1ExRDBmM3JaSlJlZzBhRi9BQW5hMSs3U0QvS0ZrOEF3UXcreklSVUlwUXFkaTFOUkdHVlhMWFFQRkNGL3Y3T1dnbUJlUGhqZ09Ydzk2ejIrZkZoNnF3d2toNGgweXVQNWtqVVRWMS9aTVhDSXZ3MGRoaCs3YTIxWndiSTVyZVBERmxzUU40QTFNSm0vWHZReS9CSW1lTWhkZFdyeG41TXlmVFEyTnlzVnlyR0VaU2FWRWJhcE05ekd3Q3duVnlHNDJ2ai82WUQyMlBDQXZmNVgzZXBoU0x1d2UrY1hlN3UzUHhobEhFOWxCbGtMS2ZEd2lNNHV3R1ZmNzFJVHVVbVd2YXlWbmNPNWFDNjZzVk1jaFNLUFpVVjRsL0RoU0JGTUtrYjBGMUd1Q1RXR1RDdEU2TEt1dTZIeU43bHpTY05ZcFdtRUZHd2ZJYzhlYjBKUzViOWlIdTc5U0QrUGVrL3dWWk9IdVRhbmNFb3A5Vk40aHdyVUgxbE5DbXR1UC9PSTFrRTJlc2JlVDFkZmdlcXpBRnoxdVU4TG9TeXlHY3NZTVFCelVHZCsyTkx1OXdKUDF5bGN3NHFsM2RNb3NwMWQvbVRPN3ZLaWFIcGQ4L1VaRHoyQkJjZ01INE1TaExiNW92K1A3aWdCZFFnNDMiLCJtYWMiOiI1NTA5ZGViNDc1OWQ1YzU0MGI4YTUyYmY0MjgwYzZkM2ViNjYwYmRiZmI0MzVmOWE2OTRhMjk5MTc3YmY3NDhhIiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 18:27:48 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2019360741\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1328693853 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PKqZteC3IabV2rdCxsXBMLnTb72fzH4qdR2KdQSn</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>17</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1328693853\", {\"maxDepth\":0})</script>\n"}}