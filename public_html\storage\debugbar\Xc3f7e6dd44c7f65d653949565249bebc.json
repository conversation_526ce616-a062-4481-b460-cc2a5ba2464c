{"__meta": {"id": "Xc3f7e6dd44c7f65d653949565249bebc", "datetime": "2025-06-26 16:00:32", "utime": **********.283338, "method": "GET", "uri": "/users/17/edit", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750953631.644329, "end": **********.283351, "duration": 0.6390218734741211, "duration_str": "639ms", "measures": [{"label": "Booting", "start": 1750953631.644329, "relative_start": 0, "end": 1750953631.972365, "relative_end": 1750953631.972365, "duration": 0.328035831451416, "duration_str": "328ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1750953631.972373, "relative_start": 0.32804393768310547, "end": **********.283353, "relative_end": 2.1457672119140625e-06, "duration": 0.31098008155822754, "duration_str": "311ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 52018416, "peak_usage_str": "50MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 4, "templates": [{"name": "1x user.edit", "param_count": null, "params": [], "start": **********.073733, "type": "blade", "hash": "bladeC:\\laragon\\www\\ERPcopy\\public_html\\resources\\views/user/edit.blade.phpuser.edit", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fresources%2Fviews%2Fuser%2Fedit.blade.php&line=1", "ajax": false, "filename": "edit.blade.php", "line": "?"}, "render_count": 1, "name_original": "user.edit"}, {"name": "3x components.required", "param_count": null, "params": [], "start": **********.278148, "type": "blade", "hash": "bladeC:\\laragon\\www\\ERPcopy\\public_html\\resources\\views/components/required.blade.phpcomponents.required", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fresources%2Fviews%2Fcomponents%2Frequired.blade.php&line=1", "ajax": false, "filename": "required.blade.php", "line": "?"}, "render_count": 3, "name_original": "components.required"}]}, "route": {"uri": "GET users/{user}/edit", "middleware": "web, verified, auth, XSS, revalidate", "as": "users.edit", "controller": "App\\Http\\Controllers\\UserController@edit", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FHttp%2FControllers%2FUserController.php&line=235\" onclick=\"\">app/Http/Controllers/UserController.php:235-250</a>"}, "queries": {"nb_statements": 10, "nb_failed_statements": 0, "accumulated_duration": 0.02335, "accumulated_duration_str": "23.35ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.000112, "duration": 0.01927, "duration_str": "19.27ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 82.527}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.0275161, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 82.527, "width_percent": 1.456}, {"sql": "select * from `roles` where `created_by` = 15 and `name` != 'client'", "type": "query", "params": [], "bindings": ["15", "client"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Controllers\\UserController.php", "line": 238}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.030607, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "UserController.php:238", "source": "app/Http/Controllers/UserController.php:238", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FHttp%2FControllers%2FUserController.php&line=238", "ajax": false, "filename": "UserController.php", "line": "238"}, "connection": "kdmkjkqknb", "start_percent": 83.983, "width_percent": 1.799}, {"sql": "select * from `warehouses` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Controllers\\UserController.php", "line": 239}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.043568, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "UserController.php:239", "source": "app/Http/Controllers/UserController.php:239", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FHttp%2FControllers%2FUserController.php&line=239", "ajax": false, "filename": "UserController.php", "line": "239"}, "connection": "kdmkjkqknb", "start_percent": 85.782, "width_percent": 2.484}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 15 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["15", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.056467, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 88.266, "width_percent": 2.912}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.0583248, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 91.178, "width_percent": 1.799}, {"sql": "select * from `users` where `users`.`id` = '17' limit 1", "type": "query", "params": [], "bindings": ["17"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Controllers\\UserController.php", "line": 241}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.062053, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "UserController.php:241", "source": "app/Http/Controllers/UserController.php:241", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FHttp%2FControllers%2FUserController.php&line=241", "ajax": false, "filename": "UserController.php", "line": "241"}, "connection": "kdmkjkqknb", "start_percent": 92.976, "width_percent": 1.328}, {"sql": "select `custom_field_values`.`value`, `custom_fields`.`id` from `custom_field_values` inner join `custom_fields` on `custom_field_values`.`field_id` = `custom_fields`.`id` where `custom_fields`.`module` = 'user' and `record_id` = 17", "type": "query", "params": [], "bindings": ["user", "17"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/CustomField.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\CustomField.php", "line": 63}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Controllers\\UserController.php", "line": 242}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.063607, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "CustomField.php:63", "source": "app/Models/CustomField.php:63", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FCustomField.php&line=63", "ajax": false, "filename": "CustomField.php", "line": "63"}, "connection": "kdmkjkqknb", "start_percent": 94.304, "width_percent": 2.527}, {"sql": "select * from `custom_fields` where `created_by` = 15 and `module` = 'user'", "type": "query", "params": [], "bindings": ["15", "user"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Controllers\\UserController.php", "line": 243}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.066008, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "UserController.php:243", "source": "app/Http/Controllers/UserController.php:243", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FHttp%2FControllers%2FUserController.php&line=243", "ajax": false, "filename": "UserController.php", "line": "243"}, "connection": "kdmkjkqknb", "start_percent": 96.831, "width_percent": 1.242}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` = 17 and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["17", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "user.edit", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\resources\\views/user/edit.blade.php", "line": 100}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.2796192, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "user.edit:100", "source": "view::user.edit:100", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fresources%2Fviews%2Fuser%2Fedit.blade.php&line=100", "ajax": false, "filename": "edit.blade.php", "line": "100"}, "connection": "kdmkjkqknb", "start_percent": 98.073, "width_percent": 1.927}]}, "models": {"data": {"Spatie\\Permission\\Models\\Role": {"value": 10, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\warehouse": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2Fwarehouse.php&line=1", "ajax": false, "filename": "warehouse.php", "line": "?"}}, "App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 16, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => edit user, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-948569427 data-indent-pad=\"  \"><span class=sf-dump-note>edit user</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">edit user</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-948569427\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.0616, "xdebug_link": null}]}, "session": {"_token": "G4oUC4dvL91DvSdXtTSUa87JqfM4sfNNzjOiK6ns", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/users/17/edit", "status_code": "<pre class=sf-dump id=sf-dump-949500874 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-949500874\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1919923093 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1919923093\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1975732393 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1975732393\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1394923576 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1ap6d1q%7C2%7Cfx3%7C0%7C1998; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clsk=p74v85%7C1750953557602%7C82%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik9GaW02NzdYNFQ2NGxpbGJxT1JMOHc9PSIsInZhbHVlIjoiekxRVFZxckZvcVZmd1VHYUhyTkdrV0daeGxMVnNnbkNWL3o5blcwM2RMbHByc2JyczBwdjBqT08welJ1cEJiWVR1WXl3d2VEek1GaXRiMUpWdUNnbDhoaVZGWU1KNEg3NkdETmk1WjRYUmxPRFNWYmRPVWUxbGkzbnljR2FhaWg4U1JYVjRqL3RkRGVvN1J4MERtdHlzandHd2VlM1VwV1hsbjMrbi9FMkU0NzcwOXVVL3NCeDQ0U01KTHpiL2tBWjZEL0UzQVhtRGhpMjdPSnFHbE1KNVVYbk1XSG1aQUJEb1JWcGtnOUEvcGREWFplaVBoc0htM0NEMkdGcGRQcGZ3QlpRLzJHR2pndkFQelRqdm41M3R1clpWa0hlSFJsZHpCSjdoV08yYUpCK1hadDNQOFdab1ZXR3p0T3J5Y3Z5NXEvc3B3ejZ1Wi9QajNXQUhadHJRc3RqbGJCNG1SSDBGMXpzb0I4U0Q5aFpMMFRTSTlTM1BhZ2pRaTh2OFdwWGNBcVArQXV5QTQwYjJpcFFKOEIyeVJ1elNnRWVzTnBSSUJoOWYzVTI3c2hUU2hqbVhRaWlRUStqZHgvbkdNeGY4QWJocmFjdy9wd1hIbS9ycExLVHBrR2x4SFZVeVc5SWxSdStLRXVhKzJwdjBiWDd0aU1rNy8yc0FjNUJsK20iLCJtYWMiOiI3YTgwZGNkOGQ1N2ZjMjc2NjdlNDQ4OTA4NzAwYzE5ZjhkNjFjZGZjODFhNWNjZDQ0MjI5NzlmYmU1OWQ2NDlhIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImdCYnEveTZwblc0akY4RDNVQXhuZ1E9PSIsInZhbHVlIjoiVGMzTTk3UUpwRng5czRtZmlMVm5rTVhWbVZMZWx6UFN0N2xXVTd3MFNXUExXZ2dlNDhLWWdLT1cxTVhZQkR3bWlRcjRaZEx2bEpLZFQxTzZCbkp2M0R5ZkUrZTh5Uks3am01bWRhdlBkQVZLOWtoWTlCZkRYYldTbXJVZjZsOXpHQVpBY0JQMWVOZXJ0dDJVQVFLQ3o4NlF4WjZlNEZJUDY5cVBrQXN0U0FWOC9ncEFYeFNRK3Erd2xqMHZraTA5dzFqbGE5QzRuZXlQMVRYb2ZoWkZpeWRCQnZhQnBQMHFXOVZOZHJWZ3RuWmFSRnBDclowSER3S1A2dmJDZkdQVGErY2R2WlJYZHJUc1gzTy9iWnlCVG81SzlSTkJxZjd1R3VPN0g4bXRBNTc4dlRXZk1YQTgvVG1NYjVpRldMUGhrY25UK0tyOEllRFFBQkZGRG9wNDMvbjlyR0VqZHQxSkNpWlBGempBeEJpUjdIRDMwYVgxckUzQW9DckhKenJTcElMcWVLZWlabmVxc2FqcnpMc3N3QTc1ajlWS2xINDM5TnRWOUFrTnJJbXQ2Zm4yTVR3bEsyd3oydjZ6WVZKMlJuNzFqRGNXUGpoN1dKV1RqVWlhRzFEWDU5dEtCSEhrZXBoMmxRMXB4UEpVaFJZU2xKaXpiNk90YndsR1cyYloiLCJtYWMiOiJlOGRkZGZkYzJkM2YxYmZmYjNjNjQzMWRjOTFkMDRlMGVlZjViYTIwYmUwYzVjYjZkZWI1ZTQ4YzgyMzgwZjZkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1394923576\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1167774873 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G4oUC4dvL91DvSdXtTSUa87JqfM4sfNNzjOiK6ns</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">16z2m35FIhU8zNuOqkDvqLO3UyIjTT7e4c1Y0Zqf</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1167774873\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2146852762 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 16:00:32 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InZ1ZFJTWFphV0N1cmJOaTBySldVQ0E9PSIsInZhbHVlIjoiMVoxU08wTEYrT3B6WmhUN2hjVjI4SlVDOUJVc0NQN1o3QjBNWWQrbncxdFlaakpyR2FDVDVaWWhQaWRyL0RSa1RWUi95SG16VjMwMzFLUHZmL3d4SmRVVUllVHRJQmo1dDRFR2ZCeThKNVJTSWMzWUN3VTlWdjZDNXpMdFpoMkNraVVyMkRwVitQK3EwMW8wZkZoS0dRVndBN0hmMnRyeGU3K0JLVW1iMy9ybW9pMjRQcW5rNHFlVU9nMUhSQmhId0tJbGVES2RSU2VhaGw4aGo3MUxFZUhPVnhqTXNINzY3bXVDbXFON0pHQmRZcENTd2ZNUFNxQW1wRHZ4RDJTVGREcUJZYXV0eHVaZStsUkVOT3lKOXhnNUxZZE9KL2VUamR1d01WaG5DK3grc2kxZUpMdFRyMHRFKzJENDB3dDZEaHFMUlBhUUNhK254eCtobnFTR1o2NXdwenFBR200UzlLazh0dWdpcEFxai9ENXNxNEFhR3l0M1FlbTgyY2JHL0RGSGlFMjMveTh2N1AxV01QVjNnRGwxcTlkcUtSUWVkSHhOaVBKNzNkS2svZnltMktqTGh5QjNRUlhMRUsrTXRrY250WFdpaTJhMVVIUEpZQ0hIT0VoaHloaWM2LzdnM01VNWFIVEx6VUJTd2Z3UEEzUDRWUzZYOUFIWnJabFkiLCJtYWMiOiJlYzI0OTJlN2M2OGJjNjA4NjY3YWNhNWFjNTg3ZTVkODE2NjBlNDg0ZjAyNTYzZjE2NTc3MDkzNDQ3MDAyNTVmIiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 18:00:32 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ikp1STR5S1U5SzhxMXlBdUtZYnRlaHc9PSIsInZhbHVlIjoiR1dlYjRJSjVVT1o5RzlCTDd2TW5TRGRWWkpvK1ZiVVBDOTVpTU9qL0JnZnVyWGhGTXlFQlF2d1lRVDY5Z2haRkc0ZWUyRFV0SC81RTIxTzZwc0RrcllGTW5rQU9kSTZ2dW10cXdkK3FteC9YTXNvZWxwa2wxL1BTZStOdEdlN2RwN2tLNVJCWDU0NXJWNDVNODRwZUlCVHpTbE1pWEwxdkxuYTZpSDJlakVRcG5neXZDTG1CdGxnL0JVbTUxMEVETHd4M2hvcW8zU2VjNUFoVjUreFFYRDdON1pRdkZ0WjRnM3g1K2VzTitSSjFHOUlJMHJ1WjVRNFRRaXJPb0JLR2ZZc1RiS0ZxL29QN1diNmpVblpWK0gxTi91ajZJcVhhS3YwN0ZKUnF0cWlCS3FhU0hQa01zdTlXckpXdlZFUHNxdFJPaWJpd2RmL0U2OEx2NnJtVmx1RU5URU8yZm02WTVBWnFsVGRySWdlS1VJWlFtZ0tDUWpScllWV0dvMHhadEZwQkJscmNCbzlpemtuWklXTjBHK0hCZlVFVnBrWXNGVy9jRExlRzlKbmhlMFB3REs3TjBrUnM0V2taUFhLRFMyam1DUW1KOXVqWVZxYzhBRlJUbUdvNi9QbnBCYWtvZm85Z0crSHNlNHNxdW9kUFFzdnFTUDdLVWk1T3pXS0wiLCJtYWMiOiJmNjYxNzU3ZDE5M2VkNGRhNTUxZDFlNmJhNjhlYWU5MjE2ZGQwZWIwODY1YTk4MzlhMTg3OTJkOGM2MzYxNWQ4IiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 18:00:32 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InZ1ZFJTWFphV0N1cmJOaTBySldVQ0E9PSIsInZhbHVlIjoiMVoxU08wTEYrT3B6WmhUN2hjVjI4SlVDOUJVc0NQN1o3QjBNWWQrbncxdFlaakpyR2FDVDVaWWhQaWRyL0RSa1RWUi95SG16VjMwMzFLUHZmL3d4SmRVVUllVHRJQmo1dDRFR2ZCeThKNVJTSWMzWUN3VTlWdjZDNXpMdFpoMkNraVVyMkRwVitQK3EwMW8wZkZoS0dRVndBN0hmMnRyeGU3K0JLVW1iMy9ybW9pMjRQcW5rNHFlVU9nMUhSQmhId0tJbGVES2RSU2VhaGw4aGo3MUxFZUhPVnhqTXNINzY3bXVDbXFON0pHQmRZcENTd2ZNUFNxQW1wRHZ4RDJTVGREcUJZYXV0eHVaZStsUkVOT3lKOXhnNUxZZE9KL2VUamR1d01WaG5DK3grc2kxZUpMdFRyMHRFKzJENDB3dDZEaHFMUlBhUUNhK254eCtobnFTR1o2NXdwenFBR200UzlLazh0dWdpcEFxai9ENXNxNEFhR3l0M1FlbTgyY2JHL0RGSGlFMjMveTh2N1AxV01QVjNnRGwxcTlkcUtSUWVkSHhOaVBKNzNkS2svZnltMktqTGh5QjNRUlhMRUsrTXRrY250WFdpaTJhMVVIUEpZQ0hIT0VoaHloaWM2LzdnM01VNWFIVEx6VUJTd2Z3UEEzUDRWUzZYOUFIWnJabFkiLCJtYWMiOiJlYzI0OTJlN2M2OGJjNjA4NjY3YWNhNWFjNTg3ZTVkODE2NjBlNDg0ZjAyNTYzZjE2NTc3MDkzNDQ3MDAyNTVmIiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 18:00:32 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ikp1STR5S1U5SzhxMXlBdUtZYnRlaHc9PSIsInZhbHVlIjoiR1dlYjRJSjVVT1o5RzlCTDd2TW5TRGRWWkpvK1ZiVVBDOTVpTU9qL0JnZnVyWGhGTXlFQlF2d1lRVDY5Z2haRkc0ZWUyRFV0SC81RTIxTzZwc0RrcllGTW5rQU9kSTZ2dW10cXdkK3FteC9YTXNvZWxwa2wxL1BTZStOdEdlN2RwN2tLNVJCWDU0NXJWNDVNODRwZUlCVHpTbE1pWEwxdkxuYTZpSDJlakVRcG5neXZDTG1CdGxnL0JVbTUxMEVETHd4M2hvcW8zU2VjNUFoVjUreFFYRDdON1pRdkZ0WjRnM3g1K2VzTitSSjFHOUlJMHJ1WjVRNFRRaXJPb0JLR2ZZc1RiS0ZxL29QN1diNmpVblpWK0gxTi91ajZJcVhhS3YwN0ZKUnF0cWlCS3FhU0hQa01zdTlXckpXdlZFUHNxdFJPaWJpd2RmL0U2OEx2NnJtVmx1RU5URU8yZm02WTVBWnFsVGRySWdlS1VJWlFtZ0tDUWpScllWV0dvMHhadEZwQkJscmNCbzlpemtuWklXTjBHK0hCZlVFVnBrWXNGVy9jRExlRzlKbmhlMFB3REs3TjBrUnM0V2taUFhLRFMyam1DUW1KOXVqWVZxYzhBRlJUbUdvNi9QbnBCYWtvZm85Z0crSHNlNHNxdW9kUFFzdnFTUDdLVWk1T3pXS0wiLCJtYWMiOiJmNjYxNzU3ZDE5M2VkNGRhNTUxZDFlNmJhNjhlYWU5MjE2ZGQwZWIwODY1YTk4MzlhMTg3OTJkOGM2MzYxNWQ4IiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 18:00:32 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2146852762\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1025820691 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G4oUC4dvL91DvSdXtTSUa87JqfM4sfNNzjOiK6ns</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1025820691\", {\"maxDepth\":0})</script>\n"}}