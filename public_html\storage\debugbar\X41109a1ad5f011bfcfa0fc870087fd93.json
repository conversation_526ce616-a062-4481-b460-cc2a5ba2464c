{"__meta": {"id": "X41109a1ad5f011bfcfa0fc870087fd93", "datetime": "2025-06-26 15:58:29", "utime": **********.825149, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.421743, "end": **********.825165, "duration": 0.40342211723327637, "duration_str": "403ms", "measures": [{"label": "Booting", "start": **********.421743, "relative_start": 0, "end": **********.772141, "relative_end": **********.772141, "duration": 0.35039806365966797, "duration_str": "350ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.772153, "relative_start": 0.35040998458862305, "end": **********.825166, "relative_end": 9.5367431640625e-07, "duration": 0.05301308631896973, "duration_str": "53.01ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45661944, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0025700000000000002, "accumulated_duration_str": "2.57ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.800892, "duration": 0.00173, "duration_str": "1.73ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 67.315}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.8116221, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 67.315, "width_percent": 17.899}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.817179, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 85.214, "width_percent": 14.786}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "G4oUC4dvL91DvSdXtTSUa87JqfM4sfNNzjOiK6ns", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/bill/eyJpdiI6IlhwTTVrTG03L25NelZRV0dEbnFMV2c9PSIsInZhbHVlIjoiM202QWM5RGRFWllQbWE5NU0zZGUxUT09IiwibWFjIjoiMzBlMTkzNTRiODk3ZWZiM2UxYzJlOGI2MTE2YTJiOWI2YjkyYzQ5ZTQ4ZmYxYTcyNzE5YTk3MzliOGFlNzRjZCIsInRhZyI6IiJ9\"\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1107486282 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1107486282\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1391375332 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1391375332\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1321278154 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G4oUC4dvL91DvSdXtTSUa87JqfM4sfNNzjOiK6ns</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1321278154\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-542061116 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"222 characters\">http://localhost/bill/eyJpdiI6IlhwTTVrTG03L25NelZRV0dEbnFMV2c9PSIsInZhbHVlIjoiM202QWM5RGRFWllQbWE5NU0zZGUxUT09IiwibWFjIjoiMzBlMTkzNTRiODk3ZWZiM2UxYzJlOGI2MTE2YTJiOWI2YjkyYzQ5ZTQ4ZmYxYTcyNzE5YTk3MzliOGFlNzRjZCIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1ap6d1q%7C2%7Cfx3%7C0%7C1998; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clsk=p74v85%7C1750953505703%7C80%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Iko5K25BdmVPdW9JY0tFU0lwNCtFcUE9PSIsInZhbHVlIjoiK1pFT0RCa0lqZ1hybHV4NHFxbU93cGxCakx0VytaVGxjeGdvbnRxUW1ucEhHS05PbEdiR08xUWlMbGR4cHRqazJ0Z2t5cEc2NklhZkQ3eXNzVTNFZFFWbXROdUx0bDFHQkwvd0pwaHlBemUwSjhwNHhKUWJWM3Q2TTduamxVQ3UxeTJqSUZjckdGbmdoTTB4eUVrT1VOTDdCaDJIR1ZUMisrRGl6WFI4ZVBoS1NLVTViSHgxcnBkUTgwY29TTUh5SnR0L3VaM3FHaXZGK3k5THJWbXhBUkdFOE44c0c0Y1RTSk4xSElRMlFHeUlYRUd4NXFEOFNhR3JaV29qSUlDdy9mRFYxWWFDU0Q5ZFdna0h1R2t3elUwbHo1UXRDakRZR1dhSE00WDNLcHdKcERFQnlpWkt6NVNuQUpFQ2VxZlp2bUdjeVhPZjcyREwvcWNraXJHWmZjdUg4TC9QT2cvalI2OWNUUjZGeW4xODFXanQrMFhtVnl5NjBsdkd0V2t2ZTMxcUNBMGtaTitxMG4vbUFJWUE5RC9QRzhheWYzS2V6eUsyRUtFOTkzSmtOVmFVYlhSZzFFa3ZFU0hzbHFPMUJxRmh1am5KQXFlMllSVHprV3dyMkdZeXYzR09VMEwvSjRFdG11SGUzaVcveGlDeUZTYWFMUWRWZU95WE1QWU0iLCJtYWMiOiIzZTM4YWI4NmFhODZkYjdmZjUxZDUzZGU5Y2Q0MDRjNzJhNDIyODBhMTBlNDE1MTQwOTc2MGVmZjhhMGQ1MzgzIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjlMcTY0eFlZaHBuYXYyQ2NISnVXSVE9PSIsInZhbHVlIjoiblpYVDRIZlRHWVgyMjBBYVJNbm14RUt4MlFPU3MzR2EwaFNLWmk2dllTUjNEaUpEWXJudFhnT09lanJ6bktpZEs1RG9qYlVnTVArdTZ1RWp6djhMdTlKWE1QOGtsL1ZGWkh3eEUwOCswME5ERFY0NGZzM3grSFcwalBKL0hFTVJ6bGhhQzd4VFpzS3lhT0g1cHp5SHRTRW1GV0d5dTh4M242Z1dFZjNyYXUrVUN0eDFwNUFOZDJ6a0pYZGtIUnBnTWpGVklhTFFhUzd0SGRiSnBsVkdrNEpJNk04Mk5NY1NEYUZmb0Rxd28yV2h3MjdUNHhqYzlRUnk2KzN6dmVuMnlyTSt6SHBmeE8zRGJRY3BCczVpOTFZMmphVGFSQTVPckplNDNzOURpRVFUak0veTlSSHEwMjEvZkRrOEJDRnFoMEpEVjZtM2k4THVTM0FoUGdjTWxseHc3RVZab0ZMc0NMdGJ3OWZmUURlVmxWdFVpZFo0VzcvSFdnbktEZ0RuVWlaNzNMU3RQUVFYbkxldlZQWW1aOWZLVGovQjdNcmg3K3ZSNFZ1dnkwM2xxZzY1aEVjY2Y0SGlJQ1FoaFhqSEUyWjFsRWhMWjJLS3NEeTArT21FWGhxbDlxV0pCQWJPSXRCRkUxbmhTaTRNOE42cStxMkhGRWxqZjJldk9lQ0QiLCJtYWMiOiJmNDNiYjY4N2RiOTgzNWU3ZjczZjIwYjI3ZGUwYWY5M2NmMDI2MWI2MzhkZjMxYjViYTM2NjcyMjlhZGIzZTU3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-542061116\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-402180116 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G4oUC4dvL91DvSdXtTSUa87JqfM4sfNNzjOiK6ns</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">16z2m35FIhU8zNuOqkDvqLO3UyIjTT7e4c1Y0Zqf</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-402180116\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1492718879 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 15:58:29 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ii9qLzFaMkU5ZElrZFY4Sk1vUGhrWUE9PSIsInZhbHVlIjoiWXV4aHBOOHdBUUJvY1M3R0tMSDF2UHRDa1ZhbnFjOGtULzMvR0l3dm9IaE0yeGkzQnBEaHNZcmlLTkgvb05wWi80RGZGM1pYNmpPbVhCZzU3RUhKVnova3hkZmM2REJEZ1hYNCtuMnY0WlpvL3ZUSTJRNmFORUZURnF1SmVtYTFadHg0ZkRWTlZneXZPUHdxbVhZK1gyYnY4RFZvTWxVU3FYN29BQ1ZMT21tTVZtVGt5MTgvaW9jOCtGU2dNbEllVWtneXFGSDdSY080RXhjUmNnMk1DNjBwcDZqM0IvbHNRWmxHWlArZEFrdHYrS0xMY3poWEsyTWJnZ0J1UVRESm1mTzJ2Z3YxZUphdGh3cW82VWk4NUEwLy9nS1JoYTVMZWhVZ1FlZ3FvNVNIUkxSeUg0SmNPR0k3Ui9XZ1ZXYnN5RVlLcXRDd2Z3SzZtZ3dYeVYyVDB0enZPMnBCb3F3SGk3bEJTeG5EME5ZMjI3bnQ0MmltNVRTYUx0eTl1bUtMKzArWDZoamt3dnl1TXFJNzJnRitpOXZzWllPWmRDeVhwNisvN1BUdnZFTXlLMldNM3doWlVEWU1pemNHSHNZcnVudUQrVElxcUtycktrM1Z2OFAxaU1vUjVGYk5rUlBkejVTU0xBY1dEcEkvRkc1WjUwTGRIR3B5OXY2T0FVMVQiLCJtYWMiOiI0ZmU2Yjc3MTIzN2MwMzM5M2Y0MTJmYzY0M2Y4NjE2NTc4Yzg2NTk2YjhkNmNhOWM0NDc1ZWY1NDIwM2QxMWIwIiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 17:58:29 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImNyMHp4cE1MSmp6S1o2em1NdVNTamc9PSIsInZhbHVlIjoibnZBdllOcEJxNWJyZUFTUmZvWG1hcU1zVVN3MmxMMG1LazVUTjVzRytjN1U3YXlNRU5uYjBGRFdwTkVlZFJ3aDhsOUZnUzdOZHZtakRreWxGK01JQlZ5Vk9xcE5vbXV6V05vNjYvenJlVzBZc3JDMS9nV1pXSG1zR09oUTFGMXBGWlJBaXBEa0JmQ0FlTU9VdXBZYTMzVzZIK1JHUXRoZ3BUM2VTVFB4TGs5L3QzbCtna21UaE5JeGNYSjh5bEhmSmRySE5WWnp2Y0tvanJ5YXJteDZBbVhEUVhlcWFwblhucStXSFpURERuQkExUmU2ejF0eTM0UzF3T2wxTW5ZQVBEMzE5Y3orcEU2K3RJeFUzbmlFbXRPLytQU2VWUkFVdFNqRWJ0RVF2SEpsVkNZcjEvZ3NuY2JTRmZ5TjYzSDlzRUo2NVNjUy9ZbVlnQUhCNUxBM0ViMVFYQ1JiOG1FdEJyYytpejZ5OHNHVG9jUnJaVlJ0MDV0YndQeHJVdHRIbzMxSkpwdHVhd3RNYlM1cXgzKzlMbUp0QmNqYVlkNjhjM0dtU0pkMUFQRGc3R2YvM2p5YWQ4ZGIwVnNjVzgrdm1xZGdSLy92MHRoNktodFI2eTMzN1RqeGROcFdYaktJUEVvdTV6NkVWU29pdFJZQWpXYzdZMjAzYzNTcGlJTXoiLCJtYWMiOiJlNjIzYWEyZDVhZmFhN2ZlNjM1ZTQ5OGUxNTA3ZWVlYThkNTMwNWVmNWQyM2JmMzY2NWEyMGIzZDM2NDcwNDVhIiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 17:58:29 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ii9qLzFaMkU5ZElrZFY4Sk1vUGhrWUE9PSIsInZhbHVlIjoiWXV4aHBOOHdBUUJvY1M3R0tMSDF2UHRDa1ZhbnFjOGtULzMvR0l3dm9IaE0yeGkzQnBEaHNZcmlLTkgvb05wWi80RGZGM1pYNmpPbVhCZzU3RUhKVnova3hkZmM2REJEZ1hYNCtuMnY0WlpvL3ZUSTJRNmFORUZURnF1SmVtYTFadHg0ZkRWTlZneXZPUHdxbVhZK1gyYnY4RFZvTWxVU3FYN29BQ1ZMT21tTVZtVGt5MTgvaW9jOCtGU2dNbEllVWtneXFGSDdSY080RXhjUmNnMk1DNjBwcDZqM0IvbHNRWmxHWlArZEFrdHYrS0xMY3poWEsyTWJnZ0J1UVRESm1mTzJ2Z3YxZUphdGh3cW82VWk4NUEwLy9nS1JoYTVMZWhVZ1FlZ3FvNVNIUkxSeUg0SmNPR0k3Ui9XZ1ZXYnN5RVlLcXRDd2Z3SzZtZ3dYeVYyVDB0enZPMnBCb3F3SGk3bEJTeG5EME5ZMjI3bnQ0MmltNVRTYUx0eTl1bUtMKzArWDZoamt3dnl1TXFJNzJnRitpOXZzWllPWmRDeVhwNisvN1BUdnZFTXlLMldNM3doWlVEWU1pemNHSHNZcnVudUQrVElxcUtycktrM1Z2OFAxaU1vUjVGYk5rUlBkejVTU0xBY1dEcEkvRkc1WjUwTGRIR3B5OXY2T0FVMVQiLCJtYWMiOiI0ZmU2Yjc3MTIzN2MwMzM5M2Y0MTJmYzY0M2Y4NjE2NTc4Yzg2NTk2YjhkNmNhOWM0NDc1ZWY1NDIwM2QxMWIwIiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 17:58:29 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImNyMHp4cE1MSmp6S1o2em1NdVNTamc9PSIsInZhbHVlIjoibnZBdllOcEJxNWJyZUFTUmZvWG1hcU1zVVN3MmxMMG1LazVUTjVzRytjN1U3YXlNRU5uYjBGRFdwTkVlZFJ3aDhsOUZnUzdOZHZtakRreWxGK01JQlZ5Vk9xcE5vbXV6V05vNjYvenJlVzBZc3JDMS9nV1pXSG1zR09oUTFGMXBGWlJBaXBEa0JmQ0FlTU9VdXBZYTMzVzZIK1JHUXRoZ3BUM2VTVFB4TGs5L3QzbCtna21UaE5JeGNYSjh5bEhmSmRySE5WWnp2Y0tvanJ5YXJteDZBbVhEUVhlcWFwblhucStXSFpURERuQkExUmU2ejF0eTM0UzF3T2wxTW5ZQVBEMzE5Y3orcEU2K3RJeFUzbmlFbXRPLytQU2VWUkFVdFNqRWJ0RVF2SEpsVkNZcjEvZ3NuY2JTRmZ5TjYzSDlzRUo2NVNjUy9ZbVlnQUhCNUxBM0ViMVFYQ1JiOG1FdEJyYytpejZ5OHNHVG9jUnJaVlJ0MDV0YndQeHJVdHRIbzMxSkpwdHVhd3RNYlM1cXgzKzlMbUp0QmNqYVlkNjhjM0dtU0pkMUFQRGc3R2YvM2p5YWQ4ZGIwVnNjVzgrdm1xZGdSLy92MHRoNktodFI2eTMzN1RqeGROcFdYaktJUEVvdTV6NkVWU29pdFJZQWpXYzdZMjAzYzNTcGlJTXoiLCJtYWMiOiJlNjIzYWEyZDVhZmFhN2ZlNjM1ZTQ5OGUxNTA3ZWVlYThkNTMwNWVmNWQyM2JmMzY2NWEyMGIzZDM2NDcwNDVhIiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 17:58:29 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1492718879\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-219235867 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G4oUC4dvL91DvSdXtTSUa87JqfM4sfNNzjOiK6ns</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"222 characters\">http://localhost/bill/eyJpdiI6IlhwTTVrTG03L25NelZRV0dEbnFMV2c9PSIsInZhbHVlIjoiM202QWM5RGRFWllQbWE5NU0zZGUxUT09IiwibWFjIjoiMzBlMTkzNTRiODk3ZWZiM2UxYzJlOGI2MTE2YTJiOWI2YjkyYzQ5ZTQ4ZmYxYTcyNzE5YTk3MzliOGFlNzRjZCIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-219235867\", {\"maxDepth\":0})</script>\n"}}