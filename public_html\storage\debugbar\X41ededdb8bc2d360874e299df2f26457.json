{"__meta": {"id": "X41ededdb8bc2d360874e299df2f26457", "datetime": "2025-06-26 16:01:41", "utime": **********.769163, "method": "GET", "uri": "/user-reset-password/eyJpdiI6Ill2QjBCdnFoaVM5RGFGR29KRHBtTlE9PSIsInZhbHVlIjoicVNzSm5IWndFYkhTQm5tTTZIN0hUUT09IiwibWFjIjoiMzM3NDdkNGMwMjI0MDI3OTg5MjljZGRiYjVkMmY1OGMyZjBlOTVlYjZiODEyNjAyMWZmZWY4ZGM3NjU0NjcwOCIsInRhZyI6IiJ9", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.245631, "end": **********.769178, "duration": 0.5235469341278076, "duration_str": "524ms", "measures": [{"label": "Booting", "start": **********.245631, "relative_start": 0, "end": **********.575642, "relative_end": **********.575642, "duration": 0.33001112937927246, "duration_str": "330ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.575652, "relative_start": 0.3300209045410156, "end": **********.76918, "relative_end": 2.1457672119140625e-06, "duration": 0.1935281753540039, "duration_str": "194ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45643872, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x user.reset", "param_count": null, "params": [], "start": **********.61525, "type": "blade", "hash": "bladeC:\\laragon\\www\\ERPcopy\\public_html\\resources\\views/user/reset.blade.phpuser.reset", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fresources%2Fviews%2Fuser%2Freset.blade.php&line=1", "ajax": false, "filename": "reset.blade.php", "line": "?"}, "render_count": 1, "name_original": "user.reset"}]}, "route": {"uri": "GET user-reset-password/{id}", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\UserController@userPassword", "namespace": null, "prefix": "", "where": [], "as": "users.reset", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FHttp%2FControllers%2FUserController.php&line=575\" onclick=\"\">app/Http/Controllers/UserController.php:575-582</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00179, "accumulated_duration_str": "1.79ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.603561, "duration": 0.00157, "duration_str": "1.57ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 87.709}, {"sql": "select * from `users` where `users`.`id` = 17 limit 1", "type": "query", "params": [], "bindings": ["17"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Controllers\\UserController.php", "line": 578}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.607478, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "UserController.php:578", "source": "app/Http/Controllers/UserController.php:578", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FHttp%2FControllers%2FUserController.php&line=578", "ajax": false, "filename": "UserController.php", "line": "578"}, "connection": "kdmkjkqknb", "start_percent": 87.709, "width_percent": 12.291}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "G4oUC4dvL91DvSdXtTSUa87JqfM4sfNNzjOiK6ns", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/user-reset-password/eyJpdiI6Ill2QjBCdnFoaVM5RGFGR29KRHBtTlE9PSIsInZhbHVlIjoicVNzSm5IWndFYkhTQm5tTTZIN0hUUT09IiwibWFjIjoiMzM3NDdkNGMwMjI0MDI3OTg5MjljZGRiYjVkMmY1OGMyZjBlOTVlYjZiODEyNjAyMWZmZWY4ZGM3NjU0NjcwOCIsInRhZyI6IiJ9", "status_code": "<pre class=sf-dump id=sf-dump-181546448 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-181546448\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1991063079 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1991063079\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-960048366 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-960048366\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-291604765 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1ap6d1q%7C2%7Cfx3%7C0%7C1998; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clsk=p74v85%7C1750953557602%7C82%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InZ1ZFJTWFphV0N1cmJOaTBySldVQ0E9PSIsInZhbHVlIjoiMVoxU08wTEYrT3B6WmhUN2hjVjI4SlVDOUJVc0NQN1o3QjBNWWQrbncxdFlaakpyR2FDVDVaWWhQaWRyL0RSa1RWUi95SG16VjMwMzFLUHZmL3d4SmRVVUllVHRJQmo1dDRFR2ZCeThKNVJTSWMzWUN3VTlWdjZDNXpMdFpoMkNraVVyMkRwVitQK3EwMW8wZkZoS0dRVndBN0hmMnRyeGU3K0JLVW1iMy9ybW9pMjRQcW5rNHFlVU9nMUhSQmhId0tJbGVES2RSU2VhaGw4aGo3MUxFZUhPVnhqTXNINzY3bXVDbXFON0pHQmRZcENTd2ZNUFNxQW1wRHZ4RDJTVGREcUJZYXV0eHVaZStsUkVOT3lKOXhnNUxZZE9KL2VUamR1d01WaG5DK3grc2kxZUpMdFRyMHRFKzJENDB3dDZEaHFMUlBhUUNhK254eCtobnFTR1o2NXdwenFBR200UzlLazh0dWdpcEFxai9ENXNxNEFhR3l0M1FlbTgyY2JHL0RGSGlFMjMveTh2N1AxV01QVjNnRGwxcTlkcUtSUWVkSHhOaVBKNzNkS2svZnltMktqTGh5QjNRUlhMRUsrTXRrY250WFdpaTJhMVVIUEpZQ0hIT0VoaHloaWM2LzdnM01VNWFIVEx6VUJTd2Z3UEEzUDRWUzZYOUFIWnJabFkiLCJtYWMiOiJlYzI0OTJlN2M2OGJjNjA4NjY3YWNhNWFjNTg3ZTVkODE2NjBlNDg0ZjAyNTYzZjE2NTc3MDkzNDQ3MDAyNTVmIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ikp1STR5S1U5SzhxMXlBdUtZYnRlaHc9PSIsInZhbHVlIjoiR1dlYjRJSjVVT1o5RzlCTDd2TW5TRGRWWkpvK1ZiVVBDOTVpTU9qL0JnZnVyWGhGTXlFQlF2d1lRVDY5Z2haRkc0ZWUyRFV0SC81RTIxTzZwc0RrcllGTW5rQU9kSTZ2dW10cXdkK3FteC9YTXNvZWxwa2wxL1BTZStOdEdlN2RwN2tLNVJCWDU0NXJWNDVNODRwZUlCVHpTbE1pWEwxdkxuYTZpSDJlakVRcG5neXZDTG1CdGxnL0JVbTUxMEVETHd4M2hvcW8zU2VjNUFoVjUreFFYRDdON1pRdkZ0WjRnM3g1K2VzTitSSjFHOUlJMHJ1WjVRNFRRaXJPb0JLR2ZZc1RiS0ZxL29QN1diNmpVblpWK0gxTi91ajZJcVhhS3YwN0ZKUnF0cWlCS3FhU0hQa01zdTlXckpXdlZFUHNxdFJPaWJpd2RmL0U2OEx2NnJtVmx1RU5URU8yZm02WTVBWnFsVGRySWdlS1VJWlFtZ0tDUWpScllWV0dvMHhadEZwQkJscmNCbzlpemtuWklXTjBHK0hCZlVFVnBrWXNGVy9jRExlRzlKbmhlMFB3REs3TjBrUnM0V2taUFhLRFMyam1DUW1KOXVqWVZxYzhBRlJUbUdvNi9QbnBCYWtvZm85Z0crSHNlNHNxdW9kUFFzdnFTUDdLVWk1T3pXS0wiLCJtYWMiOiJmNjYxNzU3ZDE5M2VkNGRhNTUxZDFlNmJhNjhlYWU5MjE2ZGQwZWIwODY1YTk4MzlhMTg3OTJkOGM2MzYxNWQ4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-291604765\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1401311617 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G4oUC4dvL91DvSdXtTSUa87JqfM4sfNNzjOiK6ns</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">16z2m35FIhU8zNuOqkDvqLO3UyIjTT7e4c1Y0Zqf</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1401311617\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2028630864 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 16:01:41 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImRWdWZDaGlncE80L1BXWXE1ZUEwQlE9PSIsInZhbHVlIjoiMzlDTWNxZW5lUDdLZjMycms0aFFFbE1pQXRIeXh2R0VuZzNFeU5HRVRUdG1WbzdpemlUZkUvVGpvaVk0aC9qVmxXbE5PZTgxemt0RUkvc3A3dTZKeVpya1V1T1NiS3o2SEtyTVIvc1o4YjE3eUp0cU5TTGd6QUpZakUxUEFLK0toemM1T1kzblgxZ05LcDNPOW9PcFh2U3hjL3JpaVBaTGVFZ2t5blo4RlAxa1RpODJ1UzJnMjg0RXd4enBoVVB4amhaczY0cXhndTBDbVVWTkdoQjNwM0lnVXN0SjJPcmFKL0VTR0VlQ2hRSXh5N1dXOUplc2RmcEE2T1hHZ2xHWmNveXlsaHlnYi90ZVZEQ3dJSWVwL3hWQktjbXJYVDRJK2lRY1E3UzJ1OTFvVUM4ckFqY3Q4d0d3Tmg0OEFJT1hwZHNGNFdhYUxBcVdtOElwaDNIUzVveVZiTWVqSmlwY1dzWUduTUFkd1Rwb1JSd0cwWlIxaXpXTTY0T3ZCZ0drR3BYK0phblRWV3EyeTBTUGFReXlpNk5JYm9kdDdVQUxnZXpnYTBiMXdDVE56S05UNHkxcy9vazU5WEFkaTVGRWNVeGxJV0VJUVpWR21yZUdsM3g1THYyeGQyQ3drMlAyeEY2a2lSVkYwblU2bXU5cXE2QWdlOHI5c2VGYTBwM3kiLCJtYWMiOiI2M2VkMGRlYThlOTU0MmY1MTUyYzdmMTlkZGNlZjI3NzgyMDZkMDI3YzA2ZmZkOTg5YzQwYzQ5MjcyYTExMTY0IiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 18:01:41 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImhDT3oyaGZsZVdvVUVOQk9QbWVHM3c9PSIsInZhbHVlIjoiQVNIaWpER1lSS054OVFSNHdFODJjRTVTR3hQUlpXUHV1NUVSQkpPSXdsUWRQdTRad3dtczgva0RtbWJpOXBVTUVKQVJHMlZHL0dxRzJ6aENQMGFPU0wyNHZxNmZjSkE0YkZMNDFXcHVYSUliQzdRTUJSUisxTzk2Y3VXMkx6ZGd1UzRmYUIzTWNqMkdUNWxjMnN5UDBndkd0Y0RWSUtvblJqZHFIRDNRZVdZVFlIYllhNXoyRU44RzdxNlZUd1Z0MjBtaENLS29TS3NqWVpvTWpjb3NHTjIxREtUUWlJZHlqTW8xU082UHVkL2xsTWc5QmRsM3hBd1dXMWVhVWR2TUlDOE1xOWJtcWRpU0MxZmJ5dWttdGVMdXNCWnZ4cGY1ZzZMS3RrcEtyS0VkbmJiMVB4RkRqZWY2TjVBMzBuUmV6ZDEyNlhCMzMrRkd2OGZ4VjdScEZlTTdWNExzZDQrbEpVL0JKZTBOcFQ0NXlJQkxnTHlaOFFHalFXMkwxM3BZdFprdFQxYkg2eVlTb1hQWTRKQWRsSXk4YWt4eHc4bnUyQ04zaXI5UG9rZ2xId1RLMS9FYkpQZXJXckxiTFM4TTBXSjYwQlpqTzB1WThOZlAwMVlLaFIrWUU3NkN3dDB4SGRHdGt2U3NXd2tKOVhWOURna3VOU3dyL2p3ZlEramsiLCJtYWMiOiI4YjUwOTQ5YTE2NTRkZmMyZTM5YTg3ZDY2MGQyMWUyNzllMTQzZjBiOTk2OTFiMzFlYmRiZWIyZDIwYTE0ZDBkIiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 18:01:41 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImRWdWZDaGlncE80L1BXWXE1ZUEwQlE9PSIsInZhbHVlIjoiMzlDTWNxZW5lUDdLZjMycms0aFFFbE1pQXRIeXh2R0VuZzNFeU5HRVRUdG1WbzdpemlUZkUvVGpvaVk0aC9qVmxXbE5PZTgxemt0RUkvc3A3dTZKeVpya1V1T1NiS3o2SEtyTVIvc1o4YjE3eUp0cU5TTGd6QUpZakUxUEFLK0toemM1T1kzblgxZ05LcDNPOW9PcFh2U3hjL3JpaVBaTGVFZ2t5blo4RlAxa1RpODJ1UzJnMjg0RXd4enBoVVB4amhaczY0cXhndTBDbVVWTkdoQjNwM0lnVXN0SjJPcmFKL0VTR0VlQ2hRSXh5N1dXOUplc2RmcEE2T1hHZ2xHWmNveXlsaHlnYi90ZVZEQ3dJSWVwL3hWQktjbXJYVDRJK2lRY1E3UzJ1OTFvVUM4ckFqY3Q4d0d3Tmg0OEFJT1hwZHNGNFdhYUxBcVdtOElwaDNIUzVveVZiTWVqSmlwY1dzWUduTUFkd1Rwb1JSd0cwWlIxaXpXTTY0T3ZCZ0drR3BYK0phblRWV3EyeTBTUGFReXlpNk5JYm9kdDdVQUxnZXpnYTBiMXdDVE56S05UNHkxcy9vazU5WEFkaTVGRWNVeGxJV0VJUVpWR21yZUdsM3g1THYyeGQyQ3drMlAyeEY2a2lSVkYwblU2bXU5cXE2QWdlOHI5c2VGYTBwM3kiLCJtYWMiOiI2M2VkMGRlYThlOTU0MmY1MTUyYzdmMTlkZGNlZjI3NzgyMDZkMDI3YzA2ZmZkOTg5YzQwYzQ5MjcyYTExMTY0IiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 18:01:41 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImhDT3oyaGZsZVdvVUVOQk9QbWVHM3c9PSIsInZhbHVlIjoiQVNIaWpER1lSS054OVFSNHdFODJjRTVTR3hQUlpXUHV1NUVSQkpPSXdsUWRQdTRad3dtczgva0RtbWJpOXBVTUVKQVJHMlZHL0dxRzJ6aENQMGFPU0wyNHZxNmZjSkE0YkZMNDFXcHVYSUliQzdRTUJSUisxTzk2Y3VXMkx6ZGd1UzRmYUIzTWNqMkdUNWxjMnN5UDBndkd0Y0RWSUtvblJqZHFIRDNRZVdZVFlIYllhNXoyRU44RzdxNlZUd1Z0MjBtaENLS29TS3NqWVpvTWpjb3NHTjIxREtUUWlJZHlqTW8xU082UHVkL2xsTWc5QmRsM3hBd1dXMWVhVWR2TUlDOE1xOWJtcWRpU0MxZmJ5dWttdGVMdXNCWnZ4cGY1ZzZMS3RrcEtyS0VkbmJiMVB4RkRqZWY2TjVBMzBuUmV6ZDEyNlhCMzMrRkd2OGZ4VjdScEZlTTdWNExzZDQrbEpVL0JKZTBOcFQ0NXlJQkxnTHlaOFFHalFXMkwxM3BZdFprdFQxYkg2eVlTb1hQWTRKQWRsSXk4YWt4eHc4bnUyQ04zaXI5UG9rZ2xId1RLMS9FYkpQZXJXckxiTFM4TTBXSjYwQlpqTzB1WThOZlAwMVlLaFIrWUU3NkN3dDB4SGRHdGt2U3NXd2tKOVhWOURna3VOU3dyL2p3ZlEramsiLCJtYWMiOiI4YjUwOTQ5YTE2NTRkZmMyZTM5YTg3ZDY2MGQyMWUyNzllMTQzZjBiOTk2OTFiMzFlYmRiZWIyZDIwYTE0ZDBkIiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 18:01:41 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2028630864\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1269944677 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G4oUC4dvL91DvSdXtTSUa87JqfM4sfNNzjOiK6ns</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1269944677\", {\"maxDepth\":0})</script>\n"}}