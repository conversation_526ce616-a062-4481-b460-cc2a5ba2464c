{"__meta": {"id": "Xa229e25ec8d1467e2288912024804d3c", "datetime": "2025-06-26 16:02:04", "utime": **********.661551, "method": "POST", "uri": "/pos-financial-record", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.005368, "end": **********.661569, "duration": 0.6562011241912842, "duration_str": "656ms", "measures": [{"label": "Booting", "start": **********.005368, "relative_start": 0, "end": **********.33471, "relative_end": **********.33471, "duration": 0.3293418884277344, "duration_str": "329ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.334718, "relative_start": 0.32934999465942383, "end": **********.661571, "relative_end": 1.9073486328125e-06, "duration": 0.32685303688049316, "duration_str": "327ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 51441424, "peak_usage_str": "49MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST pos-financial-record", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\FinancialRecordController@SetOpeningBalance", "namespace": null, "prefix": "", "where": [], "as": "pos.financial.record.opening.balance", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=134\" onclick=\"\">app/Http/Controllers/FinancialRecordController.php:134-167</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.05224, "accumulated_duration_str": "52.24ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 17 limit 1", "type": "query", "params": [], "bindings": ["17"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.363886, "duration": 0.02628, "duration_str": "26.28ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 50.306}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.3983681, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 50.306, "width_percent": 0.67}, {"sql": "insert into `shifts` (`shift_opening_balance`, `is_closed`, `created_by`, `warehouse_id`, `updated_at`, `created_at`) values ('100', 0, 17, 9, '2025-06-26 16:02:04', '2025-06-26 16:02:04')", "type": "query", "params": [], "bindings": ["100", "0", "17", "9", "2025-06-26 16:02:04", "2025-06-26 16:02:04"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 147}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.603973, "duration": 0.01097, "duration_str": "10.97ms", "memory": 0, "memory_str": null, "filename": "FinancialRecordController.php:147", "source": "app/Http/Controllers/FinancialRecordController.php:147", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=147", "ajax": false, "filename": "FinancialRecordController.php", "line": "147"}, "connection": "kdmkjkqknb", "start_percent": 50.976, "width_percent": 20.999}, {"sql": "select * from `financial_records` where (`shift_id` = 44) and `financial_records`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["44"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 154}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.632362, "duration": 0.008150000000000001, "duration_str": "8.15ms", "memory": 0, "memory_str": null, "filename": "FinancialRecordController.php:154", "source": "app/Http/Controllers/FinancialRecordController.php:154", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=154", "ajax": false, "filename": "FinancialRecordController.php", "line": "154"}, "connection": "kdmkjkqknb", "start_percent": 71.975, "width_percent": 15.601}, {"sql": "insert into `financial_records` (`shift_id`, `opening_balance`, `created_by`, `updated_at`, `created_at`) values (44, '100', 17, '2025-06-26 16:02:04', '2025-06-26 16:02:04')", "type": "query", "params": [], "bindings": ["44", "100", "17", "2025-06-26 16:02:04", "2025-06-26 16:02:04"], "hints": null, "show_copy": false, "backtrace": [{"index": 26, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 154}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.64202, "duration": 0.0039, "duration_str": "3.9ms", "memory": 0, "memory_str": null, "filename": "FinancialRecordController.php:154", "source": "app/Http/Controllers/FinancialRecordController.php:154", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=154", "ajax": false, "filename": "FinancialRecordController.php", "line": "154"}, "connection": "kdmkjkqknb", "start_percent": 87.577, "width_percent": 7.466}, {"sql": "update `users` set `is_sale_session_new` = 0, `users`.`updated_at` = '2025-06-26 16:02:04' where `id` = 17", "type": "query", "params": [], "bindings": ["0", "2025-06-26 16:02:04", "17"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 162}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.6470711, "duration": 0.00259, "duration_str": "2.59ms", "memory": 0, "memory_str": null, "filename": "FinancialRecordController.php:162", "source": "app/Http/Controllers/FinancialRecordController.php:162", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=162", "ajax": false, "filename": "FinancialRecordController.php", "line": "162"}, "connection": "kdmkjkqknb", "start_percent": 95.042, "width_percent": 4.958}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "jEiKRXRaeQw7kpqtZmXNWu3bpmEnaxKAnNI8mVhc", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"success\"\n  ]\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "17", "success": "Opening Balance has been set successfully", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/pos-financial-record", "status_code": "<pre class=sf-dump id=sf-dump-383964513 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-383964513\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-2041330213 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2041330213\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-706410722 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">jEiKRXRaeQw7kpqtZmXNWu3bpmEnaxKAnNI8mVhc</span>\"\n  \"<span class=sf-dump-key>opening_balance</span>\" => \"<span class=sf-dump-str title=\"3 characters\">100</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-706410722\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">67</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1843 characters\">_clck=1lqc4d8%7C2%7Cfx3%7C0%7C2003; XSRF-TOKEN=eyJpdiI6IlF0S25xeHFLRnhJVzNTb3RiVGdrTlE9PSIsInZhbHVlIjoib1l4b1IxT28wUXNiZGxuT0N0SGdYc3V4VDNDN0FESzlkOGNqSjh0cHMvOHFITnd2WFVxWWZoNURLWmFDblJvdmpRK0crMUY4SWh6R1ZCelZBNWRwWDVKTnNtdzJkZDhzTy9abUg3MEh6dFBsNE0xYUJRd0JXeUJXU3g1Q1lPSnRLVFgrZ1J3ZHFjSDliOVkyUDA3MG0zdjV4azNzRmRRN0d3OWNzeWVURFFpamJUMDd5T0YzVTVXTEk2dFVyUjdrVCsyVzVibkxjQldVcExYZjZORVJLS0c3RjZXaTNlcUdWdWhiYUl0VURTaWNabkVrL0tUR0RZTkhGVFNaTnpaMEE3azUyMi8vclJBeDVnNnpTM2p4dVVLdXJXb25RMzJTWEFxbzlhK2NEcTdZSm9CZEgyY2dBZ2g5Zk1LV0lMdWd1cnhaNytqMFNqK2hhNG0zTDVFY3NGelJuTGwxTmdtYnd5YVFab2Nqa2NJb0NjeHdHTmh6OE0yMVBYZ21OUktmdFQ4V0NRTU5DTTZjSER4THQzak9vQk1VZ2tTUkN2ZjBrMWx2NW5Xc0NMRjZmSndER3VpM1JyQm5qWHhQbzd6WjdyODZIV0tSRXJZV1pnejF5L2FZRFg2bERZUkJOZ0NBc0lVWXpMR3RIZzBkTG1lTkVlaGtkNk43WCtBNWlXTWwiLCJtYWMiOiJjOTJhZWQ1OWI5ZjE4ZTYxNzNhNWZlM2IwODQwYWViMTRmMDAyZGE1YzE4NmQ5ZDkwYzNiZjEwNjFhYjJlNGVkIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Im1xb2ZoUHMzVkFaRVRsM0pJMXltRVE9PSIsInZhbHVlIjoiQ2pqWDlZT2pVSGFDVzBES3BBTTRVM0VLTlVJbm1xVTN0Y0ZqdzRWZFBHMHFWWC9kVkF5TlUvbnQzcHYyOENPbnFlL0ZQMDM5Q2RRaU45dnNoSTJzV1FDNVJ4RFBvZ2REbnllbWVTbmRvcEcyUHpjcXR5TWpicmIxUXN4SEpPYmpUakhBQWZnMjlhYmdPT1hpSE1LeFNOL1FZeG05WCtxVXVtcW1Rb2xJY0VJOEF4WmhQczF4MGVzKzlRSUxnTk01dUNDVE1jQW53Y2NORHJia1NRWlV6WXBFd2psaVpGYVFXNFR1aVFkY3JjWUUwZzZEeGRFTGV1ZkltdGFwcjJaQXFTcndSeW5WUStkTmMydEp6YlhaS05NK1pXWXhJVGpRVkpRaTNZaGk4OXRlbFpxRklLMmdYb1FQSytVQmhFNjFBT2lZZmcraVB3dGtZeVorNjNaQWxFSVVGY0JlZGtGbjhGQUxabE5ucEtiOVZvclloNGszN29vbDhudUVzeDdyYXptZDlJekFyKzFuZk9WOW9YZysxYms3TEFPVE16SlJFcTFyaVI0VUVPd3lYbThHcWIyNHhJN1RmRXpadlIwRXVQQXBPYldweVJzeEs5eVFSeTlFdFlqSnFvQkszdXFDMUpzcFN4bnBJTUQwbS9UTVJFeTExRVA4OHZ6N240RjQiLCJtYWMiOiIxYWE1MzM1NWVmNjUzOWEzOTMxMzhmNDI0NTM3YmNlODNjOTQ5ZWJiYmIyYjY3NWM0MDY1NWJkYjNjZGM2ZTIxIiwidGFnIjoiIn0%3D; _clsk=1h4itwx%7C1750953722878%7C11%7C1%7Ci.clarity.ms%2Fcollect</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">jEiKRXRaeQw7kpqtZmXNWu3bpmEnaxKAnNI8mVhc</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PSbLK6QAWuUzu3JpIF2TDtYQ1L7TOqCcejFUVX3V</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-755243941 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 16:02:04 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InZQNG81UVRORFZNaThvbDhXYzkydEE9PSIsInZhbHVlIjoiL1BCMjZ5SEtTTlRBalZ2cFNKOG5YcERUeDRlYUg0bWc4RTVpa2FqcGhyVXcxTVlzaXNOWU54Z2N6Yzc3MEdKQWVTTElLMC9PZDRrMDZQZkVyMjE1OWhFcXZkV0YrUWZ5NXlySGhMRHpzcTV4M2JsSXlQZnJ3cDBoUFM4dFJmWFU1TC85bWdmSVAyOStBQXV2NXFGZURKTXdndVFpem1lQ2xiME1Jc2JKWWp5d0NLMFVBbTU3ZTZFV1VONGNWU0ZtZkpRdVNvMVcxS2k1ekMwVlhWMnlkM3Q4RzZXaEhOY2pnMTdqckZ5TFZYZ0g4UjdRemxBNjZEeXI4bXlkdlF3b2pKL1lzeVVwaldodlNSektwLzJzUXFUWVJ2SmdqQlM3TVdVSkFkWEc2VU1VeGxSeXN4QUlGNjdEdHdvNWFpV2pFUWFmY1NmRktETVU1QmVjQ3JWdHVXYTBZenlLbEkyN3poeFVRREhkRHladHhsSmQ0MjkwS3RtYkd0ZWM5Q2YvMEJjc1RKcE92M1U4U3k1MHRGSkI5eHI3QTlVQTBrSUVTN2ExeVNDTE80T0FDMkM1ZWZ4U2s5THc5TVFaRTVLQlNXREIxRHozS2x2ZlBiZG1SczFjV0ZRcHlwT3hHRjdYRTVPczluckp4eGJwcVg5VE5IWjdvb2o5ZnNRaGdGcmciLCJtYWMiOiJjNGFmZWI4ZTkzOThhYWZhZDU2OTRmOGFlZDdmODJiOWY3OThkYzE2MGQ3NTFjN2E0NDE2NTRmNGQxY2Y2M2FkIiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 18:02:04 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjNxWUVJR1VvVDlCWk9xS3EzU0FObHc9PSIsInZhbHVlIjoidDQ1SWI2SnJPTVVjL2xkR3NDVmZYUjgxQjRpNTBFUkd3M2tTQys4dnJ4ajlzMjU4d1dXazVVOVVuQ2kyKzhLNFV1K1JDSVpqQWFNMHl5TDN5R0V1VkcwZFpkd3NGV0hmd1dhN3ZnUWpDaFpsZWRIcTVOdUtYdFN6dGF2RUNJcHc4Q0RFMlVzL0owK3FVaGs5RDBROG5xcldKaEhZcTlPY0dma2xaVlFUeUVveVUydXVwbTRoUVdRUnJwaGNmdjdpd1RtdnhQTyswcGJSblBHRC8vb29zWTV5Z3BLdTBTVUQvWElHb3ZHdElYQ21iTjdvOHNTcC91endXaW9GczVKeVRscS8xTnlLR1lGUUh6Z3NmbndPSWE4dENLZ2ZWbFFUbWlBcmsvREFwanVISzFESDFpTWVjcDhIRVg2TFBiOWw3ZWlBWDZzeTlqQ1MyN3piU3pNbHVZMlg0Rm41NklpcWR4RGNRUFJoU21XOUZlL0ZISit5eWpDeVVxR2ltell2UHJETy8reTJUZmdCbDYweU1ld3FhLzdUUStvZ1BlbFJlYmNVMENDbG1hcytXeUxTSUtRZFNWeG9DLzlHa3BjOTkrWnZyTjlQNitlNWFYZkJHNHdDRFJyeit3WGtXdTc1TCt0UzhOdEpCTXAyaldZTks3Y3dFV3FFcEdnTFRFejAiLCJtYWMiOiI0ZjczNzExZGU3NTMxZWY2MjZjMzU5YWFiYWU2ZWNmODNhZGYyYmI5Y2RjMjEzYTVlMDk1MTEwN2VkODJlMTc1IiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 18:02:04 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InZQNG81UVRORFZNaThvbDhXYzkydEE9PSIsInZhbHVlIjoiL1BCMjZ5SEtTTlRBalZ2cFNKOG5YcERUeDRlYUg0bWc4RTVpa2FqcGhyVXcxTVlzaXNOWU54Z2N6Yzc3MEdKQWVTTElLMC9PZDRrMDZQZkVyMjE1OWhFcXZkV0YrUWZ5NXlySGhMRHpzcTV4M2JsSXlQZnJ3cDBoUFM4dFJmWFU1TC85bWdmSVAyOStBQXV2NXFGZURKTXdndVFpem1lQ2xiME1Jc2JKWWp5d0NLMFVBbTU3ZTZFV1VONGNWU0ZtZkpRdVNvMVcxS2k1ekMwVlhWMnlkM3Q4RzZXaEhOY2pnMTdqckZ5TFZYZ0g4UjdRemxBNjZEeXI4bXlkdlF3b2pKL1lzeVVwaldodlNSektwLzJzUXFUWVJ2SmdqQlM3TVdVSkFkWEc2VU1VeGxSeXN4QUlGNjdEdHdvNWFpV2pFUWFmY1NmRktETVU1QmVjQ3JWdHVXYTBZenlLbEkyN3poeFVRREhkRHladHhsSmQ0MjkwS3RtYkd0ZWM5Q2YvMEJjc1RKcE92M1U4U3k1MHRGSkI5eHI3QTlVQTBrSUVTN2ExeVNDTE80T0FDMkM1ZWZ4U2s5THc5TVFaRTVLQlNXREIxRHozS2x2ZlBiZG1SczFjV0ZRcHlwT3hHRjdYRTVPczluckp4eGJwcVg5VE5IWjdvb2o5ZnNRaGdGcmciLCJtYWMiOiJjNGFmZWI4ZTkzOThhYWZhZDU2OTRmOGFlZDdmODJiOWY3OThkYzE2MGQ3NTFjN2E0NDE2NTRmNGQxY2Y2M2FkIiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 18:02:04 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjNxWUVJR1VvVDlCWk9xS3EzU0FObHc9PSIsInZhbHVlIjoidDQ1SWI2SnJPTVVjL2xkR3NDVmZYUjgxQjRpNTBFUkd3M2tTQys4dnJ4ajlzMjU4d1dXazVVOVVuQ2kyKzhLNFV1K1JDSVpqQWFNMHl5TDN5R0V1VkcwZFpkd3NGV0hmd1dhN3ZnUWpDaFpsZWRIcTVOdUtYdFN6dGF2RUNJcHc4Q0RFMlVzL0owK3FVaGs5RDBROG5xcldKaEhZcTlPY0dma2xaVlFUeUVveVUydXVwbTRoUVdRUnJwaGNmdjdpd1RtdnhQTyswcGJSblBHRC8vb29zWTV5Z3BLdTBTVUQvWElHb3ZHdElYQ21iTjdvOHNTcC91endXaW9GczVKeVRscS8xTnlLR1lGUUh6Z3NmbndPSWE4dENLZ2ZWbFFUbWlBcmsvREFwanVISzFESDFpTWVjcDhIRVg2TFBiOWw3ZWlBWDZzeTlqQ1MyN3piU3pNbHVZMlg0Rm41NklpcWR4RGNRUFJoU21XOUZlL0ZISit5eWpDeVVxR2ltell2UHJETy8reTJUZmdCbDYweU1ld3FhLzdUUStvZ1BlbFJlYmNVMENDbG1hcytXeUxTSUtRZFNWeG9DLzlHa3BjOTkrWnZyTjlQNitlNWFYZkJHNHdDRFJyeit3WGtXdTc1TCt0UzhOdEpCTXAyaldZTks3Y3dFV3FFcEdnTFRFejAiLCJtYWMiOiI0ZjczNzExZGU3NTMxZWY2MjZjMzU5YWFiYWU2ZWNmODNhZGYyYmI5Y2RjMjEzYTVlMDk1MTEwN2VkODJlMTc1IiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 18:02:04 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-755243941\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-636941409 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">jEiKRXRaeQw7kpqtZmXNWu3bpmEnaxKAnNI8mVhc</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>17</span>\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"41 characters\">Opening Balance has been set successfully</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-636941409\", {\"maxDepth\":0})</script>\n"}}