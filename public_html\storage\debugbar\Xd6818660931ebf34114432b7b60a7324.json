{"__meta": {"id": "Xd6818660931ebf34114432b7b60a7324", "datetime": "2025-06-26 16:00:17", "utime": **********.207258, "method": "POST", "uri": "/logout", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750953616.809596, "end": **********.207279, "duration": 0.39768290519714355, "duration_str": "398ms", "measures": [{"label": "Booting", "start": 1750953616.809596, "relative_start": 0, "end": **********.149633, "relative_end": **********.149633, "duration": 0.34003686904907227, "duration_str": "340ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.149646, "relative_start": 0.34004998207092285, "end": **********.20728, "relative_end": 9.5367431640625e-07, "duration": 0.05763387680053711, "duration_str": "57.63ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44223808, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST logout", "middleware": "web, auth", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@destroy", "namespace": null, "prefix": "", "where": [], "as": "logout", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=243\" onclick=\"\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:243-252</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.00188, "accumulated_duration_str": "1.88ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.176696, "duration": 0.00188, "duration_str": "1.88ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "c7Olbm9j3VGWU97WwGbmhGgptmjEatJHEweNGUI4", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/logout", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1422268988 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ET1WIQmDwH5DmIalEePkmJkdqregxR405rAMGD3B</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1422268988\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-86844215 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1842 characters\">_clck=1lqc4d8%7C2%7Cfx3%7C0%7C2003; _clsk=1h4itwx%7C1750953610407%7C6%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ijg4bWVpanJpTmwxcklGcUh2U05GdGc9PSIsInZhbHVlIjoiL1Z0UUpvcnR6Rk5wczBPTWticFBweW5obktHWldtdUxaR05ROXh2blM5SWVjay9mRUZnVUUvVWtlSDNJNFRPQ2NidUFTZUpLTjI3bURhWWV4NHhWNWwrQ3RVdFRyeXhsaFdKdVdpVmtQWG9vNGxsYXl3aE9tQlJqczl0S0pyekZKeGhpV1FRNWRLUmUwOEhPYUVIOEcybTZuVUtFYTV5ZzRjRXJiTDVySHFzcFM2Sm1xZnZOOFk3N3piOUszNkZtYjAxSWNScTJYcytlNkxNWlBjMkZndkhSYUQ1T0xHcHlibVVranVUazRDdXBybktQNSs4S0ltaG1rZC8xMzBObFpGZzAvS3lwNExYRElENHQya2JvVEtIMUowSWtNM1JSK2VPUjVMd3VBc0dNclo4c1dpclY1Mnk1NUJvMFVJa045VUtzK3RFK2J4ZjBueFpkOUVBYWFPMmdhQS84eVpQV3hPaHd1Y3hyTzI5cnJFR0dyNVgxVGlFWDdZeUNTTDNPQlRBRWNXTlVxN0pMUG1BcXlzZHJES21nSW0zdDN5VGlUMmxia3R1TWExSzVPb0krdzI3d3lha080UkUxMTBCbGdWSm10WUxWUG85NDJJT1VFVU43ckZJNFFWSlVRZGN2YzVzNDJIVVo2cHhsWHA4TUVCWVp6UGNadVhxUlk5TTkiLCJtYWMiOiI2OGEyZWRmNmZjZmQ3NmIxZjA0MDFmNWE1M2MxZTk0MjAxNjkwZDA2MTU0NDlmNmIxZDcxZDJmOTJkMWM2NTkzIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImhmYWw5MUNmWjJhTllOZmRNK1FleHc9PSIsInZhbHVlIjoiTStLaWFoKzVzU3RxbWFPZWRmYTJhazFwemZPSE9IMkptTUM1bENtOURxVWtZZjU4bEtGUU4rRllIenF0eWQ5Y2xtY1lQZUk4Zkg4MXlBb2hUcDltRGdMVzlTaVdTa3YzdGZiT2p1bndobFNGV2poRUxRR1J3RjYrb0xPL1QvTnNwMUhIcFFrMzJhMTZQTTBBRzFjQXFVVExUZVl2OE5tcmk5NXdiZGswOVNZS3B4dStNSFJSWmhXalU3am9PUXlOVFVtZWI2Zy92eUpDRHUwMjZmOEhyL2JzSjNxbk9SdVNFUjJkZk82d24zeEszSzlNS1VlRTZVTENaaEIxN0RHMnVCaWxlSVF1aUh6LzFzKzdSUzBwMVBWS3NhZlhCTUZVYnpJTk1CUUhpeWo0ODcvMEFzQzFZY1BVckNwU3NlYXdYbDJjV29IbStuV1g4bTdVUXQ4bFNUUWlRRFAxQVg4ejI1a09BeUFXRExPU0ZYKy9vR25weXRWcDZkVmhwTXlud3lpYnRzSGI2TmppVHQranc1K0lOckNoc05URXUzNnZHeHpNZzVsaUFCZkRyNEJJOVlFQy9ldlkxUXBhd0c3TnpSYkNoYWRwZmlhUFQ0eG16Um1aUDlqU2xlVDhYWW5ybzB3L1ZkcEpSNWFOQlh5cWRBbDlFTG9Ma2xFYm1VRmEiLCJtYWMiOiJlOTk0NTMyYzk1ZDkyNGIzOTI2ZjYxYjU3MjYzY2YyMmRmZGMzMmU1OTY5NGY4Y2Q1ZjNkNTRjMmU4ZWY1MDgzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-86844215\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1809612876 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ET1WIQmDwH5DmIalEePkmJkdqregxR405rAMGD3B</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GB2QVAT8duF2iPjEZeDSEXBnGVj5OQhrDoibeEka</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1809612876\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-480053913 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 16:00:17 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkZnOC9yTWgxNGJBUVR0aGJGK0kvaGc9PSIsInZhbHVlIjoiQ2ZhdXg3TUZWR2ZyVEE2YUh6RCtscGJMTnVaMjVlODdBbXd0NkVmKzdjTEZhcThUM0ZXbmEvbnlUTDN2YXpxd24xbjBhNlRZUFpveElmTk95dG1rcEVwRVpvZGZHeGxyR1ZBWlNqMlpDNnNtNGViWDVpT2JVY3M5aXNWV0p5U1pTMEtMTERHZVR0S3FRUVNvcXdPVVVIN0ZpUFJnNUtUNXBYOFhKRUhGeHlLczZUcm1pMnU1d1hBSkJvZHdveGprQlFCSTlRUERwVTVrYTZvYTNrcUc2Qm9ZempoSmp1SVUvZ3E5U3lwK3RPd0k4d1hKM281VjAxd3JpUHExYnllZTUyK2FyOE1NZXNrRWE5cUNCYXEwUTJ4bjF0LzRkQWZJTWdmRmt4S0lxMzBqaUREM0hQS3hVaDM3MnVrMlAvOThWL0dBNUVldmY3N3p3U1VwMTM3QTBDM0I4YXJSUVVBVEk1cnNQU3MxMHpNUXBDVFFjcXBSZjZFSFVqMW5FOXpnbzF5THVsRlRHL25pNmkzang1Z3JlOWdUdm44eVo5OS9HcUgxcU1nZHVtTFRBaDBUWkRPajNjeHBRSDRiNy9sdHlPbHJGanY4UzJrMDhyeUwydnRMV3FGZHlubmZZSXQ4c1pEaDVNM3U0aUYxelJRWXhyNzFMQy96cGNCVm1UVmciLCJtYWMiOiIzZThlNTYwYjY3NWExOTFmYmM5YjM0YWVhNjVjN2JmMzMzYmRkNjM2MzE4ZTYxNmExOGZiN2Q2MTE2OTUwNmZjIiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 18:00:17 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InVKY0ZVY1BhbmlaUmttZUFFMlRQMHc9PSIsInZhbHVlIjoiUFFYQzJ1T1pKVXk3MU1ISjRhZVdyZjBveFAyL2V2RUQ1Y1pWZytNWmpmZXllNzQzM0VmbE84YkN2c28wUzZUVGxBT096R2tZK21OakRKUkFCSzBXdTFhU0FhVzdsZHlrNGl0QzlUVThidmxuclYySFFpQ0ZvWmM0N3lVTHBtUEo4QzVFYTlEZ1IzUklwdG5ScGZSUVBibjJkQ0krMVBERU4xMlZhZmNxaGFNREw2TXA3WFFjV0tsd1hFSkd3MnA4WFVmOHFBSjFCZnVlZEFHbFNKbnhnVHdnZEF0WTdiaGFvM3g2RmcvQ09uSktOV3ZVWTJ4VUo3YlNsc3BYcllobGNaUDhoWmJEVHlsMFE1b0xPZE41Z1p3bFlIUnYxOWFlK1B3OU9hQ2huTHc0WEVYbEZQcUNITm1lbmFYNEdBV3UzMlhxblczSTdVVDgyZHdrenFWUnFDeVhibXRSeU55Y0RxL0xPbmhiVjlmNE5zWkhoRXV0ck5QbkFSdE1EZVRqbTlqaEorUlF0aE02Tkd0ZldXc0tjYUF0VFdURnRqa2NxNlVZblh5OGZscGMwSldFTjVDL0NOWG05cW1BWEFqSFg0ZWxXTGtTVjFPL0lwM3Bka0NvVVlSMUtzN2JTT3Z4WWhnY3cxN3lVdzdDYWRINzFXNjdXcWRNSjE0dzVENEQiLCJtYWMiOiJiNDkzYWMyMGRjODhiMzZiOWE3YjVjMDIxODEzNTczOGRjN2ZmZTY1NzNiYWY1ODQ2MDg3MjhlNzQ4ZjQ5OWQ4IiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 18:00:17 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkZnOC9yTWgxNGJBUVR0aGJGK0kvaGc9PSIsInZhbHVlIjoiQ2ZhdXg3TUZWR2ZyVEE2YUh6RCtscGJMTnVaMjVlODdBbXd0NkVmKzdjTEZhcThUM0ZXbmEvbnlUTDN2YXpxd24xbjBhNlRZUFpveElmTk95dG1rcEVwRVpvZGZHeGxyR1ZBWlNqMlpDNnNtNGViWDVpT2JVY3M5aXNWV0p5U1pTMEtMTERHZVR0S3FRUVNvcXdPVVVIN0ZpUFJnNUtUNXBYOFhKRUhGeHlLczZUcm1pMnU1d1hBSkJvZHdveGprQlFCSTlRUERwVTVrYTZvYTNrcUc2Qm9ZempoSmp1SVUvZ3E5U3lwK3RPd0k4d1hKM281VjAxd3JpUHExYnllZTUyK2FyOE1NZXNrRWE5cUNCYXEwUTJ4bjF0LzRkQWZJTWdmRmt4S0lxMzBqaUREM0hQS3hVaDM3MnVrMlAvOThWL0dBNUVldmY3N3p3U1VwMTM3QTBDM0I4YXJSUVVBVEk1cnNQU3MxMHpNUXBDVFFjcXBSZjZFSFVqMW5FOXpnbzF5THVsRlRHL25pNmkzang1Z3JlOWdUdm44eVo5OS9HcUgxcU1nZHVtTFRBaDBUWkRPajNjeHBRSDRiNy9sdHlPbHJGanY4UzJrMDhyeUwydnRMV3FGZHlubmZZSXQ4c1pEaDVNM3U0aUYxelJRWXhyNzFMQy96cGNCVm1UVmciLCJtYWMiOiIzZThlNTYwYjY3NWExOTFmYmM5YjM0YWVhNjVjN2JmMzMzYmRkNjM2MzE4ZTYxNmExOGZiN2Q2MTE2OTUwNmZjIiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 18:00:17 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InVKY0ZVY1BhbmlaUmttZUFFMlRQMHc9PSIsInZhbHVlIjoiUFFYQzJ1T1pKVXk3MU1ISjRhZVdyZjBveFAyL2V2RUQ1Y1pWZytNWmpmZXllNzQzM0VmbE84YkN2c28wUzZUVGxBT096R2tZK21OakRKUkFCSzBXdTFhU0FhVzdsZHlrNGl0QzlUVThidmxuclYySFFpQ0ZvWmM0N3lVTHBtUEo4QzVFYTlEZ1IzUklwdG5ScGZSUVBibjJkQ0krMVBERU4xMlZhZmNxaGFNREw2TXA3WFFjV0tsd1hFSkd3MnA4WFVmOHFBSjFCZnVlZEFHbFNKbnhnVHdnZEF0WTdiaGFvM3g2RmcvQ09uSktOV3ZVWTJ4VUo3YlNsc3BYcllobGNaUDhoWmJEVHlsMFE1b0xPZE41Z1p3bFlIUnYxOWFlK1B3OU9hQ2huTHc0WEVYbEZQcUNITm1lbmFYNEdBV3UzMlhxblczSTdVVDgyZHdrenFWUnFDeVhibXRSeU55Y0RxL0xPbmhiVjlmNE5zWkhoRXV0ck5QbkFSdE1EZVRqbTlqaEorUlF0aE02Tkd0ZldXc0tjYUF0VFdURnRqa2NxNlVZblh5OGZscGMwSldFTjVDL0NOWG05cW1BWEFqSFg0ZWxXTGtTVjFPL0lwM3Bka0NvVVlSMUtzN2JTT3Z4WWhnY3cxN3lVdzdDYWRINzFXNjdXcWRNSjE0dzVENEQiLCJtYWMiOiJiNDkzYWMyMGRjODhiMzZiOWE3YjVjMDIxODEzNTczOGRjN2ZmZTY1NzNiYWY1ODQ2MDg3MjhlNzQ4ZjQ5OWQ4IiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 18:00:17 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-480053913\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1197363910 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c7Olbm9j3VGWU97WwGbmhGgptmjEatJHEweNGUI4</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1197363910\", {\"maxDepth\":0})</script>\n"}}