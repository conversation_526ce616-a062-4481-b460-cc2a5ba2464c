{"__meta": {"id": "X15e4f4bb125417651d839094a6d01b98", "datetime": "2025-06-26 16:04:31", "utime": **********.747901, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.27906, "end": **********.747914, "duration": 0.4688541889190674, "duration_str": "469ms", "measures": [{"label": "Booting", "start": **********.27906, "relative_start": 0, "end": **********.697282, "relative_end": **********.697282, "duration": 0.41822218894958496, "duration_str": "418ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.697295, "relative_start": 0.41823506355285645, "end": **********.747916, "relative_end": 1.9073486328125e-06, "duration": 0.05062103271484375, "duration_str": "50.62ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46018168, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0024500000000000004, "accumulated_duration_str": "2.45ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 17 limit 1", "type": "query", "params": [], "bindings": ["17"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.729053, "duration": 0.00158, "duration_str": "1.58ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 64.49}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.739643, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 64.49, "width_percent": 21.224}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.74215, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "kdmkjkqknb", "start_percent": 85.714, "width_percent": 14.286}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PKqZteC3IabV2rdCxsXBMLnTb72fzH4qdR2KdQSn", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "17"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-673326104 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-673326104\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-2071682446 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2071682446\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-676535265 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PKqZteC3IabV2rdCxsXBMLnTb72fzH4qdR2KdQSn</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-676535265\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-167140714 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1bowxa%7C2%7Cfx3%7C0%7C1999; _clsk=1osc4j2%7C1750953855370%7C1%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InY5UCtLQm9TYzV4UlpnKzdiSHNtd0E9PSIsInZhbHVlIjoiZlFnd0I5TTBwSnlpK3dYd3owODdja3lCNW81eWgrOGZXd01mMmgwQnNUd3VCK3NiV3psMHZvc2hIblhqY2lUZTYwcTh2emFQYnordFAxSFJtR0ozR3Bmckc3eDY3N2NiM2JocFppOHpHdkZ0bFFCb2RlbVB0K2hIcTQ0MzNORWxCYVRGL0lnb0JkcmI5Y0hld2RLK0ZjVkk3YmdwSmxrQzBQQWUwQ0NJelNrQjVhSEtlRDJhVmErZzBZeDlYN3dFNHIzRVNVTzljL3FEZWF0QjlkMTFYeG0wWUcyWHFnMWh6akFlMkFBdFRCQ1pISHd6V1lXRXBkUFZoWmRtajZTSml3QlRSck9RSlhraytaMFBER29kY2pOcDZaaVdKV1NQWU5tUncweVVFUlVOdGU3cnEvU29CMUxxSWlBdjhzdytGUVRJRDNXMHNyNEthNUJ1a2ZVM2U5VGJXd0dnNXRtV04yTlZQS3pOZkhDVVB5bkNpdkRPeEg4VDJQdnVJTGdCejVORjZvT2RGdFcrTmRIbzNrZGJBUFQ5dDhGZGhkT2JrOXdQaG1JdzJEL1UxK0Q3alpIVVZPL3FsVU1iSjRtblByMmdFaTR2U3J1WnplaUNjc1MycHZJTEd1SFQyaDJRdUR4ZUtxTE1adnZkSU1MRXFwYWpOak5qdS9hRnhHQysiLCJtYWMiOiJhNjNjOGExZjgyNGVjMWJmNDk2NDdhM2I2MDU2ZTIzMWEwZTg5MDIxOGExMTgzNWEzMDcwMGFjNGI3MDEyZDg3IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IndsQUZ6SVQ2SEIxZ014QU9iRlRVVFE9PSIsInZhbHVlIjoiRXJoVnNwaEJzQnhXQW93L21QSVlxYnFwK2k4ZW1Mdi9vcEZBZk1xMkJONm1wK2tSdEFBR3l3YjJkcnBkMnpJVnhnNGRWS1ZRcWZ1V01oc0U4N251VWJxcWs2bjlGQlVtMkw4WlZnbVJKWU1oZkI5SlFsbEtkRXVkZFh6dTNMU09Jcng3Z3VnQVIyYTdNODdXbU16bHhGV0VObGk2d1JOekVOOXYySVhqMjRiaDdFOU1nVTE0L29xam42QWM0N2RmMWpZSjlIdDZOY3RYTE0yV1NMdWkweWZId1A0R0ZZRHdqYWI2VU5LT3pyVDJwTTNZaHJxODBtTUNpL2R2UTVEUzQ5V1JrRGRFdDBSMmorZEpaY1QwMVRyajQ2VDdHcHNud014TE9pM3p6ZE5EaTExMURPU2MzU3MvbmQ0bWYrbHFTTmNnb1hpRTYra1FHc2lzaElLMGJwR3d1VTBFOVhJNnlsRktWV1dBWHc2cEhteEdoaitERS8zMytxZ2FKeWgwT0JtSy8rSGpXSGc3eHBYNkE3YWUzVTJNalBoV1luMHVYbnpOTmUzaFBlNkJIc3l5VXpPd2dkZVlBRGRlMk5GNm1wNzMxVHkrRDQwMlpjcUJaWkNaZmdHK3BRclh1Z2E2TGhaNDFxaXlUajdWMXMrcUtYMHBvSzlyOWlqT3RTY24iLCJtYWMiOiJiZjNhNDNlNmIyYWQwOTE5NTQwN2U2MGM3NWVmM2FhYzMxNTUxODQwMTU4ZWY1MDYzMWY4ZDllMmQ2NWU4ODI1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-167140714\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1287477275 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PKqZteC3IabV2rdCxsXBMLnTb72fzH4qdR2KdQSn</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GUUMfjgJKgBqP5oaDcRP4Rfu0FM4k6V9zWwhkyoG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1287477275\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-616376018 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 16:04:31 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkFGMnh5RzVvYkZVbktkY3Z3eTVwSHc9PSIsInZhbHVlIjoicVZ2NS9aUFlWcDdVWVo5UlFCTG1raUQ0ZVlzb29mNjdXaE10V0N0UnphTDJpVzYwc1p6Rlp0YzZCWk4vamZjMlZLaUtMWERHdlFockY3aHV1N3hHQ29IZ011TFdEeVdlRTNBdEMvRi9QdFlaVDNhTGlIanRzUTRhRXpBdHpKMzFRY3lxOXYvZlhQajhDNUwxekE5aFZHd3Y2T3ZZM1J1T1RsMVZ1aDlpZU5WMDFDVndzZWg5VklpNnVXRzBFQm13QmN2Q3NTN1hYemtvbE5IM2RsME5qK1VlWXByZWZNMjlTamt1ODFtRExxT3lnSDJMK3pBT0h0a0QwbjRTbjBJUStId2RYVmMrR1poaCszeXpMNVo2RnRCQjh6T2ljL1k1c0diRkl6NElxdVZpZ1RjOUJNa2N1SGFrWXdjQ3hMSVpEenJDMHVOUCtyRGlTSmNZU2tjUUovTG40UFZONEZxYWhsVElVZ1FaOThwZ0JKZG5yLy9pSzZzdDhZbzhjRHBDYmI0dEpDWS9JL2xOQ3pnVkp1TTVFNFNjMFR0eTFmRTE2RHpoUmlTOWlvWUpZa1hKSVdydUE0U3pLNkRTeWUvNnl5Y2xWK1MxK285ZnI2blplZHpza0RRc3lUdmNXWFVXZ2d0UkpaS0cyVlZVMTl5eUsxQkR5K0ttRk42ejFaZ0siLCJtYWMiOiIxNTI1ZWU1NDFhZDE3N2IzNDMwYzBkNDYzODUyMGNhZmVkMzhiMzA0YTY2MTc0MTMyZDUxZGFmODMwOWJkN2E5IiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 18:04:31 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InRMeWpFdG1oYldGbWxWVXZXVVhrUmc9PSIsInZhbHVlIjoibjdJdDlMSTA1SjVsMUlORFdqM2lZVXVtR2k3TmRXSXNiVWwzbHVNTDczZ0ZyWTh6VkF4OXFOQVRlSStWUDNlMTlEQ2dnSi9IQmdwOHdzd1JaMTE5RC9xUHhXa3hkUy82MXB6eWY5c2Y2dUxiTXBuWmd1VU1XL3dCZFlTRktjOUJKbEpqSlNIbTFsdHNpb3B4bExHc0dXN1NCNy90UTgySmY4OHBIMkVDL0M5YmtqM1QxcHI0dzhSK3RTajkxeFdkdDR6SjFvbXhZbEFLWVZHRWRxaVNqTVRpZktESFlmMmxSUmVxbkVaN1cra3RTU0VoUW1oQzR3RnR1ZmpXcFFjbTd0UnlVV0hMM0ViOXlnU2ZTeG0xenl2K1lzWENqMmxTcVlxUW1tWExZd0ZKOGFRdHJEc004c2tTTTVQVWNCZTdpcDZaZGZ5Zk41UXRYdnloZnRnMEhEQlZCMEttOTlmQ2ViQ2I3Y2M4K0ZWVDMwKzNLL0xkN0ZMMUt3QitKM1FuVnFJejE1ZFZGaHl2cmhVUU50eFRvbVgrMytTTWNiSm1VVzViem5YYlVpeGg0STZzeGd4cGJDZis0Tk16MXl4K2xVZE9aOGIvbHRBSHhQK1BrSEFKb1ltRzVCSEhhdENLNWVpU05ZWDhhNkhWVGlrWHB0Y0NTOXZBT0ZQcE1nQ0MiLCJtYWMiOiI0YjE5Yjg4ZTdkYWZkNDk2YThjNzYxNTBiZGRmYTEyY2YwNzZiMDljNDBhOTYzNjJhY2Q5Y2Q2NjJkMDI4NDIyIiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 18:04:31 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkFGMnh5RzVvYkZVbktkY3Z3eTVwSHc9PSIsInZhbHVlIjoicVZ2NS9aUFlWcDdVWVo5UlFCTG1raUQ0ZVlzb29mNjdXaE10V0N0UnphTDJpVzYwc1p6Rlp0YzZCWk4vamZjMlZLaUtMWERHdlFockY3aHV1N3hHQ29IZ011TFdEeVdlRTNBdEMvRi9QdFlaVDNhTGlIanRzUTRhRXpBdHpKMzFRY3lxOXYvZlhQajhDNUwxekE5aFZHd3Y2T3ZZM1J1T1RsMVZ1aDlpZU5WMDFDVndzZWg5VklpNnVXRzBFQm13QmN2Q3NTN1hYemtvbE5IM2RsME5qK1VlWXByZWZNMjlTamt1ODFtRExxT3lnSDJMK3pBT0h0a0QwbjRTbjBJUStId2RYVmMrR1poaCszeXpMNVo2RnRCQjh6T2ljL1k1c0diRkl6NElxdVZpZ1RjOUJNa2N1SGFrWXdjQ3hMSVpEenJDMHVOUCtyRGlTSmNZU2tjUUovTG40UFZONEZxYWhsVElVZ1FaOThwZ0JKZG5yLy9pSzZzdDhZbzhjRHBDYmI0dEpDWS9JL2xOQ3pnVkp1TTVFNFNjMFR0eTFmRTE2RHpoUmlTOWlvWUpZa1hKSVdydUE0U3pLNkRTeWUvNnl5Y2xWK1MxK285ZnI2blplZHpza0RRc3lUdmNXWFVXZ2d0UkpaS0cyVlZVMTl5eUsxQkR5K0ttRk42ejFaZ0siLCJtYWMiOiIxNTI1ZWU1NDFhZDE3N2IzNDMwYzBkNDYzODUyMGNhZmVkMzhiMzA0YTY2MTc0MTMyZDUxZGFmODMwOWJkN2E5IiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 18:04:31 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InRMeWpFdG1oYldGbWxWVXZXVVhrUmc9PSIsInZhbHVlIjoibjdJdDlMSTA1SjVsMUlORFdqM2lZVXVtR2k3TmRXSXNiVWwzbHVNTDczZ0ZyWTh6VkF4OXFOQVRlSStWUDNlMTlEQ2dnSi9IQmdwOHdzd1JaMTE5RC9xUHhXa3hkUy82MXB6eWY5c2Y2dUxiTXBuWmd1VU1XL3dCZFlTRktjOUJKbEpqSlNIbTFsdHNpb3B4bExHc0dXN1NCNy90UTgySmY4OHBIMkVDL0M5YmtqM1QxcHI0dzhSK3RTajkxeFdkdDR6SjFvbXhZbEFLWVZHRWRxaVNqTVRpZktESFlmMmxSUmVxbkVaN1cra3RTU0VoUW1oQzR3RnR1ZmpXcFFjbTd0UnlVV0hMM0ViOXlnU2ZTeG0xenl2K1lzWENqMmxTcVlxUW1tWExZd0ZKOGFRdHJEc004c2tTTTVQVWNCZTdpcDZaZGZ5Zk41UXRYdnloZnRnMEhEQlZCMEttOTlmQ2ViQ2I3Y2M4K0ZWVDMwKzNLL0xkN0ZMMUt3QitKM1FuVnFJejE1ZFZGaHl2cmhVUU50eFRvbVgrMytTTWNiSm1VVzViem5YYlVpeGg0STZzeGd4cGJDZis0Tk16MXl4K2xVZE9aOGIvbHRBSHhQK1BrSEFKb1ltRzVCSEhhdENLNWVpU05ZWDhhNkhWVGlrWHB0Y0NTOXZBT0ZQcE1nQ0MiLCJtYWMiOiI0YjE5Yjg4ZTdkYWZkNDk2YThjNzYxNTBiZGRmYTEyY2YwNzZiMDljNDBhOTYzNjJhY2Q5Y2Q2NjJkMDI4NDIyIiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 18:04:31 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-616376018\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-295765765 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PKqZteC3IabV2rdCxsXBMLnTb72fzH4qdR2KdQSn</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>17</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-295765765\", {\"maxDepth\":0})</script>\n"}}