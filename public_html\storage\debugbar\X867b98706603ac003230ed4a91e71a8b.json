{"__meta": {"id": "X867b98706603ac003230ed4a91e71a8b", "datetime": "2025-06-26 15:59:47", "utime": **********.86829, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.487781, "end": **********.868304, "duration": 0.****************, "duration_str": "381ms", "measures": [{"label": "Booting", "start": **********.487781, "relative_start": 0, "end": **********.817844, "relative_end": **********.817844, "duration": 0.****************, "duration_str": "330ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.817853, "relative_start": 0.*****************, "end": **********.868305, "relative_end": 9.5367431640625e-07, "duration": 0.*****************, "duration_str": "50.45ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00246, "accumulated_duration_str": "2.46ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.843242, "duration": 0.00163, "duration_str": "1.63ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 66.26}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.852489, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 66.26, "width_percent": 12.195}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.861394, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "kdmkjkqknb", "start_percent": 78.455, "width_percent": 21.545}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "ET1WIQmDwH5DmIalEePkmJkdqregxR405rAMGD3B", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/dashboard\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-3612371 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-3612371\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1842 characters\">_clck=1lqc4d8%7C2%7Cfx3%7C0%7C2003; _clsk=1h4itwx%7C**********305%7C3%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Im84Rm53aTRycWo5cVk3UlIrQ1VyOEE9PSIsInZhbHVlIjoiTEh3RkUwWFRMMzQvUDZCaExVQjdFUDUyZ0lwT3hTb0crU0dZRVE4aWdrN09NWlZWZ21OYk1xeitxcVhBbEVMYmlkeldxK1dXcEFFYXRDVzd0ZVowQ0M2SjRIRGo2ZGRERzRZK2JOUUorS1V6N1JUODY0azhKQkhPUUNrNmdPekY1KzFZQjFsajRuWU1WWFNVMmdRM3ZmbE85dXZuaWJBRnZCTElsZkNDaEdYM01DOXVyZXNVSCt5TmlkSUI5TFFna2M4a1B2SW5RZlRjTWF4eWI3R1p2Yk12WVNQZWt4aE5NWTJZT3p4a2J5Q3lJQXhiY2tBY3JoVDdiSVFyRXZad28rYmw3d2lHNGc0S0oxdWxuVUZROXVTNEJMQlYwc3RIRVhwNmMxc3BqNmZWWmtmMjJPendiSFFkWk5XSnNOUnBhWHB2TUQ1WWxncktMK01qbWhDOHNCMXc0aGxUSGpUU01hR0NWcDZ3SmRpaFZNU3F5YkF3cmJESFBka2lEcDV3Y3h1ZEVodFVaYWtPNUNFMkVHMDZyd1pTSkNKbjhuY3dRbVhrWXliTnJ5UXdGVzFRYWNKbUxBV25Ibm1OS2plTEozbFJKa2xqUHh0QVY5Z3llR1BZUWdkcXlnaEpQdUMyY2hFaEp6dWR1SWJvNC9HbWMxMGtSK3BDRjNFam05VDMiLCJtYWMiOiI4NjZlZDU5MWM5ODY1MTJhZDMzNDJkYzFjMmUxMzM2YWI3YjMzNTQyNzk4MGVhNDA2YzlmMTEwODA5ODY4ZDBkIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkVQQldoYXBxdG1iRWlRVDVEa21XUnc9PSIsInZhbHVlIjoic2RyOXFaOG5hNVNLTlVlZzhxMWcvWHNGRFVZSllIaGZ4T2lSeVBaUGUzZjVNZ0Q3T0NCUnlBUTY4bXEza3N6MTVldm5Mb1kwMTJ1aVQyTmlrK01LczRZcUcrMENzclExNjB4Z053d0hkdHJkV3ZBWkxwQXlPc3l0RTd1YlJ5UWJLNWJqNEkxb0krQUhFQnV2NmxaMWVwTUpxQUQwTnpVcnBXckxWUExqY1NEYmdZeEd5aFNHNWVPcUxDQWhMUEEydGttWTJrV2YyRUtuakRkUnZZY2xTdnNwSFVNL25QZCszaTBOMWNVVUJ4azFhR1FpWElGZ3ZpNXE3ZzMxK09ydy9BL0d4VmpSQW54eUZ1cnBCZFZJN0d3bWx0SlIvVGFrbkVsMEhSdHJWR01FcjJUdUk0dCtEVm1kU3QreVNOL1A5QkF5bWk4SHJoMTFOSnhYVjQvWjFDRUNUOEg5dTlEbisyMGd4K09SWkF5eS9EbmNpUlI1S0VMZnRnOEtwK3ljd1YrN3QxSE9iTUVCajdTTjFUTzVOTmFCT0JhT0JNcjA0MFAvZ3Bmc1dValJrMjYwa1RDWXRGRHlIQzV2VnV4cVgxb2pSNTllbWQvUjFhNEZOTStFSWdRUXp0Q2lsdjhvQndWSkI5KytjZG5XczFMTmxIU2YxNkdTTGhWUDV1QksiLCJtYWMiOiJkZjJkMWIyODhmNTNlOTQ3NWRlYThlZDNlYzhiNjQwN2Y4ZTg2ZTRhMjBiZTRjN2VmODczMGU3OTE1ZmQwMmUzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-670085254 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ET1WIQmDwH5DmIalEePkmJkdqregxR405rAMGD3B</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OKKbyGk1l1oGFNUe7gxioQDH0z4ATppFVbVTtLek</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-670085254\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-378452285 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 15:59:47 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImQ4KzRVc3lIRUVTNmJhVXF0alRZdkE9PSIsInZhbHVlIjoiaEljTDRRTzNBZDFoVjlrV2dJWE9KU1NNc1dBc05yK1hva21ZQk5mdCt5R1ZyNk9uSURhN2d5RFd5T2Jxc3haeFl2QmIrcHZSKzFWdVAxWC81SVdyLzdpeVVuejJSQ3hxZEFRYVp4QjdlcXdYb25KMXFZMHpYWTVGVzVWTmNYTVRoaFFXNThLTDh5cFVsZ1hrS2NmY0pBandRRHdwUndLWGZJSWJqTzF4WGUvNitMNXd3WENEVHptbkpxS05zbTMrTzBwWTR6MUxvOWdkcHN0RjNybmpDZTlUakFNY2Z4RzdDQlRrVENoY1hZZXAycXZLWFZOLysvVEI0OW93RG90S3dDODRNMmJDQ2VhSHVWRiszSmVzUjVQenNtVjFHWEJqNUpnZ294NW1XRUVFWjA3WFlRUnFXYno2SWQ0ZnQ2eTBMSkx2dFhFZ2ROUGFhK2M4SSs1UjRjanloekxWZWtrSWJLOVE1YjU4SmcrNG92SnQxU3pRNDlwdDk3WGJEV2RuNnU0em90cXRxTzVPcExUemlyalRWby9US01ZT0MwVTBRczdndHA4b2gyWVBUeVpsMmhza2ZBSURCS2pDQ1dhT2FtRDYzVHExaHVmVTlYLzVOb01vZ0hhK1VIVThTeGRmZXNlVUZ5bHJMRDZxbVZGdnZYVTd2MWVBbWtjR0l1ZE0iLCJtYWMiOiJkY2YyM2NjM2EwZDU3YjE0YjRhZGM3MGVmOWZkNWFiNTg0MzE2MWM5MTJjZjliMjlmYjdhZGYxMWNkNTI3ZDY2IiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 17:59:47 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImNIQnVkM1ZaQXowSk9KL3did20rTmc9PSIsInZhbHVlIjoiN3ZKNE5YdjFTa2tuQnByNTJqRGFPdEsyN2dra1dUeWcwNDhSS2J6bTJiZkdQWVVHT0N6L1pSd0lxcVFjd2FyK0hlRllYclpnaDFzcXRKaythWkhFSDYyWjdwOGNMRUR0Vm5LMVpsMis4RWhTQmZjN1E5dy96cmFsQmY1YjIvOEMvdDZaZDZSdXljeUl5U1RyaTVEUkxVTktwaUE2L2UzdW1ta0h2MFpzRzFvV1o0T1Z1ZXFpV2Z3QWNTMGhEY3hVdFd4ODlBRDFRVUY3ZGl5VGplaWdvRlMvVE1Rc2VVdTFMMzhtdHlLVmR3aE5FK3pOclM3Zm9yUUJHa0hKWEtaZkx6UUl2bjl3SE9ZdUVIZTU3SlZocHU5SzVDOU1zM1U3Mmw5RjRLNGp0MmhscitCTDFsMXhITzlCaytVM2ZPSXo0dDdQTEhydWtOd09TajFCM2hJWDBMejVJWXgwK29TeDI5bkpVd2xkV0VOTitCMllUVWhubWZkbFpsZGFwZmRrSjFEcDhzNElOR3hjN0puZml0THlYOWdxU0VCeUtFbW1Ma3ljRXBvVWkzRzlmZGxWSVo0aE9CR3cwNnVWNkllMVAzWFhDYmxXWmRTMjVhZUpFZHpEdFhOL1JiRlo3Qy9weDMrUmVYWVZMcWJrcngyQ1dId05kUy9xbUNEZlBJY20iLCJtYWMiOiI1ODliNjQ5ZTE2OGJmMzMxZGJmYjY3ZTkwZmM5YWU3MWU0N2NhNGJhOTUyNjJjMTU1YTU2ZWE4YjNlMzlkZjQ4IiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 17:59:47 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImQ4KzRVc3lIRUVTNmJhVXF0alRZdkE9PSIsInZhbHVlIjoiaEljTDRRTzNBZDFoVjlrV2dJWE9KU1NNc1dBc05yK1hva21ZQk5mdCt5R1ZyNk9uSURhN2d5RFd5T2Jxc3haeFl2QmIrcHZSKzFWdVAxWC81SVdyLzdpeVVuejJSQ3hxZEFRYVp4QjdlcXdYb25KMXFZMHpYWTVGVzVWTmNYTVRoaFFXNThLTDh5cFVsZ1hrS2NmY0pBandRRHdwUndLWGZJSWJqTzF4WGUvNitMNXd3WENEVHptbkpxS05zbTMrTzBwWTR6MUxvOWdkcHN0RjNybmpDZTlUakFNY2Z4RzdDQlRrVENoY1hZZXAycXZLWFZOLysvVEI0OW93RG90S3dDODRNMmJDQ2VhSHVWRiszSmVzUjVQenNtVjFHWEJqNUpnZ294NW1XRUVFWjA3WFlRUnFXYno2SWQ0ZnQ2eTBMSkx2dFhFZ2ROUGFhK2M4SSs1UjRjanloekxWZWtrSWJLOVE1YjU4SmcrNG92SnQxU3pRNDlwdDk3WGJEV2RuNnU0em90cXRxTzVPcExUemlyalRWby9US01ZT0MwVTBRczdndHA4b2gyWVBUeVpsMmhza2ZBSURCS2pDQ1dhT2FtRDYzVHExaHVmVTlYLzVOb01vZ0hhK1VIVThTeGRmZXNlVUZ5bHJMRDZxbVZGdnZYVTd2MWVBbWtjR0l1ZE0iLCJtYWMiOiJkY2YyM2NjM2EwZDU3YjE0YjRhZGM3MGVmOWZkNWFiNTg0MzE2MWM5MTJjZjliMjlmYjdhZGYxMWNkNTI3ZDY2IiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 17:59:47 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImNIQnVkM1ZaQXowSk9KL3did20rTmc9PSIsInZhbHVlIjoiN3ZKNE5YdjFTa2tuQnByNTJqRGFPdEsyN2dra1dUeWcwNDhSS2J6bTJiZkdQWVVHT0N6L1pSd0lxcVFjd2FyK0hlRllYclpnaDFzcXRKaythWkhFSDYyWjdwOGNMRUR0Vm5LMVpsMis4RWhTQmZjN1E5dy96cmFsQmY1YjIvOEMvdDZaZDZSdXljeUl5U1RyaTVEUkxVTktwaUE2L2UzdW1ta0h2MFpzRzFvV1o0T1Z1ZXFpV2Z3QWNTMGhEY3hVdFd4ODlBRDFRVUY3ZGl5VGplaWdvRlMvVE1Rc2VVdTFMMzhtdHlLVmR3aE5FK3pOclM3Zm9yUUJHa0hKWEtaZkx6UUl2bjl3SE9ZdUVIZTU3SlZocHU5SzVDOU1zM1U3Mmw5RjRLNGp0MmhscitCTDFsMXhITzlCaytVM2ZPSXo0dDdQTEhydWtOd09TajFCM2hJWDBMejVJWXgwK29TeDI5bkpVd2xkV0VOTitCMllUVWhubWZkbFpsZGFwZmRrSjFEcDhzNElOR3hjN0puZml0THlYOWdxU0VCeUtFbW1Ma3ljRXBvVWkzRzlmZGxWSVo0aE9CR3cwNnVWNkllMVAzWFhDYmxXWmRTMjVhZUpFZHpEdFhOL1JiRlo3Qy9weDMrUmVYWVZMcWJrcngyQ1dId05kUy9xbUNEZlBJY20iLCJtYWMiOiI1ODliNjQ5ZTE2OGJmMzMxZGJmYjY3ZTkwZmM5YWU3MWU0N2NhNGJhOTUyNjJjMTU1YTU2ZWE4YjNlMzlkZjQ4IiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 17:59:47 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-378452285\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-902349903 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ET1WIQmDwH5DmIalEePkmJkdqregxR405rAMGD3B</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-902349903\", {\"maxDepth\":0})</script>\n"}}