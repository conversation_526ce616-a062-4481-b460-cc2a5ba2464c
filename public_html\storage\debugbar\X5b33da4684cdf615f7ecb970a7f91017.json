{"__meta": {"id": "X5b33da4684cdf615f7ecb970a7f91017", "datetime": "2025-06-26 16:00:02", "utime": **********.17249, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.79218, "end": **********.172506, "duration": 0.****************, "duration_str": "380ms", "measures": [{"label": "Booting", "start": **********.79218, "relative_start": 0, "end": **********.119761, "relative_end": **********.119761, "duration": 0.*****************, "duration_str": "328ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.11977, "relative_start": 0.****************, "end": **********.172508, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "52.74ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00247, "accumulated_duration_str": "2.47ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.147186, "duration": 0.00151, "duration_str": "1.51ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 61.134}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.156548, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 61.134, "width_percent": 15.385}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.165647, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "kdmkjkqknb", "start_percent": 76.518, "width_percent": 23.482}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "ET1WIQmDwH5DmIalEePkmJkdqregxR405rAMGD3B", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-******** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1842 characters\">_clck=1lqc4d8%7C2%7Cfx3%7C0%7C2003; _clsk=1h4itwx%7C1750953587305%7C3%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkV2YmF6V0hZKys0YzB1UCtVcS82MUE9PSIsInZhbHVlIjoiRSswUDhvYXA2NFdLU28ycXY2c3Nqa2xPYk5KUE9ERGF3TWthL2RyTmhWTkpHUUZWTWdEMi9UYTBPdE5vUXphWnlQUUkzK0dGdFhFaGJZNGczVjhJK2R2bTJYOTlFcjVhelVGTEo3NTJWYnR0aksrMVdlWitEVmNIMkZIYnZVazFZTFNRcHhJdFh0TGZoa1ppenk4cW1iSUxZUUYzVzkwV1JQRi9uOVc3Ty9STUs0S3NXaXVRdkhiZ21mN1Z4M3VsK0srV1BSNG01bjlmTE1mYndKVmErN0Y5emN4cS9KQzlnd1NsWFBXZ3J2ck5ORkc5QTFLK1MwV0RoSi84UmpGeXRZb001WVJZSmllckhFYmg1azliNXNaUndFTDU4VzV4Q3YxZjA4R040YzUyeTgxWjZKbFN6VXl5TXFwWjQ1TnZ0WkZ3VU1GYnZVNHdYSjFQOW9Sb2d2dDhSbExEb0Q1TkpZeEZtOUVoclhNNlYrVnVUZThabzRMdzRQcitITmxZWVpkT0I2UnBwalVMM095cXlLU3NlU2FrTXUrbTJCaDJjdWs3TlpvalRXNWtDMGhSM1FxMkF1K0lKQVVKSkNhc3lMcStEdlBvaWdUUHRhTnYvUXNDenI2KzUzL2pTc1ZFdGpnVmdVanNpWUxNQ2NTRmgwdEdPVFZRQUs0WWFSZ2MiLCJtYWMiOiIyODcyMzZlYmU2NTFjNDUyNjRmZjE5YThjNTU2YzEzM2I1OTU1NjhiMWZlYjQ1Y2EwZTMyMjQxMzliMDU3MjlhIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjRqUFc5RERvbysvVFYxMmVyYXpNM0E9PSIsInZhbHVlIjoicTZCRnkzd1dHV1k4cHYvcXVJejFkNURQd3grZ2RwQWRuVTIwMlpXa01jeVNzbUtSTFlHYUJxNmVVMXpLdVlyWTlZSUdBVEhiTDJmdW9iR0M0UlU0cXN0aWNEUzR4NFVCYzRHU212MnN1ZnZYbEpoOHV2RGlMcG52cGNDZVg3ajBFSkcyWmNha2JQZGJnK3lCSEdnbFNzMGhNYWdKV3VMeUdRaDB2NEx3ZFRsNGFpYTQ4N2ZHdExoRkhnVkkrWjdqNzF2cFo4Q2U1eFZjNEhwZGhnUnJMclBwamlUOFRXVU9QL0toSUw3amJXRkRDcVEvWEVvODVNMWFtbUZhdG0zMm0rU0JPaGtVSFRJRU9Ld3lkdVRsU0JIZ3NyWXl3YWNuQUtJalNvdGN3dkFibFR1cTcxVFRBZy9NUjNDN1FkM1VmQjNLOGxKWEhLdmQ4bTBJalhPUnUxa0p0cHUxN1pRSXBJeFpqVzlGNWY4V0hFTE5KcGR4b0Nobm9KcElTQWdZT1lQMTZRSlRiaUxiY2o1bnNBRkwrVDZIRFBIQ2tXSWl1UVBUMUxtUVpxeUM1NGRBbk5Rc0dwN0Mwak56dlhUcHZ2UzNOaW8vNjFLQ09LS3A1d0tXK0sveWU2b25KZGl3eVhld1l3Tks1THlHNTZFLytqRkE2TXZTTkFsQUdEdWwiLCJtYWMiOiIzZjNjZjliMTI0NzM3MjNhYTllYjM2N2MwNmRiMGUzZjNmYTRhNTQ3ZTE5ODM5MDUyNjliZTA1M2NhMmMxZjExIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1368314609 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ET1WIQmDwH5DmIalEePkmJkdqregxR405rAMGD3B</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OKKbyGk1l1oGFNUe7gxioQDH0z4ATppFVbVTtLek</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1368314609\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-56453320 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 16:00:02 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlhiSzlDU01mV2lCcDFNL2ZleWpKUlE9PSIsInZhbHVlIjoid2NMMml3VFV3WTRyblFvNWRxYnZib3ZSSzBualZxai9hT2ZyakJmZHpjTnUvRHcrZkplNEtTdG9lNnhjNjkwWitQT1pKWUtPRkN5M3gwSVR2NVc2WkFhRUxMZnBCQ2t2aE9lUG5vcWRQZjZ0WHV6QkN6eXByYmp1dXYxSjZ1VElqWk9WUnVPcGg1dFFiL3RYREdVYUprQmJTN0JRM1UrT1F1ZWoyaStkZ0VVOGI4cGRHbFoyWTRmSzNiQkNEL1Eyd1JIYU5XTG5JR3ZndnluWGtjYVpTellVOXYyM1kwUElJT3BvRkNCOXFqNU1QNHhyU1p0bGVTMzFpTE1zUlJOR3lCT3djU2ZpYU05LzdRaXVtc0JINmxoTm5MazByd1diS1B3eFcxQ2Q2a3JlbFNBNjBZWExwZUYybGRYNDBuRTdxemlCSDdKR0R6Y0QyKzFQdkVmS3c2cjQrakNZTjg4NUtXVUJUQXhvWndtQzF3TTAvUUtoNVNTeFlUT1BwaWIwVk94N3l2cmdnRlF2M0dzT01TSzExRzZUdmU3cGg1dGJ6WVdNRGJmeXhBdHV5QUYySUVZNTl4Zis4RzdvV0lXTWp5eEIxcDJmQXZSTzFsenJaTDVMcTAyOHMyNVpYc3VKaUxmdnUzRUhUVTluNlFYMHJUNFlQM1pqWDFFa1V4YkEiLCJtYWMiOiJjOWQ0MDgwZDA0MDNiNzQ0NzMzZWEwNzdlYzU2M2I3NGRhZjE1YzZhODgzNjY3Y2NlYTgzNjFhYmZkMjRjZDJmIiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 18:00:02 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InplZ2J6dU9wQlREa3F3blg1SGlneWc9PSIsInZhbHVlIjoiNHN0K0kwV2RBK3craUxZV0dWaWVPV0JPS2cvOGw5NUtvSWZaZlRndVNrMkR3R1krdW96NDd5V1ZyVjgrcDhLVDlaV0xkZUtrY0pnM3FPek5MRG5uN0RmVnNYSU1seVhFUjUwQTNPRFdKR0ZIcW5sWE90czgzZ1hGTVUxcS8xcURDT0cvQXppWm9iMmJWL3NtMUs4c01adXUxck9hMFhwOGFocFNxWitmdEEwWHVyMUJzeDlGN2F4a0MxM3U1dDFFN1NTTU0rSmdYQ0YrSVQrS1lkRmZKZ3Y3cDNMemdRblNvR1VPNUNqOWUwMzdmaEZScnZEdkc2Y1lTTVBiOVdGV1JDTmZGWjdjdkxDYVVRNDJzVjJjRGJVcUpvZjFCMXhHVEpQTjBpc2NSZ3A5YkRvS3JIbjdJNWtCSXYwSU1KMENWZ25CazZKcHNFUVlRWGJvTXNBSGFCTzZCZFJEdWhGMUw3UmZsSDRWUlFhNkxTL2xqRkFmOTVUY2huZ3dVa2tZMnJEWVVXajkvRzlGTURxS2J5aVdlZktZbUEzUkIrc0NJMWtNbkkzbmJLTytHUVZiSW1XK25aTC9zYWdObmIwdnUwRCszamdDOWQxMGNzejZCVTN0UzdsMU5VMlp4dFBBblNSMWxFaXdVQ2Y3N1o4dTI1Q0hpSGhEeEdtSGIxQ0giLCJtYWMiOiJmNDNmODIzMTI4NjBiMTBmMDg1MGIzY2ZhYzEwODI2NTFiYzUxNGJiMGI0YTc1OWUxNzFmOGZhOWIyYzliZjhhIiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 18:00:02 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlhiSzlDU01mV2lCcDFNL2ZleWpKUlE9PSIsInZhbHVlIjoid2NMMml3VFV3WTRyblFvNWRxYnZib3ZSSzBualZxai9hT2ZyakJmZHpjTnUvRHcrZkplNEtTdG9lNnhjNjkwWitQT1pKWUtPRkN5M3gwSVR2NVc2WkFhRUxMZnBCQ2t2aE9lUG5vcWRQZjZ0WHV6QkN6eXByYmp1dXYxSjZ1VElqWk9WUnVPcGg1dFFiL3RYREdVYUprQmJTN0JRM1UrT1F1ZWoyaStkZ0VVOGI4cGRHbFoyWTRmSzNiQkNEL1Eyd1JIYU5XTG5JR3ZndnluWGtjYVpTellVOXYyM1kwUElJT3BvRkNCOXFqNU1QNHhyU1p0bGVTMzFpTE1zUlJOR3lCT3djU2ZpYU05LzdRaXVtc0JINmxoTm5MazByd1diS1B3eFcxQ2Q2a3JlbFNBNjBZWExwZUYybGRYNDBuRTdxemlCSDdKR0R6Y0QyKzFQdkVmS3c2cjQrakNZTjg4NUtXVUJUQXhvWndtQzF3TTAvUUtoNVNTeFlUT1BwaWIwVk94N3l2cmdnRlF2M0dzT01TSzExRzZUdmU3cGg1dGJ6WVdNRGJmeXhBdHV5QUYySUVZNTl4Zis4RzdvV0lXTWp5eEIxcDJmQXZSTzFsenJaTDVMcTAyOHMyNVpYc3VKaUxmdnUzRUhUVTluNlFYMHJUNFlQM1pqWDFFa1V4YkEiLCJtYWMiOiJjOWQ0MDgwZDA0MDNiNzQ0NzMzZWEwNzdlYzU2M2I3NGRhZjE1YzZhODgzNjY3Y2NlYTgzNjFhYmZkMjRjZDJmIiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 18:00:02 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InplZ2J6dU9wQlREa3F3blg1SGlneWc9PSIsInZhbHVlIjoiNHN0K0kwV2RBK3craUxZV0dWaWVPV0JPS2cvOGw5NUtvSWZaZlRndVNrMkR3R1krdW96NDd5V1ZyVjgrcDhLVDlaV0xkZUtrY0pnM3FPek5MRG5uN0RmVnNYSU1seVhFUjUwQTNPRFdKR0ZIcW5sWE90czgzZ1hGTVUxcS8xcURDT0cvQXppWm9iMmJWL3NtMUs4c01adXUxck9hMFhwOGFocFNxWitmdEEwWHVyMUJzeDlGN2F4a0MxM3U1dDFFN1NTTU0rSmdYQ0YrSVQrS1lkRmZKZ3Y3cDNMemdRblNvR1VPNUNqOWUwMzdmaEZScnZEdkc2Y1lTTVBiOVdGV1JDTmZGWjdjdkxDYVVRNDJzVjJjRGJVcUpvZjFCMXhHVEpQTjBpc2NSZ3A5YkRvS3JIbjdJNWtCSXYwSU1KMENWZ25CazZKcHNFUVlRWGJvTXNBSGFCTzZCZFJEdWhGMUw3UmZsSDRWUlFhNkxTL2xqRkFmOTVUY2huZ3dVa2tZMnJEWVVXajkvRzlGTURxS2J5aVdlZktZbUEzUkIrc0NJMWtNbkkzbmJLTytHUVZiSW1XK25aTC9zYWdObmIwdnUwRCszamdDOWQxMGNzejZCVTN0UzdsMU5VMlp4dFBBblNSMWxFaXdVQ2Y3N1o4dTI1Q0hpSGhEeEdtSGIxQ0giLCJtYWMiOiJmNDNmODIzMTI4NjBiMTBmMDg1MGIzY2ZhYzEwODI2NTFiYzUxNGJiMGI0YTc1OWUxNzFmOGZhOWIyYzliZjhhIiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 18:00:02 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-56453320\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-425743967 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ET1WIQmDwH5DmIalEePkmJkdqregxR405rAMGD3B</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-425743967\", {\"maxDepth\":0})</script>\n"}}