{"__meta": {"id": "X7439186c9c42fed00959f5e08594856f", "datetime": "2025-06-26 16:01:58", "utime": **********.203291, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750953717.6922, "end": **********.203306, "duration": 0.511106014251709, "duration_str": "511ms", "measures": [{"label": "Booting", "start": 1750953717.6922, "relative_start": 0, "end": **********.081254, "relative_end": **********.081254, "duration": 0.3890540599822998, "duration_str": "389ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.081262, "relative_start": 0.38906216621398926, "end": **********.203308, "relative_end": 2.1457672119140625e-06, "duration": 0.12204599380493164, "duration_str": "122ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46003048, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.027949999999999996, "accumulated_duration_str": "27.95ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 17 limit 1", "type": "query", "params": [], "bindings": ["17"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.125335, "duration": 0.026449999999999998, "duration_str": "26.45ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 94.633}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.1603649, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 94.633, "width_percent": 2.576}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.1631038, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "kdmkjkqknb", "start_percent": 97.209, "width_percent": 2.791}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "jEiKRXRaeQw7kpqtZmXNWu3bpmEnaxKAnNI8mVhc", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "17"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-598424170 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-598424170\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1158829910 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1158829910\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-540052224 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">jEiKRXRaeQw7kpqtZmXNWu3bpmEnaxKAnNI8mVhc</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-540052224\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-100982400 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1842 characters\">_clck=1lqc4d8%7C2%7Cfx3%7C0%7C2003; _clsk=1h4itwx%7C1750953692214%7C9%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjY2VkN6S01VeDVtZElheFEyNVpVNkE9PSIsInZhbHVlIjoiWnZpZi9LRUxiQ3o2dmRBOUtYbktKbjQxSjNOUlhueDNDL1NVTTN6WkpCV0tPb2Q2NGxnRUNwZUVQMlFPaFg2UnNDUUYzME5zZmpJaUo5djdvTFZHUW00QndTNmxsd1hUVGk2Z3JiZHBKQW4reUJNbE5td3NPK1Brc1hDOGM2eEo0M0J5VGU1NWNoaWdWY0dXcWZpREYrMTQ5Zm1XTkpvdXc1REVMNW1FVXVDb1FVMXE1Zmt3SGlNbUNxcVphVndWNTdGelZVU2FCOURtaFZ5UTkwMjQ3bUtmNmprMk9QU1U5Q3BmVmp2NHBtM2o5ZWc5cG9kQzgrakhyWTdVREVKTHQ2NDIzYWpQNkxzbWUzajRWSjRpL09IcmUwYUpxTWZQb2ErUXIzTnFvUHhjUkZWVlhaUDBWNXA3ajJaTmg4V2xBTlFxNmJLUG5YTE9MQmlYMkVtQkFrdnZzK1ppN3dKZ0Z6ZU5rYlZmYUN4MWxudFFmVkVndkpaUjFZNWU1ZkVFSkJRWkFsc3NCVXBIdDlDRVMwU3AzYUptR0hqVjcrSWdWV1lteU9Ea1VYQmtnTE5pUFdXeCtUMW45LzlYWnhGUlpZTSsvdVN5QUxQV256bWR3Szdxejg5aGxKelc4S2pxaVhUSVVaSWpobWt6a1JpempzTm1VTFJZemRjSXlrT1giLCJtYWMiOiI0NTIyOTI3YzYxMWMyZjc3YzVhYjc4NzEyNzQ3Y2M4Y2UzNDYyYTkxYjZkODg3Yjc4N2EyZDMyNWE5YmJjY2ZmIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlhKNUl0WFpQSE5VcC90eUZPaE04WkE9PSIsInZhbHVlIjoiRFJFZllYNUdTelRjb1FCVlhZTlhpN2RjemF4T21BV1U4aFJyamRwbXhZd2lUQmo4aWNiQjVsZXBHaE9wR2lVN2g1RkxXdmt4dC84SE5EMy8rV3g2bjUzWUV6WVZkM1hSRVFFdUhZZklOY0w1SmxKUk1zM3AzUWlaeTJENmMvK2NHaGJLY3N1WGhoYUxUbGhKNTh1aUFOU05LaHFub0cyNytPN2RROXJha0gvMVVZalFEdk5xdXY3UW5seDUvR2p2Z1BrcG5oc0RVQTVrVkttKzcwWEMwWmYxMjE5aldSSkkrM3dXV3Vlb1pDUkhBdnlKZ2RoTWdMejhOenFEL1pSeVcxNFlTeWlqMkk0Y1FDNW5CcnJVQWhnSlZvc2RRUVVoeVhmZ3B2c1ZKbHRyaTVta2loK1NzeHlJK1ljZ011NFRlQi9LTjZsMWNqcEt1cXZGUFRiVnk5dnpkSTZsUitrN2ttM0F2QTRxNVJvSGNVNXJjcURwZnBGMDEydEkzTlFmQ01FRjlsSy9RazhOVExtMXVLc1krdUIxMjk2MWNhQW94K2RBTjNFTjMzcTl5KzNMZlJUUkVJdUFsM1d4VmhkWXp6dVhWWXFyemxQQmwxQzVaLzFvM2JuWnJkbnJvZ2ZTUW1pbHdtdWtpRzQ2ZWFOSDFLbmR2cGdZTlNWK2JDNUIiLCJtYWMiOiJmNzViZTYwNzk3YmY4YWY0YTJlYjk0NTQ2MDEyMDk0NmY5YjNkZmM5ODM0ZDdiNDM1NjVkNDNmNDdlZmJhZWFkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-100982400\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">jEiKRXRaeQw7kpqtZmXNWu3bpmEnaxKAnNI8mVhc</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PSbLK6QAWuUzu3JpIF2TDtYQ1L7TOqCcejFUVX3V</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-860440574 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 16:01:58 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlpyQ0lDb0pLN1dDRGZPK0w1dyt5K3c9PSIsInZhbHVlIjoidUhGOHpxNjlpTGZRRUFVK0EzZXloSUlRenZQUlpJYnBNRWRyaVBPdSt3cDdkMThlb2dYV2JEOHJMRlpWbmMveXkyOUlhL1hpSFBqSjZpaFIvcjJZNUJFanFzREtveGJUalhJNURpOVRxUjlLQ3lWUVJ2OGowN2FKRCsvZ2VOMEhEc3AvS3JaOHd1Ly9zZnVWUk5WNy9MZVRiaUtCNnFSRWdiSWFueHBobEtwNVlwSGtvMHJrdXk3cVVMTGV6eE9ja3VpQUxUZGhUa3grWEQzTUcwZWprYW95L2xBK1RzdDB5ZmhKdUhtdHdmTklKeTd1QkhOZitqNWFFdUFyeSswdjd4ZzJyaS80RUhKVUwzbWJqam14RFJXQXpkN3RjazdBSkFsdEUveUVpNVR6eEVxaUtvZGxjY1pIbzhvckhNZEVWOC8yejV0RDVXemtKOTJNc3FMRThLRnlRYkJSWEdFcDl3UFhKQVdYOFVUK2tiUnRYWWM0ZFFtNUQvaHVnY1NSeGNWSlhUZXBoZTVQYWh2MVVyQXZUL0ZCYzZoZWhkRUMxai8xVXUzUVNBbnpKZFplMThnTnViY3ZoUGptNUZHc2tOVnBhbldSR0RIbDdJUGlDaU9oTE5ndGc0VFArK0lPZ1NIYVFZZDBncFhlcUovUjdlems3RjJFUjNwQkp6WGgiLCJtYWMiOiI4MDVkYTNlZTAxYWJiMTdlM2Q4MjM3MmUwY2FjN2NmY2E0Y2IzMGZkZDY2OGRhNjQ1NmU4ZTAyMDIyMzFhYTZmIiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 18:01:58 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InozejJXemRkdDMzMjd0ZVdvUExkNGc9PSIsInZhbHVlIjoidWRtazE4S0E5UmxndW1CczVmWW9RQjhCclU4cHJKeE15ZjJTVWgyY2ZLbHNPaXE3bDY0WnpzL09tNzF6bDJHZU0rVGM2RGNZMmlhK0xHVFkrNWVGb3VSZDVHeHNwbTRoWmZaNmhETTFoRE0rZVZIRkdMY2kwcFNLNzVmYmY5Yk96dEVpUlpJYnUwSS9hUU9DVTVMYXc3VXJXS3RYTkRqdTN0eWpYcWxVNzEzZjBRUXZ3NzRCQkxKaTNDeW16Y3llZHJjWVNUWWU1VkN4aHp0SzN2NzF0NmRWRVhlbGhaZ2d4VnBmY1lTMTI5b28rVTEyUUNOMXNkYjdPcExkc0lla01rZG11YW1LZEQwb1R0RURXNE9aNW1XTkFFNmFUNGhoSHR2UEQ5alZaTW9zQ0hKUHZ6Z1ZrVVNSU0oxM3hySDlnTDZzZEFBWVJ5R1BQNHNlSEZuY3pwK1ppZWZpV3cvL1QwdUNCcDNHMnBveGV3dVV2U0xPWHc5ZVRFMU9RbWd6Z3VpSENkenZVMnJJc085bmlaV0l1QUxXRWgrN25sSGZtb05ZU2s0dk9ncStPeHJYZ1dadTBzb3JQSmZoMndTUVBEeEdWOFl5RGluenVxZkRkWE1vNVJrUTl0OUN4a3cyNmI0TDZCZURhakN6MzQ4VENKYlQ2eklXcFRFd3l5cUIiLCJtYWMiOiJmMzBjNzYyOGI3YzI0ZWVlNTI0ZTkxNjcxNDQwOGM4ZDg1YjRmMGFkNTI0ZmQzYjM1ZGE3MWI1ZmI2M2QzZWRjIiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 18:01:58 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlpyQ0lDb0pLN1dDRGZPK0w1dyt5K3c9PSIsInZhbHVlIjoidUhGOHpxNjlpTGZRRUFVK0EzZXloSUlRenZQUlpJYnBNRWRyaVBPdSt3cDdkMThlb2dYV2JEOHJMRlpWbmMveXkyOUlhL1hpSFBqSjZpaFIvcjJZNUJFanFzREtveGJUalhJNURpOVRxUjlLQ3lWUVJ2OGowN2FKRCsvZ2VOMEhEc3AvS3JaOHd1Ly9zZnVWUk5WNy9MZVRiaUtCNnFSRWdiSWFueHBobEtwNVlwSGtvMHJrdXk3cVVMTGV6eE9ja3VpQUxUZGhUa3grWEQzTUcwZWprYW95L2xBK1RzdDB5ZmhKdUhtdHdmTklKeTd1QkhOZitqNWFFdUFyeSswdjd4ZzJyaS80RUhKVUwzbWJqam14RFJXQXpkN3RjazdBSkFsdEUveUVpNVR6eEVxaUtvZGxjY1pIbzhvckhNZEVWOC8yejV0RDVXemtKOTJNc3FMRThLRnlRYkJSWEdFcDl3UFhKQVdYOFVUK2tiUnRYWWM0ZFFtNUQvaHVnY1NSeGNWSlhUZXBoZTVQYWh2MVVyQXZUL0ZCYzZoZWhkRUMxai8xVXUzUVNBbnpKZFplMThnTnViY3ZoUGptNUZHc2tOVnBhbldSR0RIbDdJUGlDaU9oTE5ndGc0VFArK0lPZ1NIYVFZZDBncFhlcUovUjdlems3RjJFUjNwQkp6WGgiLCJtYWMiOiI4MDVkYTNlZTAxYWJiMTdlM2Q4MjM3MmUwY2FjN2NmY2E0Y2IzMGZkZDY2OGRhNjQ1NmU4ZTAyMDIyMzFhYTZmIiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 18:01:58 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InozejJXemRkdDMzMjd0ZVdvUExkNGc9PSIsInZhbHVlIjoidWRtazE4S0E5UmxndW1CczVmWW9RQjhCclU4cHJKeE15ZjJTVWgyY2ZLbHNPaXE3bDY0WnpzL09tNzF6bDJHZU0rVGM2RGNZMmlhK0xHVFkrNWVGb3VSZDVHeHNwbTRoWmZaNmhETTFoRE0rZVZIRkdMY2kwcFNLNzVmYmY5Yk96dEVpUlpJYnUwSS9hUU9DVTVMYXc3VXJXS3RYTkRqdTN0eWpYcWxVNzEzZjBRUXZ3NzRCQkxKaTNDeW16Y3llZHJjWVNUWWU1VkN4aHp0SzN2NzF0NmRWRVhlbGhaZ2d4VnBmY1lTMTI5b28rVTEyUUNOMXNkYjdPcExkc0lla01rZG11YW1LZEQwb1R0RURXNE9aNW1XTkFFNmFUNGhoSHR2UEQ5alZaTW9zQ0hKUHZ6Z1ZrVVNSU0oxM3hySDlnTDZzZEFBWVJ5R1BQNHNlSEZuY3pwK1ppZWZpV3cvL1QwdUNCcDNHMnBveGV3dVV2U0xPWHc5ZVRFMU9RbWd6Z3VpSENkenZVMnJJc085bmlaV0l1QUxXRWgrN25sSGZtb05ZU2s0dk9ncStPeHJYZ1dadTBzb3JQSmZoMndTUVBEeEdWOFl5RGluenVxZkRkWE1vNVJrUTl0OUN4a3cyNmI0TDZCZURhakN6MzQ4VENKYlQ2eklXcFRFd3l5cUIiLCJtYWMiOiJmMzBjNzYyOGI3YzI0ZWVlNTI0ZTkxNjcxNDQwOGM4ZDg1YjRmMGFkNTI0ZmQzYjM1ZGE3MWI1ZmI2M2QzZWRjIiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 18:01:58 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-860440574\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-477942614 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">jEiKRXRaeQw7kpqtZmXNWu3bpmEnaxKAnNI8mVhc</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>17</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-477942614\", {\"maxDepth\":0})</script>\n"}}