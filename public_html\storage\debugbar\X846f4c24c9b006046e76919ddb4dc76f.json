{"__meta": {"id": "X846f4c24c9b006046e76919ddb4dc76f", "datetime": "2025-06-26 16:04:44", "utime": **********.743748, "method": "POST", "uri": "/warehouse-empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.309166, "end": **********.743763, "duration": 0.4345970153808594, "duration_str": "435ms", "measures": [{"label": "Booting", "start": **********.309166, "relative_start": 0, "end": **********.693342, "relative_end": **********.693342, "duration": 0.38417601585388184, "duration_str": "384ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.693351, "relative_start": 0.3841850757598877, "end": **********.743765, "relative_end": 2.1457672119140625e-06, "duration": 0.050414085388183594, "duration_str": "50.41ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45880368, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST warehouse-empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@warehouseemptyCart", "namespace": null, "prefix": "", "where": [], "as": "warehouse-empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1627\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1627-1637</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00292, "accumulated_duration_str": "2.92ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 17 limit 1", "type": "query", "params": [], "bindings": ["17"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.724403, "duration": 0.00214, "duration_str": "2.14ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 73.288}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.735642, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 73.288, "width_percent": 26.712}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PKqZteC3IabV2rdCxsXBMLnTb72fzH4qdR2KdQSn", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "17"}, "request": {"path_info": "/warehouse-empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-1596999802 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1596999802\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1747014662 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1747014662\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-390245341 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-390245341\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-95785763 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PKqZteC3IabV2rdCxsXBMLnTb72fzH4qdR2KdQSn</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1bowxa%7C2%7Cfx3%7C0%7C1999; _clsk=1osc4j2%7C1750953875247%7C2%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Iit1eEluUENWa2lEMDdmbXZQdkNpNEE9PSIsInZhbHVlIjoiMGRkV1ltdG5RcGNSeGo0YWRRamdOTDBLTHJ2bnpIdzdPVmlpMWZVN0VhM255ZklRZXFnWEFReTF0bnlFZUhTZzlhN01ib3VVU0pLS3ZESGlFN285Z0w1bWM4U2t4aEpIMmVoM253dG1reVI1bjI5aS9nU0k2RUNrNU9MQSs5SU1HeXV2Q2dqTm9kK2wyUCtJSGFiSitaUXBFc1ZGMWkrNDh4VkFhVXp3Q2xNeTJoVTE1ajl3cFZKeHJlTFFieURrbW1aeFYrUkpRVHJvMWhBc1lSREVGTkVXRURucU9OY1RSUzhDbmpxU1pDcFAxVDFJWVp2alB2d25NTTFKYm5kZ0J5US91cHE4N1FiSjd0OU5UaGFpbGM2MmRUODd6cVdObXB0UW8zS05aYnhSNEQxSUdQR1IvUlBldXIySitCYnZpUzRQYzkvY2ZrbE5uRXgrR0ZDSWRJckYzc1dIZTVGNlNvVllMb2h4UmpTaHkwL1I4VmF2TnhRUkl5QklWdlJwTFlTYWduTVFSRG1INWU1Nm0zcGVadGR0aHVyS0kxVkEyRVNTK0pscVFDUmE0bkZ5dTlvU1JKWXV0MFk5dnVZRFNkYUpxR2lLOGN3L0ZqSGVuQk1aSmZTRlZMWG1CeEROK1VZNFpTTFdtb3pRSitSNHh3cXUvelFreU4wVi8xSWYiLCJtYWMiOiI4ZjY0MGQxNDEzMjk5NjlhMDFjYTE1MDA5ODJlMjQwODQwZTUwNzNjM2JjYjFjNjg0ZGVlYmI1ZmExOWMyMTcxIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ino4TWlac2lscTlDWndQZVVpZ2lkNlE9PSIsInZhbHVlIjoiUUhVZTh6Tk5ScDhxN2tXdW82YTlFeEpWMWpINGNjTFVSOXRlVTN1UUZqNEpSWTZFUllOVVlla3FycUYrcTRpUFZtaW92aXdhZ2pOTTdLNUxMTjJZT2tjY0tmZ2M1YndicGlWQWRneW4waU03Q05SK3ZWc01mRXVjSzhtT3hoSExGQk9xMWRQV2VzSjVFc0pTcERtYkhqOEZLNWJrSzRiYXhCaEhFc0hUL0QzUUJ3R09laUFnV21rNXdpVDk2QmZwZm53b3RSWldBdzN2RmYyU2ZLTWNlSHdqNlZ5NXFia1NDMzg3d2YxN3NGM1czMmlxSHEwZHpFYWdvcll6VjhCSTJXN0FSWE1VVGRXaDg0ejVMRzdFVzR5UEpyMFhGQUZXTG0zWWplME5MYkhtQnJaYUU3QUR0enBWQWVOR2d4ZnhCSGhUbTRUYzRUVVlPOWRSR2VGbWM5ZkZBYXBCNUo2QTdLSjlPcDhHelhvWnFoczFFN3dxMTdGbkIvWDVLUGRBeFVvamNPQW9hY09JQ240aDJjQmtoNVN2QWZuSDNCR0tvbVlJTnhFRnVpSkFFeEJIUTNGblRHakw0a1pxR3lVU3FRVVVLMjJweEpZMnQxSjBCR0cwY09DUDc0ZFBSQk5kTWNHT1hYZkhsbWtwekNHb01mVE00cXh6RmhOUDN3Y08iLCJtYWMiOiIzMDY3ZmExZjcxNWM1N2Y3NGU3YjcxOTcyMWMyNGU3MmY0ODlkNGFjYjNlMWUxODEyYTNkOTU5NmVmZDg4MmUyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-95785763\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1656975128 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PKqZteC3IabV2rdCxsXBMLnTb72fzH4qdR2KdQSn</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GUUMfjgJKgBqP5oaDcRP4Rfu0FM4k6V9zWwhkyoG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1656975128\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1353053962 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 16:04:44 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ii9nSWgxWEhLS0lYdTI2bXFETk9KS1E9PSIsInZhbHVlIjoia3I2SGhQbXNrY0syaTl0MGpyc3hFSVJhTjRFZk04N2JBY253TjgxMjFjdXBEN01XUTRZZ1BKc1d4WXgzUm1sSGpLdWFxVkpmWUczSzdQQllsSVBSUkNRL0VYai90NnoyQkFRd0lFYmhKV0dkcWMwMkoxazZCeHNRaFg3Y1krMWVDQkNGRWRkRTZBUUlRaExRRE9SSFFUVXEvYXUrYnhwK1M3ZXR3aVpTVExlMnlYdDkvak0za2RSbW9LS3dnWFdHRkFQN3lmaG5XdzZKSGxyQkZaTzFzTTZrZ0U3OXM3UXlKYkhzRjE2b3I5VXVtbEJkWUFpZjAxZTd2YlF2WjA2OTdUaEVsRWFCVFd6eTFDbkhENmwvSzV1V1lRZjVOZFU5VlBpcG1ScWcxMFh4d3JneFFCZ0dWNXVOZnVSQ2lhSDlEaDh0QmhlejJWc3MxUC82QjE2TVFJNUM5RXExMkhhTUNqcUJnTTd4eFZ2NnpRRVJGL2NuOFEvMUVwc1JKeDV5OXQ5ekczTTkyWFByMnU2ZzIydlBxWjF5bUZLanFGUVNrMlA2UnA2c2hCaWZtSkJVWmE3QlFQT1lCVFVaclRxd09EY0docmpVT3pVRUVKV0MwZTd1bjlQSUJFUVdZUW1NV09pNFp4UU5Wb09mOHNPRlBjUGpOb0FiSUFOaW9CSGkiLCJtYWMiOiI1MThhNmU1ZTdjYmQwYzg2ZjcwMDE3N2M2MjA5YjY0YTNkNGNjMDcyOTllYjQ2MDM0Mjc4ZTg5YmIzNDEwZWM2IiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 18:04:44 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlRmci9icXZwYVd2ZVpET2NQb1ZVN1E9PSIsInZhbHVlIjoiTDQ3YWhRdXJnZzJDaWE4RDJYZ0x0cndEN3lBeEJtcWtDVVIxYnZJOFo2MjRCajVhNm4xUVpuczN6c21Eb0sxZDI4enNHOGhZYXBGcCtzaDV5cWs2TlRkMWNyQXpMNkNZYlBha3BRdFlha2JEbWwweUN3SjVNSi9KdG4yQWJ5bitJNUNOOXFyb282TE9iSzNOYVdqbDJENlV5K2I2R0IrbVYvUDBva3U1Y0lBbXhHTUM5WkZ3VEdEQ1N6VHBwUDZ6VEZhSENBbmhYWDdwZDF1OXlJS1RrQjlBK3o0cUd6c0N1U1J5OUN1ZEFmREh1N041cmFVcUo1d3Z1YVNaZkp0dEY5UCtiYmZkUS9Lb1NhR05KTzBYWk4xM080SXZDMTduQXBaUWV0K0FKOEw3bWVTLzlYWG53emlpVGlIY3daazExWkNCNEFCUTNwUDdNM1hmK0RVY2d0dXRRem9VbHRSRlJlNmthbERadmxFa1dWM3dmTlA3MklZMkZtMGVPdVBGVEpRL2plTkpIT1BIbm5rckNrU0FSSC9IbWxMUGpFd1laV0t5Y0hIM1VScDJoNElqMmhBYzNvczErNE1YM05oTUVWRjRIVGRtSXdLVDJhVFR1bjhzREVwRms4SmJ1MHdSYzY5eTdTSGpDSHBOVVAxRFpQN09ncmNvd1lqWXN4M0UiLCJtYWMiOiI5MTRkYWY1OWE2ZDVmNWRjZDJmYjMyMTU3NTVmNDJiYTA5MWY5N2U5MzZjOGMzOGY4MjgxNDYxM2VjNWFmYWVkIiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 18:04:44 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ii9nSWgxWEhLS0lYdTI2bXFETk9KS1E9PSIsInZhbHVlIjoia3I2SGhQbXNrY0syaTl0MGpyc3hFSVJhTjRFZk04N2JBY253TjgxMjFjdXBEN01XUTRZZ1BKc1d4WXgzUm1sSGpLdWFxVkpmWUczSzdQQllsSVBSUkNRL0VYai90NnoyQkFRd0lFYmhKV0dkcWMwMkoxazZCeHNRaFg3Y1krMWVDQkNGRWRkRTZBUUlRaExRRE9SSFFUVXEvYXUrYnhwK1M3ZXR3aVpTVExlMnlYdDkvak0za2RSbW9LS3dnWFdHRkFQN3lmaG5XdzZKSGxyQkZaTzFzTTZrZ0U3OXM3UXlKYkhzRjE2b3I5VXVtbEJkWUFpZjAxZTd2YlF2WjA2OTdUaEVsRWFCVFd6eTFDbkhENmwvSzV1V1lRZjVOZFU5VlBpcG1ScWcxMFh4d3JneFFCZ0dWNXVOZnVSQ2lhSDlEaDh0QmhlejJWc3MxUC82QjE2TVFJNUM5RXExMkhhTUNqcUJnTTd4eFZ2NnpRRVJGL2NuOFEvMUVwc1JKeDV5OXQ5ekczTTkyWFByMnU2ZzIydlBxWjF5bUZLanFGUVNrMlA2UnA2c2hCaWZtSkJVWmE3QlFQT1lCVFVaclRxd09EY0docmpVT3pVRUVKV0MwZTd1bjlQSUJFUVdZUW1NV09pNFp4UU5Wb09mOHNPRlBjUGpOb0FiSUFOaW9CSGkiLCJtYWMiOiI1MThhNmU1ZTdjYmQwYzg2ZjcwMDE3N2M2MjA5YjY0YTNkNGNjMDcyOTllYjQ2MDM0Mjc4ZTg5YmIzNDEwZWM2IiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 18:04:44 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlRmci9icXZwYVd2ZVpET2NQb1ZVN1E9PSIsInZhbHVlIjoiTDQ3YWhRdXJnZzJDaWE4RDJYZ0x0cndEN3lBeEJtcWtDVVIxYnZJOFo2MjRCajVhNm4xUVpuczN6c21Eb0sxZDI4enNHOGhZYXBGcCtzaDV5cWs2TlRkMWNyQXpMNkNZYlBha3BRdFlha2JEbWwweUN3SjVNSi9KdG4yQWJ5bitJNUNOOXFyb282TE9iSzNOYVdqbDJENlV5K2I2R0IrbVYvUDBva3U1Y0lBbXhHTUM5WkZ3VEdEQ1N6VHBwUDZ6VEZhSENBbmhYWDdwZDF1OXlJS1RrQjlBK3o0cUd6c0N1U1J5OUN1ZEFmREh1N041cmFVcUo1d3Z1YVNaZkp0dEY5UCtiYmZkUS9Lb1NhR05KTzBYWk4xM080SXZDMTduQXBaUWV0K0FKOEw3bWVTLzlYWG53emlpVGlIY3daazExWkNCNEFCUTNwUDdNM1hmK0RVY2d0dXRRem9VbHRSRlJlNmthbERadmxFa1dWM3dmTlA3MklZMkZtMGVPdVBGVEpRL2plTkpIT1BIbm5rckNrU0FSSC9IbWxMUGpFd1laV0t5Y0hIM1VScDJoNElqMmhBYzNvczErNE1YM05oTUVWRjRIVGRtSXdLVDJhVFR1bjhzREVwRms4SmJ1MHdSYzY5eTdTSGpDSHBOVVAxRFpQN09ncmNvd1lqWXN4M0UiLCJtYWMiOiI5MTRkYWY1OWE2ZDVmNWRjZDJmYjMyMTU3NTVmNDJiYTA5MWY5N2U5MzZjOGMzOGY4MjgxNDYxM2VjNWFmYWVkIiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 18:04:44 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1353053962\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2116663098 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PKqZteC3IabV2rdCxsXBMLnTb72fzH4qdR2KdQSn</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>17</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2116663098\", {\"maxDepth\":0})</script>\n"}}