{"__meta": {"id": "X359368c91c816cd76dca7c84be14215c", "datetime": "2025-06-26 16:00:01", "utime": **********.337322, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750953600.897409, "end": **********.337336, "duration": 0.4399271011352539, "duration_str": "440ms", "measures": [{"label": "Booting", "start": 1750953600.897409, "relative_start": 0, "end": **********.269736, "relative_end": **********.269736, "duration": 0.3723270893096924, "duration_str": "372ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.269745, "relative_start": 0.37233614921569824, "end": **********.337338, "relative_end": 1.9073486328125e-06, "duration": 0.06759285926818848, "duration_str": "67.59ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45669096, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.00363, "accumulated_duration_str": "3.63ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.2968972, "duration": 0.00206, "duration_str": "2.06ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 56.749}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.311928, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 56.749, "width_percent": 14.05}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.3236818, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "kdmkjkqknb", "start_percent": 70.799, "width_percent": 16.253}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.329541, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 87.052, "width_percent": 12.948}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "ET1WIQmDwH5DmIalEePkmJkdqregxR405rAMGD3B", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-812213663 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-812213663\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1097794415 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1097794415\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-640478663 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ET1WIQmDwH5DmIalEePkmJkdqregxR405rAMGD3B</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-640478663\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1842 characters\">_clck=1lqc4d8%7C2%7Cfx3%7C0%7C2003; _clsk=1h4itwx%7C1750953587305%7C3%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ii83TzJOTzVXVk9wblQ5emZYNmJRVkE9PSIsInZhbHVlIjoidVR3STY2RXdKaWIzUC82YUlRWFRROHg2UHgvSHFvMzBtcUpNaFFxUGVBSzRUVkQ1SjdXK283UUNSSk5kd3d6MzdnQVF1MHE1bUZNY09qSm11QStwZ2h5aFdIMDV4T1gzbFdBMjdRSDRmVWRHRmxzaEJTbTQ1d0tsZzdjOXZPeEhsL05aYVFidnIvMmRDcUJPOGtHNmw5V0w5WGJOQmx5VzBBbzdTNUxacDNXZDVZYnUzb2pFY2dSWkE3RDVNYXhSVlRXV3hPNldsSUtpRmJtYnNJMElTVitCRUxNMHVVbFZIQWhhU1ZvZ29qOVlpbGZyRnlQallsMWpQQWVyWFFLSUYvQ1pLWUdEWmRHT0RZYUhHUDA5bGVLaElvR3RuaGlnbjAreHI4aGNiRzdYeXVJTEJFSXFPWUdsS2tPZzc0SmFkUXpiRGtkTmFXU0lJTTNzd3ArOUVKa1Yyb2lzM05KWWlwL0k3TG9Sd1hWYXdqbU0wNHJBREg5VTY4MmZhM3hrdTRMa3IrOC9Oamt5RWx1Qzd1TzNXMHVYTTN6dXFpZDB1eGpPV2NOOVRDZXZaZFFWdXRYWndDVm5wck5MN2NFaHVZbThkUUVKbFBvaVFwOEZNelVVVlczeXlPblpZemwvVnJQRHQ0UWpwMjVFd1BDNjdZOCtRdzVyZjFBM3NYblMiLCJtYWMiOiIwZTZiMjdlMWY2NjUzNjNkYzMxODg1ZTkwZTU2ZWIyYjgwMjVkY2VmNzRjMGU1YWQ4MGEyYjZhMWM3Zjg5MjUwIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImNNczEvVm83VWdPSERHRm4wZ0ljR3c9PSIsInZhbHVlIjoiN011WmFNU2E2Z3ZZS2hUOUhaRkY4TGZqWVRnMVZaSTM0L3lzV2IxUXgvTFN2RXRtQ1o4My9KS0doVStCazZvMCs5Smk0ODNVeFNXZHF2UzNRcVo3YlZ4VkNOSUhRRU1iUWMwcFlkcUZYK2VrQ0lIYnhJSWMxaldOaU5BSEZqQm9NOS9vdHJCSmlYNnBVd2NsY3Z6Sm9PVTl4WTU3aHk4c0x6VFFKR1hneVJhbmJ5alVmL0dhUXNJWWRjdEhNT2FYNGxFTG1jUHRTOTRhL2hCZzFmUmFoeTJZOHJ3OXl3cjFmYm5SZnRKbVlpVW1aV3FSbDdsYmx1VWNVOWRaVlZ2bUcyVUpkSU9OcUZSc1lxaG1IUHlzRjJRTTA2MnJmVUpjQ0xwVXV6TmJxd2pJc1QrUzdIVDhWZHZxM3pvbjdaTVJ5ek9JRXdlbzBVbW1pemJEWmt4TlM1WnZMZy9NdEp4WWczdm02dk5QdTFMaHNtbEh0SWhUNHhjYWpxVi9PclNXOWJSMlFZN3paYjh5dURTUFBDSFBJdThOczdWVUNVZy9oZk9GMlBnT3NVRG5KM3Z6NW50aXNiTGZSSHBnVkthYVMwWW1Takkxb2piZEp2MnRWenUrUHRWa1hpMVBHUzk0blBGbG42TlY2NThNK3dHODVIRzkyem11WnlNU2xDV1MiLCJtYWMiOiJkZWFjZGVlMzMyMTNiN2E1M2Y2N2JmNTg1MjgzZjA5MzY5MTM5MTY2M2I0YjcxYmY3Y2ZkNDk1YTFjNDY2ZmE4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-94425142 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ET1WIQmDwH5DmIalEePkmJkdqregxR405rAMGD3B</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OKKbyGk1l1oGFNUe7gxioQDH0z4ATppFVbVTtLek</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-94425142\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1949057007 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 16:00:01 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ilg5SVdTejBvQUJYN0pqRmszbXMxQ2c9PSIsInZhbHVlIjoiTWd4ZkI0dW5CMEdNZWJSNUJXNEE3aWZwQmFGekkvT2lncWRPaXc5RHg1TERWMjREUVZEd0pxVDFrb3BUdVY3U0syRDlIZnBRbjBRM0x1c0o5MDl6cVRuaWhrdjBhN29CcUk4Y2kzaWFLcjhGdEJNZ0ZrZFdBdTdXNHl1RFhVV2liYnJUVmZFL0U2VlVRRjdybnRGd0tlR2lQYzVwN2c4SUdRbkxUMjlYYnVKTlZXNU1lS2kwUHcxZUZVQUNyNS83MFZGMHRJREcrMlVqYms5TzdoQkc3dmN1TmlUd2p0UFhjS1ZaRnFUaWhDVGtkMDFIVHN6bGd2RERyb2wybTBsdnduYW9yeHN5bHFrUkxMYTMwU1hxbXU3N0N2UkNvMG5OWDVZREtKV3I4ZUlCUUNnRWxxZG1hOVIzMnhHT1k2VzRJZDRLOVp5aUx4SGVld1pnRXUzQkhVTWs4K1NreW80NjdNbG5Kb0k3UXQxVzhiSlhVRlNUa3l1eUd2UXZTY0hIczE3b0VBOXh3TTJpandNa09aYytlVWdncTlsWExYTkpSSktGdDFWSEhlNXpPV3Zsb0c3ZFVKVG9pSFI3bE1pR3ZJS2krYXFUOVhzV2g5VVp0bGlVOW9zanZGdWlxOVQvcTZZQks1MjhrRVdnU1dvS2pUWGZoeDVTcm1IcVN1VTUiLCJtYWMiOiI5OTBjMjM0YjIzNmIyNTQ4N2I0NDc1OTU0NzEyNTcwNjE0MjllNTAyZGYzZmYyNzI2MzY4YjhlM2FmMjEwN2JjIiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 18:00:01 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjJQM3RFSG1NcktqMlVucXNKZjRIS2c9PSIsInZhbHVlIjoiS3hUKzZCYUdJRkJ4bDhZVkhMSUlCUUQ4OVRKK1VhUW02YVI0M2phUWhyRU1PbW1PRkNRR1kyS2J6b1lpRHlPNDZCa0tzY1dacGhWRzhwVTJaUll6R0I2U29IQjJJdnhxVW82QUhxRmhPVmV0eG5KcDl1c1RiTFhFSWVCSEF1MnQxZFptN1dUako3S1RyOEdLTWRZQm4vSXBESkpEUVNuL21tNmZDWi9qV0dWZGVZUnAzeEM1SWRXaXR0Y3l2SnZudUNlMVZnODdWT2V2dlRWODl6cWxCZjZuQ29qK04xeDBkZGNqQzNnbWNJMDNhMUNNazlzT0RaSE83NTFnQzh3WVZwSUo3MmkrOTJLVmRhRFBVTm81MjMybUlZbW10TVBiM2Q0ODFxZmRvVndJZWZCVnVZRnVHMG1uUlpaR3lCZ0p1SGxLZDBQWStrQ0hvNENKaS84ZnJVZEhlcDU4Mm54OXB1czRjNFdocVdSbHN1Y1lWM2FqVTcxUUxxUVhQdVRUaGc1M21uNmhwaXNxRTcrdFRlQWIzcFQzRGE4b3Q4MmJSSWNHZ0lqQ0tXbmJFQmFYaC9vOVg3STlmcHZpWlFMYmNKWkxpNU9mUXJ5eTdwUXJPdlExTStDbTVXMTJhbmZoQXk1V2xGVTRvK2FHRmVKWExacTBRejg0MDN0UzNiSHAiLCJtYWMiOiJiY2FiOTNhYWZlNjAwYWMyYTIwYTY1YzM4NTA5ZDRhNmQzMGNiODIxMDQ1Zjg3Nzc0ZTFlYTI2N2U3NzQ4MDZhIiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 18:00:01 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ilg5SVdTejBvQUJYN0pqRmszbXMxQ2c9PSIsInZhbHVlIjoiTWd4ZkI0dW5CMEdNZWJSNUJXNEE3aWZwQmFGekkvT2lncWRPaXc5RHg1TERWMjREUVZEd0pxVDFrb3BUdVY3U0syRDlIZnBRbjBRM0x1c0o5MDl6cVRuaWhrdjBhN29CcUk4Y2kzaWFLcjhGdEJNZ0ZrZFdBdTdXNHl1RFhVV2liYnJUVmZFL0U2VlVRRjdybnRGd0tlR2lQYzVwN2c4SUdRbkxUMjlYYnVKTlZXNU1lS2kwUHcxZUZVQUNyNS83MFZGMHRJREcrMlVqYms5TzdoQkc3dmN1TmlUd2p0UFhjS1ZaRnFUaWhDVGtkMDFIVHN6bGd2RERyb2wybTBsdnduYW9yeHN5bHFrUkxMYTMwU1hxbXU3N0N2UkNvMG5OWDVZREtKV3I4ZUlCUUNnRWxxZG1hOVIzMnhHT1k2VzRJZDRLOVp5aUx4SGVld1pnRXUzQkhVTWs4K1NreW80NjdNbG5Kb0k3UXQxVzhiSlhVRlNUa3l1eUd2UXZTY0hIczE3b0VBOXh3TTJpandNa09aYytlVWdncTlsWExYTkpSSktGdDFWSEhlNXpPV3Zsb0c3ZFVKVG9pSFI3bE1pR3ZJS2krYXFUOVhzV2g5VVp0bGlVOW9zanZGdWlxOVQvcTZZQks1MjhrRVdnU1dvS2pUWGZoeDVTcm1IcVN1VTUiLCJtYWMiOiI5OTBjMjM0YjIzNmIyNTQ4N2I0NDc1OTU0NzEyNTcwNjE0MjllNTAyZGYzZmYyNzI2MzY4YjhlM2FmMjEwN2JjIiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 18:00:01 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjJQM3RFSG1NcktqMlVucXNKZjRIS2c9PSIsInZhbHVlIjoiS3hUKzZCYUdJRkJ4bDhZVkhMSUlCUUQ4OVRKK1VhUW02YVI0M2phUWhyRU1PbW1PRkNRR1kyS2J6b1lpRHlPNDZCa0tzY1dacGhWRzhwVTJaUll6R0I2U29IQjJJdnhxVW82QUhxRmhPVmV0eG5KcDl1c1RiTFhFSWVCSEF1MnQxZFptN1dUako3S1RyOEdLTWRZQm4vSXBESkpEUVNuL21tNmZDWi9qV0dWZGVZUnAzeEM1SWRXaXR0Y3l2SnZudUNlMVZnODdWT2V2dlRWODl6cWxCZjZuQ29qK04xeDBkZGNqQzNnbWNJMDNhMUNNazlzT0RaSE83NTFnQzh3WVZwSUo3MmkrOTJLVmRhRFBVTm81MjMybUlZbW10TVBiM2Q0ODFxZmRvVndJZWZCVnVZRnVHMG1uUlpaR3lCZ0p1SGxLZDBQWStrQ0hvNENKaS84ZnJVZEhlcDU4Mm54OXB1czRjNFdocVdSbHN1Y1lWM2FqVTcxUUxxUVhQdVRUaGc1M21uNmhwaXNxRTcrdFRlQWIzcFQzRGE4b3Q4MmJSSWNHZ0lqQ0tXbmJFQmFYaC9vOVg3STlmcHZpWlFMYmNKWkxpNU9mUXJ5eTdwUXJPdlExTStDbTVXMTJhbmZoQXk1V2xGVTRvK2FHRmVKWExacTBRejg0MDN0UzNiSHAiLCJtYWMiOiJiY2FiOTNhYWZlNjAwYWMyYTIwYTY1YzM4NTA5ZDRhNmQzMGNiODIxMDQ1Zjg3Nzc0ZTFlYTI2N2U3NzQ4MDZhIiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 18:00:01 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1949057007\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1788402497 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ET1WIQmDwH5DmIalEePkmJkdqregxR405rAMGD3B</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1788402497\", {\"maxDepth\":0})</script>\n"}}