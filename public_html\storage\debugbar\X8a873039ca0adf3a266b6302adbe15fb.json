{"__meta": {"id": "X8a873039ca0adf3a266b6302adbe15fb", "datetime": "2025-06-26 16:02:05", "utime": **********.691299, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.234808, "end": **********.691315, "duration": 0.45650696754455566, "duration_str": "457ms", "measures": [{"label": "Booting", "start": **********.234808, "relative_start": 0, "end": **********.629554, "relative_end": **********.629554, "duration": 0.3947460651397705, "duration_str": "395ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.629569, "relative_start": 0.3947610855102539, "end": **********.691317, "relative_end": 2.1457672119140625e-06, "duration": 0.06174802780151367, "duration_str": "61.75ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46003048, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01316, "accumulated_duration_str": "13.16ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 17 limit 1", "type": "query", "params": [], "bindings": ["17"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.6587558, "duration": 0.012199999999999999, "duration_str": "12.2ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 92.705}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.680459, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 92.705, "width_percent": 4.407}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.6829069, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "kdmkjkqknb", "start_percent": 97.112, "width_percent": 2.888}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "jEiKRXRaeQw7kpqtZmXNWu3bpmEnaxKAnNI8mVhc", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "17"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-746278257 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-746278257\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1340850676 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1340850676\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-47604553 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">jEiKRXRaeQw7kpqtZmXNWu3bpmEnaxKAnNI8mVhc</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-47604553\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1285458913 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1843 characters\">_clck=1lqc4d8%7C2%7Cfx3%7C0%7C2003; _clsk=1h4itwx%7C1750953722878%7C11%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik1KOTJNeEkvNnNOcEl4cEN4eEtCZXc9PSIsInZhbHVlIjoiTlJLUEhXY3BWa0NLZVVpc3VrSDUxVm5OWlFYaG5GdnJmSmI3WllFbEJLM2k1UXhsa3RGVXF1SVdUeVlIWnFNWGRRQnQ4TW9DRjFzMkdza2M5UzJMVW13MjV5TzloelhTSmlneERCWFI1VERsK21KWTY5K3JxUVR0MmhzOHloYndVL1dtWWlLaVg0N3VZTXBNVUlmaHUxdWFpamQzeEw3M2xTS0lkQ20ydHkwZHdLTXRSM1BFcjAvdFRDcjN6RzFjUDlhUzBNZE5FVzdDc2RXd2ZLc1FobitZekdCTForUDZBSVV2cHA3VUxwckwrbXd3RXlQdE1qcjVqeUpHT21DNHFWeHVnZGRyQldQa3U4ZVR3Z2hnNzdNV0RmR1FVYVIveFozUmxScWV4ZGxXUWNvd3h5OU1lOUZCWXJ6bHFFZVhZZUVKRStYc1MxL1RRaGNUQWM2WDF5dEdDK0UwbzRHSGZ5amdWcmxsUVpqY3g2TjJaaGZFZjJ2dTRwUzcxMVNtOXhiYnRDeThQS1A0b3RsSVFDeU50UDBhOUhtZ1lCOUtxMlRIN3VwQ2pHNG92Zkh6eGpPbXJFMmJvbG03c1RYWTdPL3BtT3RuR0tFQnRjUWdqRDhCdlRxL3JSMVdtQzRjdUFFcFRQSnM2OWF6U0NKMGg0OXAzUCtHb2VPdEtvdEoiLCJtYWMiOiI4ODE4OGI2NmMwMTY5Y2RkM2ZiMTY0ZWU2OWZhYWViY2MxZWNlYzdkODRiNTI4MzgwZjAwZDdlYWI1NWRhMGI2IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjBXcno3WGxSL1d3Q0NpdzRiU1NTR2c9PSIsInZhbHVlIjoibGJqa0l2RzMvU1hyTFRHbGFEY1l3K1R6U2hqWmo0eTkxNjNaWmxOUmJ1LzFsclQ5RStSODZFdUw0NFIyYVdKdWQxK1hrVkdXdnlsZG0rMHVGcklGdFdpNTFJOTZVTGNoYlY0aDVHTnI3TGpsYzJWNFRVWHRpZjBXdytzUFBxKzczVG5KQjJ3VTNLUTF1WHRGanhuVElpQUkvWWZHU3VXeVhHT24vcVNKenFPOGFyUjY0ZmFaU0FWNXUrbWM4MUljbjcwMHlDelRXeUk3aFRYUzhOdzV0L0s2WTFySjljL0NGc0dsRVQyR3NRbDh2UjV1djg2YUNiWGFpYTFSMlBqaHFwbm1DYTlCa3AzUWlLeEFTaXRSK0ZIM1JoTGpsQzU1dTBhSEtYdG9Dc250ZUZieXlTRGRDN2t6VEVzK1puT0dPWmJPdkNzcTdpOExzaWdVdDZ2OEVtelQrTDBOcUd3b1dmRWwxZlV1Uk5vbk91cjNSVkd5amRwMUx6NjR6cnVqYkpESlJpT2JxaS9tdWxvYnpPbVBPTThoN0p5c2Mxd09yQnl5eHZpSzBQOTRyM25tTDBnaUVCbWRtL05xcEtaMlhJQVRkMVVURm9MYzhra3QvNTJwSXRIRW1Za3ZsRldlRFlhWFYyUXltMGZEOS95YzhqenZaRzRtSGFObTFRZVYiLCJtYWMiOiJlNmNjYjg2ZGZmOGVmZTljOTYzODAyODlmM2U2ZTYxYTg0ZGQ2YjU5NjY2OTY1MTkxYTQyYjNlOTVjMDExODA1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1285458913\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1661524209 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">jEiKRXRaeQw7kpqtZmXNWu3bpmEnaxKAnNI8mVhc</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PSbLK6QAWuUzu3JpIF2TDtYQ1L7TOqCcejFUVX3V</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1661524209\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-882108013 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 16:02:05 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjZRN0N6KzNlbDdsRDVUdjJGZ0NoSnc9PSIsInZhbHVlIjoiZ203Qm9xZm5XbjJxcVNRNWE0QjltZUVMQXFLb0hDc2N2U1lpd01ZVHJ0OUExdUNETVIxZXlZVG93T2E2T0dNTDNOMWhlT3ZNV3poaFVhZDNDQ09iVlpuVWxWOFZ3ZmM5TmRnT3FrVDlnRmw4YkdQcDhKWWZIVTRYbm55Q1BNb1NrSEhmeDdGRlpqbnIxUFVkSnkrclNvUFFSV0FXTkRncFBHSlViVk41R1dVbHFManpKaUtNcU9ET2dPcjZibEpRdXpJSk1qREV0TkJ4RWdHYzlERnAwcDF4MGJHbkVmdFREbEVqc20rcXg3b3BwaDl6UENEMHBHQkFHSXEyQW1ja3RKa3VBeFE1SlIxZGRFdXEvUFJoai91UVQ2QnZWT2pkTktzZzZVMURCZVRGOFY1ZUI0QUNxUytIZ0xnVlljUFA5VDBLZkdpV2t3TzgwMmR0VHovcUc3TmcyQVQ0ZUI2UWp1aXdsazFXZFJ4b3ZJc2xSSGY0YzVBbGhPem5sc0dxS1UzYVh4aXZBL2YweVUzVjFMcUR6cVY4NW1JTFBIejFBdUtRWFhPUjlZL0tmb203ZEFaclVqVXhLZVdyelBVMjNyemR0QjFXcWkxTm8zQmpaSFNpL3ZRaGVmZ2hCMzF0bUJ5dXhnZnN6QjNxeFFTbnE4Mmg1UGRtcllKdTVUUzUiLCJtYWMiOiJiM2QzM2Y1YjM3MThiNjc4NzA1ZTk0YzRkNWIzY2M0MDI5MTkzMDNhYjY1NmI5NGRhMjczZTY0NjllMzBlNWE4IiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 18:02:05 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IktobU51a1RVVjZ3ZUF6T3hKMzgxQVE9PSIsInZhbHVlIjoiaDR2VkEyd1p0Wit0VHVORWdCdFk1VkFybzA5WExldVM4TUplR0hjRXBjVlFYaTVoR2dadmFUTzdIK0FoUngzSlBEaDFOWFJzUkhkeG5ibjYyRUxaVjFzWnBPMTNJU2NZMlBwWXFsL3V2UkhQUWF6SDBsUFczaUQ0MkczWklROFc5bHFVMFlNYmtKaEVNNmxBT1VlbzJReUFENUJVQzVqYTZMUi91UThOTkZPMzNxSWt1VzU0Ly9xZThDd1BiMzBmTE5peHlGVzJsK1U0ckNqU2xNWWVWd3UxY0FERGhZdHptRExTcU5ieVMyZTJnYXdkSkM4Nkk3dEt4cjNndEZVVTRRM3lGU0IreW8wdkFnNWZwRjJFWVV6MTQ3cFkvSDNsYjdGVVdHblU3c211YS8vNXkxTEJUcUN6ay9nWFVEWUxrOGJLZy9Ca3BTd2loRjlKc1ZtaTB0ZW51bGZlSTdQNUZ1M2pWNWU5bUJWbTNBaE5DaFR0MmIyeDlkZk1oU3I5aWFyMkxNZnJqSFdVOGVjdHZ1TzFvb1BWdDlYV1UxUmpUYkpaM2hMdWwvMEZrbUFzMGZ0R1B3bjBpZnhRbmtQQ0YwV1d0b3lKRE1QeXYvSis2Q05DNHFqcWJNOWwxNlVmeWpRd056OXN3dG51Z1JUZlhWQmxCcXlXUitLUU1zdmwiLCJtYWMiOiI1ZTNlMjNlYzY2OTUyZGFhMmYzMGJjOWZkNzNhMDA3MmViOTRjNDFiZGNiZWVjYjg4MmNiZWY3ZjM1NzU0MDYwIiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 18:02:05 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjZRN0N6KzNlbDdsRDVUdjJGZ0NoSnc9PSIsInZhbHVlIjoiZ203Qm9xZm5XbjJxcVNRNWE0QjltZUVMQXFLb0hDc2N2U1lpd01ZVHJ0OUExdUNETVIxZXlZVG93T2E2T0dNTDNOMWhlT3ZNV3poaFVhZDNDQ09iVlpuVWxWOFZ3ZmM5TmRnT3FrVDlnRmw4YkdQcDhKWWZIVTRYbm55Q1BNb1NrSEhmeDdGRlpqbnIxUFVkSnkrclNvUFFSV0FXTkRncFBHSlViVk41R1dVbHFManpKaUtNcU9ET2dPcjZibEpRdXpJSk1qREV0TkJ4RWdHYzlERnAwcDF4MGJHbkVmdFREbEVqc20rcXg3b3BwaDl6UENEMHBHQkFHSXEyQW1ja3RKa3VBeFE1SlIxZGRFdXEvUFJoai91UVQ2QnZWT2pkTktzZzZVMURCZVRGOFY1ZUI0QUNxUytIZ0xnVlljUFA5VDBLZkdpV2t3TzgwMmR0VHovcUc3TmcyQVQ0ZUI2UWp1aXdsazFXZFJ4b3ZJc2xSSGY0YzVBbGhPem5sc0dxS1UzYVh4aXZBL2YweVUzVjFMcUR6cVY4NW1JTFBIejFBdUtRWFhPUjlZL0tmb203ZEFaclVqVXhLZVdyelBVMjNyemR0QjFXcWkxTm8zQmpaSFNpL3ZRaGVmZ2hCMzF0bUJ5dXhnZnN6QjNxeFFTbnE4Mmg1UGRtcllKdTVUUzUiLCJtYWMiOiJiM2QzM2Y1YjM3MThiNjc4NzA1ZTk0YzRkNWIzY2M0MDI5MTkzMDNhYjY1NmI5NGRhMjczZTY0NjllMzBlNWE4IiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 18:02:05 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IktobU51a1RVVjZ3ZUF6T3hKMzgxQVE9PSIsInZhbHVlIjoiaDR2VkEyd1p0Wit0VHVORWdCdFk1VkFybzA5WExldVM4TUplR0hjRXBjVlFYaTVoR2dadmFUTzdIK0FoUngzSlBEaDFOWFJzUkhkeG5ibjYyRUxaVjFzWnBPMTNJU2NZMlBwWXFsL3V2UkhQUWF6SDBsUFczaUQ0MkczWklROFc5bHFVMFlNYmtKaEVNNmxBT1VlbzJReUFENUJVQzVqYTZMUi91UThOTkZPMzNxSWt1VzU0Ly9xZThDd1BiMzBmTE5peHlGVzJsK1U0ckNqU2xNWWVWd3UxY0FERGhZdHptRExTcU5ieVMyZTJnYXdkSkM4Nkk3dEt4cjNndEZVVTRRM3lGU0IreW8wdkFnNWZwRjJFWVV6MTQ3cFkvSDNsYjdGVVdHblU3c211YS8vNXkxTEJUcUN6ay9nWFVEWUxrOGJLZy9Ca3BTd2loRjlKc1ZtaTB0ZW51bGZlSTdQNUZ1M2pWNWU5bUJWbTNBaE5DaFR0MmIyeDlkZk1oU3I5aWFyMkxNZnJqSFdVOGVjdHZ1TzFvb1BWdDlYV1UxUmpUYkpaM2hMdWwvMEZrbUFzMGZ0R1B3bjBpZnhRbmtQQ0YwV1d0b3lKRE1QeXYvSis2Q05DNHFqcWJNOWwxNlVmeWpRd056OXN3dG51Z1JUZlhWQmxCcXlXUitLUU1zdmwiLCJtYWMiOiI1ZTNlMjNlYzY2OTUyZGFhMmYzMGJjOWZkNzNhMDA3MmViOTRjNDFiZGNiZWVjYjg4MmNiZWY3ZjM1NzU0MDYwIiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 18:02:05 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-882108013\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1619220416 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">jEiKRXRaeQw7kpqtZmXNWu3bpmEnaxKAnNI8mVhc</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>17</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1619220416\", {\"maxDepth\":0})</script>\n"}}