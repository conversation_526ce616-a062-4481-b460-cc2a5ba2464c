{"__meta": {"id": "X5aee85956a79db4a19f6168d3cafee7c", "datetime": "2025-06-26 16:02:01", "utime": **********.755351, "method": "GET", "uri": "/pos-financial-record/opening-balance", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 2, "messages": [{"message": "[16:02:01] LOG.info: Opening Balance Request Started {\n    \"user_id\": 17,\n    \"warehouse_id\": 9,\n    \"is_sale_session_new\": 1,\n    \"has_manage_pos_permission\": true\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.728352, "xdebug_link": null, "collector": "log"}, {"message": "[16:02:01] LOG.info: Returning opening balance view", "message_html": null, "is_string": false, "label": "info", "time": **********.729347, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.226761, "end": **********.755368, "duration": 0.5286068916320801, "duration_str": "529ms", "measures": [{"label": "Booting", "start": **********.226761, "relative_start": 0, "end": **********.570677, "relative_end": **********.570677, "duration": 0.3439159393310547, "duration_str": "344ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.570687, "relative_start": 0.34392595291137695, "end": **********.75537, "relative_end": 1.9073486328125e-06, "duration": 0.18468284606933594, "duration_str": "185ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 52690888, "peak_usage_str": "50MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x pos.financial_record.opening-balance", "param_count": null, "params": [], "start": **********.735006, "type": "blade", "hash": "bladeC:\\laragon\\www\\ERPcopy\\public_html\\resources\\views/pos/financial_record/opening-balance.blade.phppos.financial_record.opening-balance", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fresources%2Fviews%2Fpos%2Ffinancial_record%2Fopening-balance.blade.php&line=1", "ajax": false, "filename": "opening-balance.blade.php", "line": "?"}, "render_count": 1, "name_original": "pos.financial_record.opening-balance"}]}, "route": {"uri": "GET pos-financial-record/opening-balance", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\FinancialRecordController@opinningBalace", "namespace": null, "prefix": "", "where": [], "as": "pos.financial.opening.balance", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=262\" onclick=\"\">app/Http/Controllers/FinancialRecordController.php:262-323</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.0029799999999999996, "accumulated_duration_str": "2.98ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 17 limit 1", "type": "query", "params": [], "bindings": ["17"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.69971, "duration": 0.00149, "duration_str": "1.49ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 50}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.70877, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 50, "width_percent": 10.067}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 17 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["17", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.721967, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 60.067, "width_percent": 23.826}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (17) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\ERPcopy\\public_html\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.723907, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 83.893, "width_percent": 16.107}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FERPcopy%2Fpublic_html%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 2, "messages": [{"message": "[ability => manage pos, result => true, user => 17, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1576703312 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>17</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1576703312\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.727546, "xdebug_link": null}, {"message": "[ability => manage pos, result => true, user => 17, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-480065333 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>17</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-480065333\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.729255, "xdebug_link": null}]}, "session": {"_token": "jEiKRXRaeQw7kpqtZmXNWu3bpmEnaxKAnNI8mVhc", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "17"}, "request": {"path_info": "/pos-financial-record/opening-balance", "status_code": "<pre class=sf-dump id=sf-dump-1193904205 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1193904205\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-647851120 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-647851120\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-673939522 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-673939522\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1449539785 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1843 characters\">_clck=1lqc4d8%7C2%7Cfx3%7C0%7C2003; _clsk=1h4itwx%7C1750953718334%7C10%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjZDSnpGMTZ0QnJFUWJYVis4cXI0Wmc9PSIsInZhbHVlIjoiam9BZGQ5T3gvTXNYV3ljUUhXNWpHRkVZVUNEWk5QWHk2QlduRkhTWmg0R1F5STZWTUxZYXNlaXJyNHNENDNjWkpVeTd6bWsxV20rQjFneWVsbm8rQ0xhNUhoYkE5VEw3bHFDNi81N1ZzUjNVSzdPQllsWC94SDFHbU93amNiaC9MOVZ0dlA1cGczZnJML2tKN3I0c3RDdGhBZzFmUTRlakdHY2k0dTUxaUE4OE9LWnZBUjBSclJiMy8zdm8rbC9URlNWYzRBMzJDSVpoNVFvL1J0Y08wZ01HbjVyeDNQc3pSTUhYd01BSmRLRXlRbTA2Z2FGc2xCeklwd3hPbExhNmZsSFBOdWJzNEloNlBLcFRod2RZTVQ1bDF3aERseTFEK1hWMDI5NGplUGxjckRnYm9kN0YySTBnbGtFaW4wZ0lEMnhsb1NCamh6NG9hK0RtQ3oyTjZWN1daODVwTndVY3NKYXo2cFJ6RDltSFFUVThwYUFDa1ZyUzhaYzNQSXFlVTR5eUQwTGVmY3BYcWsxc1VNT2szU051UEE5TUhFdlNuOVZib0lNa0llSC9lR1dSN2ZQL2NuVmkxVzg2OGtDSi94ME9VbnFING5WUHBudjhUNzhBSS9ERjVRaDdCcGtqcHZjTXpZWktpVjJaUncxaldiemdBWXVuTlZNL2NwSFciLCJtYWMiOiJkMzlmYzQ0YmUzNWNhOWRhOTA2ZTFiZjEzMTA4NThkMDUzNGU3N2U2YTc3NGU1N2UzN2Y4MTk4MjM0MTU2OGU2IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjA3VlVCUGNHcE81Qkh1V1BTT0tKeXc9PSIsInZhbHVlIjoiUU5ZQkxpWTVUV0taTDJITnNkRVMyTXRteERYNjdZUTlhVmR5QlZVYXBCWkduOVdueHlTSEpUQTJ2KzZWWVE1c1pBeEl3UEovdWJsREZ1WjFJUUpCUUJjVmJkWkVMRmNoRno2cWdna1IwUnhDV2xwVUx5OE5HZUw1RHhiWU53OTFRSEkwZis3QXNrK3M1SHBUN2FJZTl3TzZlb2pxbmZ5RmhWR1grRk43SUhpUVROQkcrWmE3b1NrTHN1alUwcXNiNEFyVzE3NFV5R1duTnBwa0xDOVVMdndUQnZHYk5TN3lJZUtqcVJRMTRJRENGRDJRWFlmK0JUem1MeThGamRiN3IrR0xOU2h0a3lSWnlaWEQyeTZkYU5BL1VSQTBGMy83ZTZhTVJnRS8xaGJ4bThNRU14WDZESzBsWjFkWnMxcmRtb3ZOUjFOSmhYbnZ4ckdVQWFLbzBLWWcvcmE3WHF2S3pyeEQ4dE1WWGNxc1JtSGxkU0FrdUxXcWx1d0J5RG5kTzVSM3hPYzB0WWN3RkNRZWNkOTFsRFJGNStzWmxVSXQ5TlRmRnR6SHJheWF5bmtLeWxmdG1HbjBTRmlvZjhycUtRWlVrMnN4cFRtd1F2WG1RMHJnWnB2RGdoOG1IRXcvVjI2ekR0MndEQmpzSUZUUkNUS1JoRUU4TFJiUm4rNysiLCJtYWMiOiIzMDgzMWVlYWI1NzBhMTUyYWM1ZGFmMjE0NDExOGI0ZTkyMzkwZTZiZTJlNDVjMGNhNDFhY2ExMDBjMGU2YmE5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1449539785\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-11455135 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">jEiKRXRaeQw7kpqtZmXNWu3bpmEnaxKAnNI8mVhc</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PSbLK6QAWuUzu3JpIF2TDtYQ1L7TOqCcejFUVX3V</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-11455135\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-221835111 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 16:02:01 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlF0S25xeHFLRnhJVzNTb3RiVGdrTlE9PSIsInZhbHVlIjoib1l4b1IxT28wUXNiZGxuT0N0SGdYc3V4VDNDN0FESzlkOGNqSjh0cHMvOHFITnd2WFVxWWZoNURLWmFDblJvdmpRK0crMUY4SWh6R1ZCelZBNWRwWDVKTnNtdzJkZDhzTy9abUg3MEh6dFBsNE0xYUJRd0JXeUJXU3g1Q1lPSnRLVFgrZ1J3ZHFjSDliOVkyUDA3MG0zdjV4azNzRmRRN0d3OWNzeWVURFFpamJUMDd5T0YzVTVXTEk2dFVyUjdrVCsyVzVibkxjQldVcExYZjZORVJLS0c3RjZXaTNlcUdWdWhiYUl0VURTaWNabkVrL0tUR0RZTkhGVFNaTnpaMEE3azUyMi8vclJBeDVnNnpTM2p4dVVLdXJXb25RMzJTWEFxbzlhK2NEcTdZSm9CZEgyY2dBZ2g5Zk1LV0lMdWd1cnhaNytqMFNqK2hhNG0zTDVFY3NGelJuTGwxTmdtYnd5YVFab2Nqa2NJb0NjeHdHTmh6OE0yMVBYZ21OUktmdFQ4V0NRTU5DTTZjSER4THQzak9vQk1VZ2tTUkN2ZjBrMWx2NW5Xc0NMRjZmSndER3VpM1JyQm5qWHhQbzd6WjdyODZIV0tSRXJZV1pnejF5L2FZRFg2bERZUkJOZ0NBc0lVWXpMR3RIZzBkTG1lTkVlaGtkNk43WCtBNWlXTWwiLCJtYWMiOiJjOTJhZWQ1OWI5ZjE4ZTYxNzNhNWZlM2IwODQwYWViMTRmMDAyZGE1YzE4NmQ5ZDkwYzNiZjEwNjFhYjJlNGVkIiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 18:02:01 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Im1xb2ZoUHMzVkFaRVRsM0pJMXltRVE9PSIsInZhbHVlIjoiQ2pqWDlZT2pVSGFDVzBES3BBTTRVM0VLTlVJbm1xVTN0Y0ZqdzRWZFBHMHFWWC9kVkF5TlUvbnQzcHYyOENPbnFlL0ZQMDM5Q2RRaU45dnNoSTJzV1FDNVJ4RFBvZ2REbnllbWVTbmRvcEcyUHpjcXR5TWpicmIxUXN4SEpPYmpUakhBQWZnMjlhYmdPT1hpSE1LeFNOL1FZeG05WCtxVXVtcW1Rb2xJY0VJOEF4WmhQczF4MGVzKzlRSUxnTk01dUNDVE1jQW53Y2NORHJia1NRWlV6WXBFd2psaVpGYVFXNFR1aVFkY3JjWUUwZzZEeGRFTGV1ZkltdGFwcjJaQXFTcndSeW5WUStkTmMydEp6YlhaS05NK1pXWXhJVGpRVkpRaTNZaGk4OXRlbFpxRklLMmdYb1FQSytVQmhFNjFBT2lZZmcraVB3dGtZeVorNjNaQWxFSVVGY0JlZGtGbjhGQUxabE5ucEtiOVZvclloNGszN29vbDhudUVzeDdyYXptZDlJekFyKzFuZk9WOW9YZysxYms3TEFPVE16SlJFcTFyaVI0VUVPd3lYbThHcWIyNHhJN1RmRXpadlIwRXVQQXBPYldweVJzeEs5eVFSeTlFdFlqSnFvQkszdXFDMUpzcFN4bnBJTUQwbS9UTVJFeTExRVA4OHZ6N240RjQiLCJtYWMiOiIxYWE1MzM1NWVmNjUzOWEzOTMxMzhmNDI0NTM3YmNlODNjOTQ5ZWJiYmIyYjY3NWM0MDY1NWJkYjNjZGM2ZTIxIiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 18:02:01 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlF0S25xeHFLRnhJVzNTb3RiVGdrTlE9PSIsInZhbHVlIjoib1l4b1IxT28wUXNiZGxuT0N0SGdYc3V4VDNDN0FESzlkOGNqSjh0cHMvOHFITnd2WFVxWWZoNURLWmFDblJvdmpRK0crMUY4SWh6R1ZCelZBNWRwWDVKTnNtdzJkZDhzTy9abUg3MEh6dFBsNE0xYUJRd0JXeUJXU3g1Q1lPSnRLVFgrZ1J3ZHFjSDliOVkyUDA3MG0zdjV4azNzRmRRN0d3OWNzeWVURFFpamJUMDd5T0YzVTVXTEk2dFVyUjdrVCsyVzVibkxjQldVcExYZjZORVJLS0c3RjZXaTNlcUdWdWhiYUl0VURTaWNabkVrL0tUR0RZTkhGVFNaTnpaMEE3azUyMi8vclJBeDVnNnpTM2p4dVVLdXJXb25RMzJTWEFxbzlhK2NEcTdZSm9CZEgyY2dBZ2g5Zk1LV0lMdWd1cnhaNytqMFNqK2hhNG0zTDVFY3NGelJuTGwxTmdtYnd5YVFab2Nqa2NJb0NjeHdHTmh6OE0yMVBYZ21OUktmdFQ4V0NRTU5DTTZjSER4THQzak9vQk1VZ2tTUkN2ZjBrMWx2NW5Xc0NMRjZmSndER3VpM1JyQm5qWHhQbzd6WjdyODZIV0tSRXJZV1pnejF5L2FZRFg2bERZUkJOZ0NBc0lVWXpMR3RIZzBkTG1lTkVlaGtkNk43WCtBNWlXTWwiLCJtYWMiOiJjOTJhZWQ1OWI5ZjE4ZTYxNzNhNWZlM2IwODQwYWViMTRmMDAyZGE1YzE4NmQ5ZDkwYzNiZjEwNjFhYjJlNGVkIiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 18:02:01 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Im1xb2ZoUHMzVkFaRVRsM0pJMXltRVE9PSIsInZhbHVlIjoiQ2pqWDlZT2pVSGFDVzBES3BBTTRVM0VLTlVJbm1xVTN0Y0ZqdzRWZFBHMHFWWC9kVkF5TlUvbnQzcHYyOENPbnFlL0ZQMDM5Q2RRaU45dnNoSTJzV1FDNVJ4RFBvZ2REbnllbWVTbmRvcEcyUHpjcXR5TWpicmIxUXN4SEpPYmpUakhBQWZnMjlhYmdPT1hpSE1LeFNOL1FZeG05WCtxVXVtcW1Rb2xJY0VJOEF4WmhQczF4MGVzKzlRSUxnTk01dUNDVE1jQW53Y2NORHJia1NRWlV6WXBFd2psaVpGYVFXNFR1aVFkY3JjWUUwZzZEeGRFTGV1ZkltdGFwcjJaQXFTcndSeW5WUStkTmMydEp6YlhaS05NK1pXWXhJVGpRVkpRaTNZaGk4OXRlbFpxRklLMmdYb1FQSytVQmhFNjFBT2lZZmcraVB3dGtZeVorNjNaQWxFSVVGY0JlZGtGbjhGQUxabE5ucEtiOVZvclloNGszN29vbDhudUVzeDdyYXptZDlJekFyKzFuZk9WOW9YZysxYms3TEFPVE16SlJFcTFyaVI0VUVPd3lYbThHcWIyNHhJN1RmRXpadlIwRXVQQXBPYldweVJzeEs5eVFSeTlFdFlqSnFvQkszdXFDMUpzcFN4bnBJTUQwbS9UTVJFeTExRVA4OHZ6N240RjQiLCJtYWMiOiIxYWE1MzM1NWVmNjUzOWEzOTMxMzhmNDI0NTM3YmNlODNjOTQ5ZWJiYmIyYjY3NWM0MDY1NWJkYjNjZGM2ZTIxIiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 18:02:01 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-221835111\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">jEiKRXRaeQw7kpqtZmXNWu3bpmEnaxKAnNI8mVhc</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>17</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}