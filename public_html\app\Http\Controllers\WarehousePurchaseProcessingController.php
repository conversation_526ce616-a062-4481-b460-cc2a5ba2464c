<?php

namespace App\Http\Controllers;

use App\Models\Purchase;
use App\Models\PurchaseProduct;
use App\Models\ProductService;
use App\Models\Vender;
use App\Models\Warehouse;
use App\Models\WarehouseProduct;
use App\Models\ProductServiceCategory;
use App\Models\Utility;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;

class WarehousePurchaseProcessingController extends Controller
{
    /**
     * عرض قائمة فواتير المشتريات للمستودع
     */
    public function index()
    {
        // إزالة التحقق من الصلاحيات - الصفحة متاحة لجميع المستخدمين

        // استرجاع جميع فواتير المشتريات مع العلاقات
        $purchases = Purchase::where('created_by', '=', \Auth::user()->creatorId())
            ->with(['vender', 'warehouse', 'items', 'payments'])
            ->orderBy('id', 'desc')
            ->get();

        // حساب المجاميع لكل فاتورة
        foreach ($purchases as $purchase) {
            $purchase->total_amount = $purchase->getTotal();
            $purchase->paid_amount = $this->getPayments($purchase);
            $purchase->due_amount = $purchase->getDue();
            $purchase->status_text = $this->getStatusText($purchase->status);
        }

        // حساب الإحصائيات
        $statistics = $this->calculateStatistics($purchases);

        return view('warehouse_purchase_processing.index', compact('purchases', 'statistics'));
    }

    /**
     * حساب إجمالي المدفوعات للفاتورة
     */
    private function getPayments($purchase)
    {
        $totalPayments = 0;
        foreach($purchase->payments as $payment)
        {
            $totalPayments += $payment->amount;
        }
        return $totalPayments;
    }

    /**
     * حساب الإحصائيات
     */
    private function calculateStatistics($purchases)
    {
        $statistics = [
            'total_purchases' => $purchases->count(),
            'total_amount' => 0,
            'total_paid' => 0,
            'total_due' => 0,
            'paid_count' => 0,
            'unpaid_count' => 0,
            'partially_paid_count' => 0
        ];

        foreach ($purchases as $purchase) {
            $statistics['total_amount'] += $purchase->total_amount;
            $statistics['total_paid'] += $purchase->paid_amount;
            $statistics['total_due'] += $purchase->due_amount;

            if ($purchase->status == 4) {
                $statistics['paid_count']++;
            } elseif ($purchase->status == 2) {
                $statistics['unpaid_count']++;
            } elseif ($purchase->status == 3) {
                $statistics['partially_paid_count']++;
            }
        }

        return $statistics;
    }

    /**
     * الحصول على نص الحالة
     */
    private function getStatusText($status)
    {
        $statuses = [
            0 => 'Draft',
            1 => 'Sent',
            2 => 'Unpaid',
            3 => 'Partially Paid',
            4 => 'Paid'
        ];

        return $statuses[$status] ?? 'Unknown';
    }

    /**
     * تحديث بيانات الفاتورة مباشرة (Inline Update)
     */
    public function updateInline(Request $request)
    {
        // إزالة التحقق من الصلاحيات - متاح لجميع المستخدمين

        $validator = Validator::make($request->all(), [
            'purchase_id' => 'required|integer|exists:purchases,id',
            'field' => 'required|string|in:vender_id,warehouse_id,purchase_date,order_number,status',
            'value' => 'required'
        ]);

        if ($validator->fails()) {
            return response()->json(['success' => false, 'message' => $validator->errors()->first()], 400);
        }

        try {
            $purchase = Purchase::where('id', $request->purchase_id)
                ->where('created_by', \Auth::user()->creatorId())
                ->first();

            if (!$purchase) {
                return response()->json(['success' => false, 'message' => __('Purchase not found.')], 404);
            }

            // التحقق من صحة البيانات حسب نوع الحقل
            $value = $request->value;
            switch ($request->field) {
                case 'vender_id':
                    $vender = Vender::where('id', $value)->where('created_by', \Auth::user()->creatorId())->first();
                    if (!$vender) {
                        return response()->json(['success' => false, 'message' => __('Invalid vendor.')], 400);
                    }
                    break;

                case 'warehouse_id':
                    $warehouse = Warehouse::where('id', $value)->where('created_by', \Auth::user()->creatorId())->first();
                    if (!$warehouse) {
                        return response()->json(['success' => false, 'message' => __('Invalid warehouse.')], 400);
                    }
                    break;

                case 'purchase_date':
                    if (!strtotime($value)) {
                        return response()->json(['success' => false, 'message' => __('Invalid date format.')], 400);
                    }
                    break;

                case 'order_number':
                    if (!is_numeric($value)) {
                        return response()->json(['success' => false, 'message' => __('Order number must be numeric.')], 400);
                    }
                    break;

                case 'status':
                    if (!in_array($value, [0, 1, 2, 3, 4])) {
                        return response()->json(['success' => false, 'message' => __('Invalid status.')], 400);
                    }
                    break;
            }

            // تحديث البيانات
            $purchase->{$request->field} = $value;
            $purchase->save();

            // إرجاع البيانات المحدثة
            $displayValue = $this->getDisplayValue($purchase, $request->field);

            return response()->json([
                'success' => true,
                'message' => __('Purchase updated successfully.'),
                'display_value' => $displayValue
            ]);

        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => __('An error occurred while updating.')], 500);
        }
    }

    /**
     * الحصول على القيمة المعروضة للحقل
     */
    private function getDisplayValue($purchase, $field)
    {
        switch ($field) {
            case 'vender_id':
                return $purchase->vender ? $purchase->vender->name : '-';

            case 'warehouse_id':
                return $purchase->warehouse ? $purchase->warehouse->name : '-';

            case 'purchase_date':
                return \Auth::user()->dateFormat($purchase->purchase_date);

            case 'order_number':
                return $purchase->order_number;

            case 'status':
                return $this->getStatusBadge($purchase->status);

            default:
                return $purchase->{$field};
        }
    }

    /**
     * الحصول على شارة الحالة
     */
    private function getStatusBadge($status)
    {
        $badges = [
            0 => '<span class="badge bg-secondary">' . __('Draft') . '</span>',
            1 => '<span class="badge bg-info">' . __('Sent') . '</span>',
            2 => '<span class="badge bg-danger">' . __('Unpaid') . '</span>',
            3 => '<span class="badge bg-warning">' . __('Partially Paid') . '</span>',
            4 => '<span class="badge bg-success">' . __('Paid') . '</span>'
        ];

        return $badges[$status] ?? '<span class="badge bg-secondary">Unknown</span>';
    }

    /**
     * الحصول على خيارات الحقول للتعديل
     */
    public function getFieldOptions(Request $request)
    {
        // إزالة التحقق من الصلاحيات - متاح لجميع المستخدمين

        $field = $request->get('field');
        $options = [];

        switch ($field) {
            case 'vender_id':
                $venders = Vender::where('created_by', \Auth::user()->creatorId())->get();
                foreach ($venders as $vender) {
                    $options[] = ['value' => $vender->id, 'text' => $vender->name];
                }
                break;

            case 'warehouse_id':
                $warehouses = Warehouse::where('created_by', \Auth::user()->creatorId())->get();
                foreach ($warehouses as $warehouse) {
                    $options[] = ['value' => $warehouse->id, 'text' => $warehouse->name];
                }
                break;

            case 'status':
                $statuses = [
                    0 => __('Draft'),
                    1 => __('Sent'),
                    2 => __('Unpaid'),
                    3 => __('Partially Paid'),
                    4 => __('Paid')
                ];
                foreach ($statuses as $value => $text) {
                    $options[] = ['value' => $value, 'text' => $text];
                }
                break;
        }

        return response()->json(['success' => true, 'options' => $options]);
    }

    /**
     * عرض صفحة تعديل منتجات الفاتورة
     */
    public function editProducts($id)
    {
        try {
            $purchase = Purchase::where('id', $id)
                ->where('created_by', \Auth::user()->creatorId())
                ->with(['vender', 'warehouse', 'items.product'])
                ->first();

            if (!$purchase) {
                return redirect()->back()->with('error', __('Purchase not found.'));
            }

            // جلب جميع المنتجات المتاحة
            $products = ProductService::where('created_by', \Auth::user()->creatorId())
                ->where('type', 'product')
                ->get();

            // جلب جميع الموردين المتاحين
            $venders = Vender::where('created_by', \Auth::user()->creatorId())->get();

            return view('warehouse_purchase_processing.edit_products', compact('purchase', 'products', 'venders'));

        } catch (\Exception $e) {
            return redirect()->back()->with('error', __('An error occurred while loading the page.'));
        }
    }

    /**
     * تحديث منتج في الفاتورة
     */
    public function updateProduct(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'purchase_product_id' => 'required|integer|exists:purchase_products,id',
            'field' => 'required|string|in:product_id,quantity,price,tax,discount',
            'value' => 'required'
        ]);

        if ($validator->fails()) {
            return response()->json(['success' => false, 'message' => $validator->errors()->first()], 400);
        }

        try {
            $purchaseProduct = PurchaseProduct::where('id', $request->purchase_product_id)
                ->whereHas('purchase', function($query) {
                    $query->where('created_by', \Auth::user()->creatorId());
                })
                ->first();

            if (!$purchaseProduct) {
                return response()->json(['success' => false, 'message' => __('Product not found.')], 404);
            }

            // التحقق من صحة البيانات حسب نوع الحقل
            $value = $request->value;
            switch ($request->field) {
                case 'product_id':
                    $product = ProductService::where('id', $value)
                        ->where('created_by', \Auth::user()->creatorId())
                        ->first();
                    if (!$product) {
                        return response()->json(['success' => false, 'message' => __('Invalid product.')], 400);
                    }
                    break;

                case 'quantity':
                    if (!is_numeric($value) || $value <= 0) {
                        return response()->json(['success' => false, 'message' => __('Quantity must be a positive number.')], 400);
                    }
                    break;

                case 'price':
                    if (!is_numeric($value) || $value < 0) {
                        return response()->json(['success' => false, 'message' => __('Price must be a valid number.')], 400);
                    }
                    break;

                case 'tax':
                    if (!is_numeric($value) || $value < 0 || $value > 100) {
                        return response()->json(['success' => false, 'message' => __('Tax must be between 0 and 100.')], 400);
                    }
                    break;

                case 'discount':
                    if (!is_numeric($value) || $value < 0) {
                        return response()->json(['success' => false, 'message' => __('Discount must be a positive number.')], 400);
                    }
                    break;
            }

            // تحديث البيانات
            $purchaseProduct->{$request->field} = $value;
            $purchaseProduct->save();

            // إرجاع البيانات المحدثة
            $displayValue = $this->getProductDisplayValue($purchaseProduct, $request->field);

            return response()->json([
                'success' => true,
                'message' => __('Product updated successfully.'),
                'display_value' => $displayValue,
                'total' => $purchaseProduct->quantity * $purchaseProduct->price
            ]);

        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => __('An error occurred while updating.')], 500);
        }
    }

    /**
     * إضافة منتج جديد للفاتورة
     */
    public function addProduct(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'purchase_id' => 'required|integer|exists:purchases,id',
            'product_id' => 'required|integer|exists:product_services,id',
            'quantity' => 'required|numeric|min:1',
            'price' => 'required|numeric|min:0',
            'tax' => 'nullable|numeric|min:0|max:100',
            'discount' => 'nullable|numeric|min:0'
        ]);

        if ($validator->fails()) {
            return response()->json(['success' => false, 'message' => $validator->errors()->first()], 400);
        }

        try {
            // التحقق من ملكية الفاتورة
            $purchase = Purchase::where('id', $request->purchase_id)
                ->where('created_by', \Auth::user()->creatorId())
                ->first();

            if (!$purchase) {
                return response()->json(['success' => false, 'message' => __('Purchase not found.')], 404);
            }

            // التحقق من المنتج
            $product = ProductService::where('id', $request->product_id)
                ->where('created_by', \Auth::user()->creatorId())
                ->first();

            if (!$product) {
                return response()->json(['success' => false, 'message' => __('Product not found.')], 404);
            }

            // إنشاء منتج جديد في الفاتورة
            $purchaseProduct = new PurchaseProduct();
            $purchaseProduct->purchase_id = $request->purchase_id;
            $purchaseProduct->product_id = $request->product_id;
            $purchaseProduct->quantity = $request->quantity;
            $purchaseProduct->price = $request->price;
            $purchaseProduct->tax = $request->tax ?? 0;
            $purchaseProduct->discount = $request->discount ?? 0;
            $purchaseProduct->save();

            return response()->json([
                'success' => true,
                'message' => __('Product added successfully.'),
                'product' => $purchaseProduct->load('product')
            ]);

        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => __('An error occurred while adding product.')], 500);
        }
    }

    /**
     * حذف منتج من الفاتورة
     */
    public function deleteProduct(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'purchase_product_id' => 'required|integer|exists:purchase_products,id'
        ]);

        if ($validator->fails()) {
            return response()->json(['success' => false, 'message' => $validator->errors()->first()], 400);
        }

        try {
            $purchaseProduct = PurchaseProduct::where('id', $request->purchase_product_id)
                ->whereHas('purchase', function($query) {
                    $query->where('created_by', \Auth::user()->creatorId());
                })
                ->first();

            if (!$purchaseProduct) {
                return response()->json(['success' => false, 'message' => __('Product not found.')], 404);
            }

            $purchaseProduct->delete();

            return response()->json([
                'success' => true,
                'message' => __('Product deleted successfully.')
            ]);

        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => __('An error occurred while deleting product.')], 500);
        }
    }

    /**
     * تحديث جميع منتجات الفاتورة مع تحديث المخزون
     */
    public function updateProducts(Request $request, $id)
    {
        try {
            DB::beginTransaction();

            $purchase = Purchase::where('created_by', '=', \Auth::user()->creatorId())->findOrFail($id);

            // التحقق من صحة البيانات
            $validator = Validator::make($request->all(), [
                'purchase_date' => 'required|date',
                'vender_id' => 'required|integer|exists:venders,id',
                'status' => 'required|integer|in:0,1,2,3,4',
                'products' => 'required|array|min:1',
                'products.*.product_id' => 'required|integer|exists:product_services,id',
                'products.*.quantity' => 'required|numeric|min:1',
                'products.*.price' => 'required|numeric|min:0',
                'products.*.tax' => 'nullable|numeric|min:0|max:100',
                'products.*.discount' => 'nullable|numeric|min:0',
                'products.*.description' => 'nullable|string|max:255'
            ]);

            if ($validator->fails()) {
                DB::rollBack();
                return redirect()->back()->withErrors($validator)->withInput();
            }

            // حفظ المنتجات القديمة لتحديث المخزون
            $oldProducts = $purchase->items()->get();

            // تحديث بيانات الفاتورة الأساسية
            $purchase->purchase_date = $request->purchase_date;
            $purchase->vender_id = $request->vender_id;
            $purchase->status = $request->status;
            $purchase->save();

            // إزالة المنتجات القديمة من المخزون (عكس عملية الشراء)
            foreach ($oldProducts as $oldProduct) {
                $this->updateInventoryStock(
                    $purchase->warehouse_id,
                    $oldProduct->product_id,
                    $oldProduct->quantity,
                    'minus' // إزالة الكمية من المخزون
                );
            }

            // حذف جميع المنتجات الحالية
            $purchase->items()->delete();

            // إضافة المنتجات الجديدة وتحديث المخزون
            foreach ($request->products as $productData) {
                if (!empty($productData['product_id'])) {
                    $purchaseProduct = new PurchaseProduct();
                    $purchaseProduct->purchase_id = $purchase->id;
                    $purchaseProduct->product_id = $productData['product_id'];
                    $purchaseProduct->quantity = $productData['quantity'];
                    $purchaseProduct->price = $productData['price'];
                    $purchaseProduct->tax = $productData['tax'] ?? 0;
                    $purchaseProduct->discount = $productData['discount'] ?? 0;
                    $purchaseProduct->description = $productData['description'] ?? '';
                    $purchaseProduct->save();

                    // إضافة المنتج الجديد للمخزون
                    $this->updateInventoryStock(
                        $purchase->warehouse_id,
                        $productData['product_id'],
                        $productData['quantity'],
                        'plus' // إضافة الكمية للمخزون
                    );
                }
            }

            DB::commit();

            return redirect()->route('warehouse.purchase.processing.edit.products', $id)
                ->with('success', __('تم تحديث الفاتورة ومنتجاتها والمخزون بنجاح.'));

        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()
                ->with('error', __('حدث خطأ أثناء تحديث الفاتورة: ') . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * تحديث كمية المنتج في المخزون
     */
    private function updateInventoryStock($warehouse_id, $product_id, $quantity, $type)
    {
        try {
            // التحقق من وجود المنتج قبل تحديث المخزون
            $product = ProductService::find($product_id);
            if (!$product) {
                \Log::error('Product not found when updating inventory stock', [
                    'product_id' => $product_id,
                    'warehouse_id' => $warehouse_id,
                    'quantity' => $quantity,
                    'type' => $type
                ]);
                return; // تخطي التحديث إذا كان المنتج غير موجود
            }

            // استخدام وظيفة Utility الموجودة لتحديث المخزون
            Utility::warehouse_quantity($type, $quantity, $product_id, $warehouse_id);

            // إضافة سجل في تقرير المخزون
            $description = $type == 'plus'
                ? 'إضافة من فاتورة شراء'
                : 'إزالة من تعديل فاتورة شراء';

            Utility::addProductStock(
                $product_id,
                $quantity,
                $type == 'plus' ? 'Purchase' : 'Purchase Edit',
                $description,
                0 // type_id - يمكن تحديثه حسب الحاجة
            );

        } catch (\Exception $e) {
            \Log::error('خطأ في تحديث المخزون: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * الحصول على القيمة المعروضة لحقل المنتج
     */
    private function getProductDisplayValue($purchaseProduct, $field)
    {
        switch ($field) {
            case 'product_id':
                return $purchaseProduct->product ? $purchaseProduct->product->name : '-';

            case 'quantity':
                return number_format($purchaseProduct->quantity, 0);

            case 'price':
                return \Auth::user()->priceFormat($purchaseProduct->price);

            case 'tax':
                return $purchaseProduct->tax . '%';

            case 'discount':
                return \Auth::user()->priceFormat($purchaseProduct->discount);

            default:
                return $purchaseProduct->{$field};
        }
    }
}
